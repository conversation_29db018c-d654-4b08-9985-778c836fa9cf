# Arcoa Nexus Diagnostics — Bootable USB Creation Guide

This guide shows **every** step — from downloading Puppy Linux through flashing the final ISO — so that even a Linux beginner can create a self-booting USB stick that runs the Arcoa Nexus Diagnostics GUI on any laptop, even one with *no* operating system.

---

## 0. What You Need

| Item | Notes |
|------|-------|
| **BookwormPup64 ISO** | Download from the official Puppy Linux mirrors (≈ 415 MB) |
| **USB flash drive** | 4 GB+ recommended; contents will be destroyed |
| **Working PC** | Windows, macOS, or Linux — used to build & flash the ISO |
| **ArcoaEPS project folder** | The folder that contains `agent/`, `requirements.txt`, etc. |

---

## 1. Prepare a Puppy Linux “Builder” VM (or spare PC)

> Why a VM?  It keeps your main OS clean and lets you take snapshots.

1. Install [VirtualBox](https://www.virtualbox.org/) (Windows/macOS/Linux).
2. Create a new VM:
   * **Name:** `puppy-builder`
   * **Type:** `Linux`, **Version:** `Other Linux (64-bit)`
   * **Memory:** 2 GB (2048 MB) is plenty.
   * **Hard-disk:** 8 GB dynamically allocated VDI.
3. Attach the **BookwormPup64 ISO** as the optical drive.
4. Boot the VM; Puppy loads fully into RAM within seconds.

---

## 2. Switch Puppy to “Persistence”

When you shut Puppy down it will ask **“Do you want to save your session?”**

1. Choose **`Save`**.
2. Select **`folder`** (not *file*) if the VM’s virtual disk is ext4; otherwise choose *savefile*.
3. Name it `puppy-save` and pick the default size (512 MB is fine).
4. Reboot once so the save folder is used automatically.

---

## 3. Load Development Tools (devx) & Required Packages

Puppy ships with a modular SFS containing compilers and Python headers.

1. Menu ▸ **Setup ▸ SFS-Load**.
2. In the list select `devx-bookwormpup64.sfs` and click **Load**.
3. Open a terminal (black monitor icon).
4. Install runtime packages via **PPM**:

   ```bash
   # Click the orange puppy icon ▸ Setup ▸ Puppy Package Manager (PPM)
   # Search each package and click 'Install':
   python3
   python3-tk
   python3-pip
   coreutils        # provides shredding tools
   git              # optional, for pulling code
   ```

---

## 4. Place the Arcoa Application Under `/opt`

There are two easy ways:

### 4A. Drag-and-Drop from Host (VirtualBox Guest Additions)

1. In VirtualBox: Devices ▸ Shared Folders ▸ **+** ➜ Choose your `ArcoaEPS` host folder and tick *Auto-mount* + *Make Permanent*.
2. Puppy auto-mounts it at `/mnt/shared/ArcoaEPS`.
3. Copy it:

   ```bash
   mkdir -p /opt/arcoa
   cp -r /mnt/shared/ArcoaEPS/* /opt/arcoa
   ```

### 4B. Clone via Git (if you pushed to a repo)

```bash
mkdir -p /opt
cd /opt
git clone https://github.com/yourorg/ArcoaEPS arcoa
```

---

## 5. Vendor Python Dependencies Locally

We don’t want to rely on PyPI at runtime, so we **vendor** wheels into an internal folder.

```bash
python3 -m pip install --no-cache-dir --target /opt/arcoa/deps psutil httpx
```

*Inside* `/opt/arcoa/agent/__init__.py` (top of file) add:

```python
import sys, os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "deps"))
```

This makes `import psutil` etc. resolve from the vendored directory.

---

## 6. Auto-start the GUI at Boot

Puppy executes any script placed in `~/Startup` after X starts.

```bash
cat >/opt/arcoa/arcoa.sh <<'EOF'
#!/bin/sh
cd /opt/arcoa
exec python3 agent/main.py
EOF
chmod +x /opt/arcoa/arcoa.sh
ln -s /opt/arcoa/arcoa.sh ~/Startup/arcoa.sh
```

Reboot **inside** Puppy; the Arcoa Nexus window should appear automatically.

---

## 7. Remaster Puppy into a New ISO

1. Menu ▸ **Setup ▸ Remaster Puppy live-CD**
2. Choose **Easy/official** remaster.
3. For *“Choose a working directory”* pick `/mnt/home/<USER>
4. At the *“EXTRA”* prompt, verify that `/opt/arcoa` is ticked — it ensures your app lands in the new SFS.
5. Finish; the wizard writes something like `bookwormpup64-arcoa.iso` into `/mnt/home`.

*(Optional)* Test the ISO quickly:

```bash
qemu-system-x86_64 -m 2048 -cdrom /mnt/home/<USER>
```

---

## 8. Flash the ISO to a USB Stick

> **Warning:** The stick will be completely overwritten.

### 8A. On Windows (Rufus)

1. Download Rufus portable.
2. Insert the USB (4 GB+).
3. Rufus ▸ **Device:** *your USB* ▸ **Boot selection:** *bookwormpup64-arcoa.iso*.
4. When prompted, pick **`DD` mode** (not ISO-file copy).
5. Click **Start** ➜ wait ≈ 2 min.

### 8B. On Linux/macOS

```bash
sudo dd if=bookwormpup64-arcoa.iso of=/dev/sdX bs=4M status=progress && sync
```
Replace `sdX` with your stick (check via `lsblk`).

---

## 9. Boot a Target Laptop

1. Plug the USB.
2. Power on and press the Boot-menu key (Esc / F9 / F12 / etc.).
3. Select the USB.  On UEFI machines you may need to disable Secure Boot.
4. Puppy loads entirely into RAM (≈ 20 s).  Arcoa Nexus starts automatically.

**Persisting Logs**: Puppy creates a `puppy-save` file on the stick the first time you shut down.  Any logs you write under `/root/` (e.g. `/root/ArcoaLogs/`) will be stored there so you can review them on another PC.

---

## 10. Troubleshooting

| Symptom | Fix |
|---------|-----|
| *Arcoa window doesn’t appear* | Verify `arcoa.sh` is executable and symlinked into `~/Startup`.  Run it manually in a terminal for errors. |
| *python3: Module psutil not found* | Re-run the `pip install --target /opt/arcoa/deps …` step and ensure `sys.path.insert…` code is in place. |
| *Can’t find shred* | Install **coreutils** via PPM, or rely on the `dd` fallback already in `drive_wipe_test.py`. |
| *No Wi-Fi* | Many Puppy Wi-Fi drivers are in separate firmware SFS.  Menu ▸ Setup ▸ SFS-Load ▸ load the matching firmware-*.sfs. |

---

## 11. Next Steps / Advanced

* **Branding**: Replace `/usr/share/backgrounds/default.jpg` and the Puppy splash to show your company logo.
* **Multi-ISO USB**: Use [Ventoy](https://www.ventoy.net/) and copy `bookwormpup64-arcoa.iso` onto it; Ventoy’s menu lets you boot multiple tools.
* **Automated build**: Once you like the workflow, script the remaster with *woof-CE*’s `build-scripts`, enabling CI releases.

---

Happy diagnostics!
