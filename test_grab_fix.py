#!/usr/bin/env python3
"""
Test script to verify the grab_set fix works correctly
This script tests that modal windows can be created without the TclError.
"""

import sys
import os
import tkinter as tk
from tkinter import ttk

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_grab_fix():
    """Test that the grab_set fix works correctly."""
    print("Testing grab_set fix...")
    
    # Create a main window
    root = tk.Tk()
    root.title("Grab Fix Test")
    root.geometry("400x300")
    
    def test_end_screen():
        """Test the EndScreenWindow."""
        try:
            from agent.gui.end_screen_window import EndScreenWindow
            
            # Create some dummy summary data
            summary_data = {
                "total_tests": 5,
                "passed_tests": 4,
                "failed_tests": 1,
                "test_results": [
                    {"name": "Test 1", "status": "PASS"},
                    {"name": "Test 2", "status": "PASS"},
                    {"name": "Test 3", "status": "FAIL"},
                    {"name": "Test 4", "status": "PASS"},
                    {"name": "Test 5", "status": "PASS"}
                ]
            }
            
            # Create the end screen window
            end_screen = EndScreenWindow(
                parent=root,
                summary_data=summary_data,
                log_callback=lambda msg, level: print(f"[{level}] {msg}")
            )
            
            print("EndScreenWindow created successfully!")
            
            # Wait for the window to close
            root.wait_window(end_screen)
            
        except Exception as e:
            print(f"Error testing EndScreenWindow: {e}")
            import traceback
            traceback.print_exc()
    
    def test_results_display():
        """Test the ResultsDisplayWindow."""
        try:
            from agent.gui.results_display_window import ResultsDisplayWindow
            
            # Create some dummy test results
            test_results = [
                {
                    "test_name": "Sample Test 1",
                    "status": "PASS",
                    "timestamp": "2024-01-01 12:00:00",
                    "details": "Test completed successfully"
                },
                {
                    "test_name": "Sample Test 2", 
                    "status": "FAIL",
                    "timestamp": "2024-01-01 12:01:00",
                    "details": "Test failed due to error"
                }
            ]
            
            # Create the results display window
            results_window = ResultsDisplayWindow(
                parent=root,
                test_results=test_results,
                log_callback=lambda msg, level: print(f"[{level}] {msg}")
            )
            
            print("ResultsDisplayWindow created successfully!")
            
            # Wait for the window to close
            root.wait_window(results_window)
            
        except Exception as e:
            print(f"Error testing ResultsDisplayWindow: {e}")
            import traceback
            traceback.print_exc()
    
    def test_device_condition():
        """Test the DeviceConditionWindow."""
        try:
            from agent.gui.device_condition import DeviceConditionWindow
            
            # Create the device condition window
            condition_window = DeviceConditionWindow(
                parent=root,
                asset_num="TEST001",
                log_callback=lambda msg, level: print(f"[{level}] {msg}")
            )
            
            print("DeviceConditionWindow created successfully!")
            
            # Wait for the window to close
            root.wait_window(condition_window)
            
        except Exception as e:
            print(f"Error testing DeviceConditionWindow: {e}")
            import traceback
            traceback.print_exc()
    
    # Create a simple UI to test different windows
    main_frame = ttk.Frame(root, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    title_label = ttk.Label(
        main_frame,
        text="Grab Set Fix Test",
        font=("Arial", 16, "bold")
    )
    title_label.pack(pady=(0, 20))
    
    info_label = ttk.Label(
        main_frame,
        text="Test that modal windows can be created without TclError.\n"
             "Each button will open a different modal window.",
        justify=tk.CENTER
    )
    info_label.pack(pady=(0, 20))
    
    # Test buttons
    ttk.Button(
        main_frame,
        text="Test End Screen Window",
        command=test_end_screen
    ).pack(pady=5)
    
    ttk.Button(
        main_frame,
        text="Test Results Display Window", 
        command=test_results_display
    ).pack(pady=5)
    
    ttk.Button(
        main_frame,
        text="Test Device Condition Window",
        command=test_device_condition
    ).pack(pady=5)
    
    ttk.Button(
        main_frame,
        text="Close",
        command=root.quit
    ).pack(pady=(20, 0))
    
    print("Grab fix test window created. Click buttons to test modal windows.")
    print("If no TclError occurs, the fix is working correctly.")
    
    root.mainloop()

if __name__ == "__main__":
    test_grab_fix()
