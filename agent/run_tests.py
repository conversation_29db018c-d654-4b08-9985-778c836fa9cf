#!/usr/bin/env python3
"""
Arcoa Nexus Test Runner Script

This script provides a command-line interface for running tests using the test framework.
"""
import argparse
import logging
import os
import sys
import time
from typing import Dict, Any, List, Optional, Set

# Make sure we can import our own modules regardless of how we're called
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from agent.tests.test_framework import (
    TestCategory, TestSeverity, TestStatus, get_available_tests
)
from agent.tests.test_runner import TestRunner
from agent.tests.test_config import get_setting, get_test_suite, get_test_args


def setup_logging(log_level: str = "INFO", log_file: Optional[str] = None) -> None:
    """
    Set up logging configuration.
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR)
        log_file: Optional file to log to
    """
    # Convert string log level to logging constant
    numeric_level = getattr(logging, log_level.upper(), logging.INFO)
    
    # Configure logging
    logging.basicConfig(
        level=numeric_level,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(),
            *([] if log_file is None else [logging.FileHandler(log_file)])
        ]
    )


def log_callback(message: str, level: str = "info") -> None:
    """
    Callback function for logging messages from tests.
    
    Args:
        message: The message to log
        level: The log level (info, warning, error, debug)
    """
    logger = logging.getLogger("arcoa.tests")
    log_method = getattr(logger, level.lower(), logger.info)
    log_method(message)


def parse_args() -> argparse.Namespace:
    """
    Parse command-line arguments.
    
    Returns:
        Parsed arguments
    """
    parser = argparse.ArgumentParser(description="Arcoa Nexus Test Runner")
    
    # Test selection options
    test_group = parser.add_argument_group("Test Selection")
    test_group.add_argument(
        "--tests",
        nargs="*",
        help="Specific tests to run (if not specified, runs default suite)"
    )
    test_group.add_argument(
        "--suite",
        choices=["default", "quick", "full", "wipe", "stress"],
        default="default",
        help="Predefined test suite to run"
    )
    test_group.add_argument(
        "--categories",
        nargs="*",
        choices=[c.value for c in TestCategory],
        help="Test categories to run"
    )
    test_group.add_argument(
        "--severities",
        nargs="*",
        choices=[s.value for s in TestSeverity],
        help="Test severities to run"
    )
    
    # Execution options
    exec_group = parser.add_argument_group("Execution Options")
    exec_group.add_argument(
        "--parallel",
        action="store_true",
        help="Run tests in parallel"
    )
    exec_group.add_argument(
        "--workers",
        type=int,
        default=4,
        help="Maximum number of parallel workers"
    )
    exec_group.add_argument(
        "--report-dir",
        default=get_setting("report_dir"),
        help="Directory to save test reports"
    )
    
    # Output options
    output_group = parser.add_argument_group("Output Options")
    output_group.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default=get_setting("log_level"),
        help="Logging level"
    )
    output_group.add_argument(
        "--log-file",
        help="Log file path"
    )
    
    # Utility options
    util_group = parser.add_argument_group("Utility Options")
    util_group.add_argument(
        "--list",
        action="store_true",
        help="List available tests and exit"
    )
    util_group.add_argument(
        "--asset",
        help="Asset number or identifier"
    )
    util_group.add_argument(
        "--operator",
        help="Operator ID or name"
    )
    
    return parser.parse_args()


def list_available_tests() -> None:
    """List all available tests with their metadata."""
    tests = get_available_tests()
    
    print(f"Available tests ({len(tests)}):")
    print("-" * 80)
    
    # Group tests by category
    by_category = {}
    for test in tests:
        category = test["category"]
        if category not in by_category:
            by_category[category] = []
        by_category[category].append(test)
    
    # Print tests by category
    for category, category_tests in sorted(by_category.items()):
        print(f"\n{category.upper()} TESTS:")
        for test in sorted(category_tests, key=lambda t: t["name"]):
            print(f"  {test['full_path']}")
            print(f"    Severity: {test['severity']}")
            if test['description']:
                print(f"    Description: {test['description']}")
    
    print("\nTest Suites:")
    print("-" * 80)
    print("  default: Basic CPU and RAM tests")
    print("  quick: Fast tests for basic functionality")
    print("  full: Comprehensive testing of all components")
    print("  wipe: Drive wiping and verification tests")
    print("  stress: Extended stress tests for CPU and RAM")


def main() -> int:
    """
    Main entry point for the test runner.
    
    Returns:
        Exit code (0 for success, non-zero for failure)
    """
    # Parse command-line arguments
    args = parse_args()
    
    # Set up logging
    setup_logging(args.log_level, args.log_file)
    logger = logging.getLogger("arcoa.tests")
    
    # List tests if requested
    if args.list:
        list_available_tests()
        return 0
    
    # Determine which tests to run
    tests_to_run = []
    if args.tests:
        # Use explicitly specified tests
        tests_to_run = args.tests
    else:
        # Use a predefined suite
        tests_to_run = get_test_suite(args.suite)
    
    # Convert categories and severities to sets if specified
    categories = set(TestCategory(c) for c in args.categories) if args.categories else None
    severities = set(TestSeverity(s) for s in args.severities) if args.severities else None
    
    # Log test plan
    logger.info(f"Running test suite: {args.suite if not args.tests else 'custom'}")
    logger.info(f"Tests to run: {', '.join(tests_to_run) if tests_to_run else 'All'}")
    if categories:
        logger.info(f"Filtering by categories: {', '.join(c.value for c in categories)}")
    if severities:
        logger.info(f"Filtering by severities: {', '.join(s.value for s in severities)}")
    
    # Prepare test arguments
    test_args = {}
    for test in tests_to_run:
        test_args[test] = {
            "log_callback": log_callback,
            **get_test_args(test)
        }
    
    # Create and run the test runner
    start_time = time.time()
    runner = TestRunner(
        log_callback=log_callback,
        report_dir=args.report_dir,
        parallel=args.parallel,
        max_workers=args.workers
    )
    
    results = runner.run_tests(
        tests=tests_to_run,
        categories=categories,
        severities=severities,
        test_args=test_args
    )
    
    # Calculate total time
    total_time = time.time() - start_time
    
    # Count results by status
    status_counts = {status.value: 0 for status in TestStatus}
    for result in results.values():
        status = result.get("test_details", {}).get("status", "error")
        status_counts[status] = status_counts.get(status, 0) + 1
    
    # Log summary
    logger.info(f"Test run completed in {total_time:.2f} seconds")
    logger.info(f"Total tests: {len(results)}")
    for status, count in status_counts.items():
        if count > 0:
            logger.info(f"  {status}: {count}")
    
    # Determine exit code based on results
    if status_counts.get("fail", 0) > 0 or status_counts.get("error", 0) > 0:
        return 1
    return 0


if __name__ == "__main__":
    sys.exit(main())
