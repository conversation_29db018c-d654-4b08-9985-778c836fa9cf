#!/usr/bin/env python3
"""
Arcoa Nexus Diagnostics Tool - Main Entry Point

This script serves as the entry point for the Arcoa Nexus diagnostics and secure wipe platform.
It starts the GUI and initializes the necessary systems.
"""
import os
import sys
import multiprocessing

# Make sure we can import our own modules regardless of how we're called
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__))) 
if project_root not in sys.path:
    sys.path.insert(0, project_root)


def main():
    """Main entry point for the Arcoa Nexus diagnostics application."""
    from agent.gui.main_window import NexusApp
    app = NexusApp()
    app.mainloop()


if __name__ == "__main__":
    multiprocessing.freeze_support()
    main()
