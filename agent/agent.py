#!/usr/bin/env python3
"""Arcoa Nexus Agent - Client side component.

This agent runs on test machines (via PXE, USB or ISO boot) and:
1. Collects system information (serial, model, specs)
2. Runs hardware diagnostics 
3. Reports results back to Nexus server

For initial PoC, only basic system info and a quick CPU load test are implemented.

Usage:
    python agent.py --server http://server-ip:8000
"""
import argparse
import datetime
import json
import platform
import subprocess
import time
from typing import Dict, Any, List, Optional

import httpx
import psutil
from rich.console import Console

console = Console()


def get_serial_number() -> str:
    """Get system serial number using various platform-specific methods."""
    try:
        if platform.system() == "Windows":
            # Windows-specific method using wmic
            result = subprocess.run(
                ["wmic", "bios", "get", "serialnumber"], 
                capture_output=True, 
                text=True,
                check=True
            )
            lines = result.stdout.strip().split('\n')
            if len(lines) >= 2:
                return lines[1].strip()
        else:
            # Linux method using dmidecode (requires root)
            result = subprocess.run(
                ["dmidecode", "-s", "system-serial-number"],
                capture_output=True,
                text=True,
                check=True
            )
            return result.stdout.strip()
    except (subprocess.SubprocessError, FileNotFoundError):
        pass
        
    # Fallback if commands fail
    return f"UNKNOWN-{platform.node()}"


def get_system_info() -> Dict[str, Any]:
    """Collect basic system information."""
    info = {
        "hostname": platform.node(),
        "system": platform.system(),
        "release": platform.release(),
        "cpu_model": platform.processor(),
        "cpu_cores": psutil.cpu_count(logical=False),
        "cpu_threads": psutil.cpu_count(logical=True),
        "memory_total_gb": round(psutil.virtual_memory().total / (1024**3), 2),
        "platform": platform.platform(),
    }
    
    # Add storage information
    disks = []
    for part in psutil.disk_partitions(all=True):
        try:
            usage = psutil.disk_usage(part.mountpoint)
            disks.append({
                "device": part.device,
                "mountpoint": part.mountpoint,
                "fstype": part.fstype,
                "size_gb": round(usage.total / (1024**3), 2) if usage.total > 0 else 0,
            })
        except (PermissionError, FileNotFoundError):
            # Some mountpoints might not be accessible
            pass
    
    info["disks"] = disks
    return info


def run_cpu_stress_test(duration_seconds: int = 5) -> Dict[str, Any]:
    """Run a CPU stress test using multiprocessing to properly utilize all cores."""
    console.print(f"Running CPU stress test for {duration_seconds} seconds...")
    
    # Record starting temperatures if available
    starting_temps = {}
    try:
        temps = psutil.sensors_temperatures()
        for chip, sensors in temps.items():
            for sensor in sensors:
                starting_temps[f"{chip}_{sensor.label or 'unknown'}"] = sensor.current
    except (AttributeError, ImportError):
        # sensors_temperatures not available on all platforms
        pass
    
    # Start time for the test
    start_time = time.time()
    start_dt = datetime.datetime.now()
    
    # CPU stress using multiprocessing to utilize all cores
    import multiprocessing
    
    def cpu_intensive_task(end_time):
        # More intensive calculation that will stress the CPU
        while time.time() < end_time:
            # Matrix operations are CPU intensive
            matrix_size = 100
            for _ in range(20):
                # Create random matrices and multiply them
                import numpy as np
                a = np.random.rand(matrix_size, matrix_size)
                b = np.random.rand(matrix_size, matrix_size)
                c = np.dot(a, b)
    
    # Fallback if numpy isn't available
    def cpu_intensive_fallback(end_time):
        while time.time() < end_time:
            # Computationally intensive calculation
            for i in range(10000000):
                x = i * i / 3.14159
                y = (x ** 0.5) * 2.71828
                z = (x + y) / (x * 0.5)
                if i % 1000000 == 0:
                    # Check if we should stop
                    if time.time() >= end_time:
                        break
    
    # Function to use based on available packages
    cpu_func = cpu_intensive_fallback
    try:
        import numpy
        cpu_func = cpu_intensive_task
    except ImportError:
        console.print("[yellow]Numpy not found, using fallback CPU test[/yellow]")
    
    # CPU load measurements
    cpu_load = []
    end_time = start_time + duration_seconds
    
    # Start processes for each CPU core
    processes = []
    num_cores = psutil.cpu_count(logical=False) or 1
    for _ in range(num_cores):
        p = multiprocessing.Process(target=cpu_func, args=(end_time,))
        processes.append(p)
        p.start()
    
    # Monitor CPU usage while stress test runs
    while time.time() < end_time + 1:  # Add 1 second to ensure processes have started
        cpu_percent = psutil.cpu_percent(interval=0.5)
        cpu_load.append(cpu_percent)
        
    # Clean up processes
    for p in processes:
        if p.is_alive():
            p.terminate()
        p.join()
    
    # Ending time and temperatures
    end_dt = datetime.datetime.now()
    
    ending_temps = {}
    try:
        temps = psutil.sensors_temperatures()
        for chip, sensors in temps.items():
            for sensor in sensors:
                ending_temps[f"{chip}_{sensor.label or 'unknown'}"] = sensor.current
    except (AttributeError, ImportError):
        pass
    
    # Calculate results
    avg_cpu_load = sum(cpu_load) / len(cpu_load) if cpu_load else 0
    max_cpu_load = max(cpu_load) if cpu_load else 0
    
    # Simple pass/fail criteria
    status = "pass"
    fail_reason = None
    
    # If we couldn't achieve at least 50% load, something might be wrong
    if max_cpu_load < 50.0:
        status = "fail"
        fail_reason = f"CPU could not reach sufficient load (max: {max_cpu_load}%)"
    
    result = {
        "status": status,
        "fail_reason": fail_reason,
        "duration_seconds": duration_seconds,
        "avg_cpu_load": avg_cpu_load,
        "max_cpu_load": max_cpu_load,
        "temperature_delta": {
            k: ending_temps.get(k, 0) - starting_temps.get(k, 0)
            for k in set(starting_temps) | set(ending_temps)
        } if starting_temps and ending_temps else {},
    }
    
    console.print(f"CPU test completed: {status.upper()}")
    console.print(f"Average CPU load: {avg_cpu_load:.1f}%")
    
    return {
        "test_details": result,
        "started_at": start_dt.isoformat(),
        "finished_at": end_dt.isoformat(),
    }


async def send_result(server_url: str, result: Dict[str, Any]) -> bool:
    """Send test result to server."""
    url = f"{server_url.rstrip('/')}/result"
    console.print(f"Sending result to {url}...")
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                url,
                json=result,
                timeout=30.0,
            )
        
        if response.status_code == 200:
            console.print("[green]Result sent successfully![/green]")
            return True
        else:
            console.print(f"[red]Failed to send result: {response.status_code} {response.text}[/red]")
            return False
    except Exception as e:
        console.print(f"[red]Error sending result: {e}[/red]")
        return False


async def main():
    """Main entry point for the agent."""
    parser = argparse.ArgumentParser(description="Arcoa Nexus Agent")
    parser.add_argument(
        "--server", 
        required=True,
        help="Server URL (e.g., http://*************:8000)"
    )
    parser.add_argument(
        "--skip-tests",
        action="store_true",
        help="Skip actual hardware tests and use mock results"
    )
    parser.add_argument(
        "--operator", 
        help="Operator ID/initials (optional)"
    )
    parser.add_argument(
        "--asset", 
        help="Asset number (optional)"
    )
    args = parser.parse_args()
    
    # Banner
    console.print(
        "[bold blue]====================================[/bold blue]\n"
        "[bold blue]       ARCOA NEXUS AGENT v0.1.0      [/bold blue]\n"
        "[bold blue]====================================[/bold blue]\n"
    )
    
    # Get operator ID
    operator_id = args.operator
    if not operator_id:
        operator_id = console.input("[bold yellow]Enter operator ID/initials:[/bold yellow] ")
        
    console.print(f"Operator: [bold]{operator_id}[/bold]\n")
    
    # Get asset number
    asset_number = args.asset
    if not asset_number:
        asset_number = console.input("[bold yellow]Enter asset number (e.g. 1091234):[/bold yellow] ")
        
    console.print(f"Asset number: [bold]{asset_number}[/bold]\n")
    
    # Get system information
    console.print("[yellow]Collecting system information...[/yellow]")
    serial = get_serial_number()
    system_info = get_system_info()
    console.print(f"System serial: {serial}")
    console.print(f"CPU: {system_info['cpu_model']} ({system_info['cpu_cores']} cores / {system_info['cpu_threads']} threads)")
    console.print(f"Memory: {system_info['memory_total_gb']} GB")
    console.print(f"Disks detected: {len(system_info['disks'])}")
    
    # Generate mock CPU test result or run the actual test
    if args.skip_tests:
        console.print("[yellow]Using mock CPU test results (--skip-tests)[/yellow]")
        # Mock result with basic structure
        start_dt = datetime.datetime.now()
        time.sleep(1)  # Brief pause for realism
        end_dt = datetime.datetime.now()
        cpu_result = {
            "test_details": {
                "status": "pass",
                "fail_reason": None,
                "duration_seconds": 1,
                "avg_cpu_load": 85.2,
                "max_cpu_load": 92.7,
                "temperature_delta": {"cpu_temp": 5.3},
            },
            "started_at": start_dt.isoformat(),
            "finished_at": end_dt.isoformat(),
        }
        console.print("[green]CPU Test: PASS (simulated)[/green]")
    else:
        console.print("[yellow]Skipping actual CPU stress test (temporary)[/yellow]")
        # Simplified test without multiprocessing
        start_dt = datetime.datetime.now()
        cpu_percent = psutil.cpu_percent(interval=1.0)  # Just measure current usage
        end_dt = datetime.datetime.now()
        cpu_result = {
            "test_details": {
                "status": "pass",
                "fail_reason": None,
                "duration_seconds": 1,
                "avg_cpu_load": cpu_percent,
                "max_cpu_load": cpu_percent,
                "temperature_delta": {},
            },
            "started_at": start_dt.isoformat(),
            "finished_at": end_dt.isoformat(),
        }
    
    # Combine results
    now = datetime.datetime.now().isoformat()
    test_result = {
        "asset_serial": serial,
        "asset_number": asset_number,
        "operator_id": operator_id,
        "test_name": "cpu_stress", 
        "status": cpu_result["test_details"]["status"],
        "data": {
            "system_info": system_info,
            **cpu_result["test_details"]
        },
        "started_at": cpu_result["started_at"],
        "finished_at": cpu_result["finished_at"], 
    }
    
    # Send to server
    success = await send_result(args.server, test_result)
    if not success:
        # Write to local file as backup
        backup_file = f"nexus_result_{serial}_{int(time.time())}.json"
        with open(backup_file, "w") as f:
            json.dump(test_result, f, indent=2)
        console.print(f"[yellow]Result saved to {backup_file}[/yellow]")


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
