#!/usr/bin/env python3
"""
Arcoa Nexus Hardware Information Module

This module handles collecting system hardware information including:
- CPU, RAM, disk details
- GPU information
- Battery status
- Serial numbers and system identifiers
"""
import datetime
import os
import platform
import subprocess
import shutil
import logging
from typing import Dict, Any, List

import psutil

try:
    import cpuinfo  # For detailed CPU information
except ImportError:
    cpuinfo = None

# NEW: for detailed disk info
from agent.hardware.drive_info import get_detailed_drive_info

LOGGER = logging.getLogger(__name__)

def get_serial_number() -> str:
    """Get system serial number."""
    serial = "Unknown"
    try:
        if platform.system() == "Windows":
            try:
                # Use WMI to query Windows
                result = subprocess.run(
                    ["wmic", "bios", "get", "serialnumber"],
                    capture_output=True,
                    text=True,
                    check=True
                )
                lines = result.stdout.strip().split('\n')
                if len(lines) >= 2:
                    return lines[1].strip()
            except Exception:
                pass

            # Fallback to PowerShell if WMIC fails
            try:
                result = subprocess.run(
                    ["powershell", "-Command", "(Get-WmiObject -Class Win32_BIOS).SerialNumber"],
                    capture_output=True,
                    text=True,
                    check=True
                )
                return result.stdout.strip() or "Unknown"
            except Exception:
                return "Unknown"
        elif platform.system() == "Linux":
            # Try reading from /sys/class/dmi/id/ first (often doesn't require sudo)
            sys_paths_to_try = [
                "/sys/class/dmi/id/product_serial",
                "/sys/class/dmi/id/board_serial",
                "/sys/firmware/devicetree/base/serial-number",  # For some ARM-based systems
                "/sys/devices/virtual/dmi/id/product_serial"  # Alternative path on some distros
            ]
            for path in sys_paths_to_try:
                try:
                    with open(path, 'r') as f:
                        content = f.read().strip()
                    if content and content != "None" and content != "To be filled by O.E.M.": # Check for common placeholder values
                        serial = content
                        return serial
                except FileNotFoundError:
                    continue # Path doesn't exist, try next
                except PermissionError:
                    LOGGER.warning(f"Permission denied reading serial from {path}")
                    # Don't return yet, try other paths or methods
                    continue
                except Exception as e:
                    LOGGER.warning(f"Error reading serial from {path}: {e}")
                    continue

            # Try non-privileged commands first before using dmidecode with sudo
            try:
                # Try lshw (which sometimes works without sudo)
                if shutil.which("lshw") is not None:
                    result = subprocess.run(
                        ["lshw", "-c", "system"],
                        capture_output=True,
                        text=True,
                        check=False
                    )
                    if result.returncode == 0:
                        for line in result.stdout.splitlines():
                            if "serial:" in line.lower():
                                serial_part = line.split(":", 1)[1].strip()
                                if serial_part and serial_part != "None" and serial_part != "To be filled by O.E.M.":
                                    return serial_part
                    LOGGER.info("lshw did not provide a valid serial number")
                else:
                    LOGGER.info("lshw command not found")
                    
                # Try hostnamectl which might show serial on some systems
                if shutil.which("hostnamectl") is not None:
                    result = subprocess.run(
                        ["hostnamectl", "status"],
                        capture_output=True,
                        text=True,
                        check=False
                    )
                    if result.returncode == 0:
                        for line in result.stdout.splitlines():
                            if "hardware id" in line.lower() or "serial" in line.lower():
                                serial_part = line.split(":", 1)[1].strip()
                                if serial_part and serial_part != "None" and serial_part != "To be filled by O.E.M.":
                                    return serial_part
                    LOGGER.info("hostnamectl did not provide a valid serial number")
                else:
                    LOGGER.info("hostnamectl command not found")
                    
                # Fallback to dmidecode if all else fails
                if shutil.which("dmidecode") is None:
                    LOGGER.warning("dmidecode command not found, cannot fetch serial via dmidecode.")
                    return "Unknown" # Return Unknown if dmidecode is not even there

                # Try dmidecode without sudo first (no password prompt)
                try:
                    result = subprocess.run(
                        ["dmidecode", "-s", "system-serial-number"],
                        capture_output=True,
                        text=True,
                        check=False
                    )
                    if result.returncode == 0:
                        output = result.stdout.strip()
                        if output and output != "None" and output != "To be filled by O.E.M.":
                            return output
                except Exception as e:
                    LOGGER.info(f"dmidecode without sudo failed: {e}")
            except Exception as e:
                LOGGER.error(f"Exception calling hardware info commands for serial: {e}")
                # serial remains "Unknown"
    except Exception as e:
        LOGGER.error(f"General exception in get_serial_number: {e}")
        # serial remains "Unknown"
    return serial


def get_screen_resolution(root_tk_instance=None) -> str:
    """Get screen resolution using Tkinter."""
    try:
        if root_tk_instance:
            width = root_tk_instance.winfo_screenwidth()
            height = root_tk_instance.winfo_screenheight()
            resolution = f"{width}x{height}"
        else:
            import tkinter as tk
            temp_root = tk.Tk()
            width = temp_root.winfo_screenwidth()
            height = temp_root.winfo_screenheight()
            resolution = f"{width}x{height}"
            temp_root.destroy()
        return resolution
    except Exception as e:
        return f"Error detecting resolution: {e}"


def get_cpu_info_detailed() -> str:
    """
    Returns a human-friendly CPU info string, e.g.:
    Intel(R) Core(TM) i7-7820HQ CPU @ 2.90GHz, 2901 MHz, 4 Core(s), 8 Logical Processor(s)
    """
    try:
        # Get CPU brand/model
        if cpuinfo is not None:
            cpu_brand = cpuinfo.get_cpu_info().get('brand_raw', '')
        else:
            cpu_brand = platform.processor()
        if not cpu_brand and platform.system() == "Linux":
            # Try /proc/cpuinfo
            try:
                with open('/proc/cpuinfo', 'r') as f:
                    for line in f:
                        if line.startswith("model name"):
                            cpu_brand = line.split(":", 1)[1].strip()
                            break
            except Exception:
                pass
        if not cpu_brand:
            cpu_brand = "Unknown CPU"

        # Get core and logical processor counts
        try:
            cpu_cores = psutil.cpu_count(logical=False)
        except Exception:
            cpu_cores = None
        try:
            cpu_logical = psutil.cpu_count(logical=True)
        except Exception:
            cpu_logical = None

        # Get max frequency in MHz and GHz
        mhz = None
        ghz = None
        try:
            freq = psutil.cpu_freq()
            if freq and hasattr(freq, 'max') and freq.max > 0:
                mhz = int(round(freq.max))
                ghz = freq.max / 1000.0
            elif freq and hasattr(freq, 'current') and freq.current > 0:
                mhz = int(round(freq.current))
                ghz = freq.current / 1000.0
        except Exception:
            pass

        # Build the string
        parts = [cpu_brand]
        if mhz:
            parts.append(f"{mhz} MHz")
        if ghz:
            parts.append(f"{ghz:.2f} GHz")
        if cpu_cores is not None:
            parts.append(f"{cpu_cores} Core(s)")
        if cpu_logical is not None:
            parts.append(f"{cpu_logical} Logical Processor(s)")

        return ", ".join(parts)
    except Exception:
        return "N/A"


def get_gpu_info() -> List[str]:
    """Attempts to detect GPU(s) using OS-specific commands."""
    gpus = []
    try:
        if platform.system() == "Windows":
            # Use PowerShell command to get GPU name and VRAM information in a clean format
            ps_command = [
                "powershell",
                "-Command",
                ("Get-CimInstance Win32_VideoController | ForEach-Object { "
                 "$ram = $_.AdapterRAM/1048576; "
                 "if ($ram -ge 1024) { $ramGB = [Math]::Round($ram/1024); "
                 "Write-Output \"$($_.Name), $ramGB GB\" } else { "
                 "$ramMB = [Math]::Round($ram); Write-Output \"$($_.Name), $ramMB MB\" } }")
            ]
            try:
                process = subprocess.run(ps_command, capture_output=True, text=True, check=True)
                output = process.stdout.strip()
                if output:
                    gpus.extend([line.strip() for line in output.splitlines() if line.strip()])
            except Exception as e:
                # Fallback to WMIC command if PowerShell fails
                wmic_path = r"C:\Windows\System32\wbem\WMIC.exe"
                command_str = f'{wmic_path} path Win32_VideoController get Caption,AdapterRAM'
                process = subprocess.run(command_str, capture_output=True, text=True, shell=True, check=False)
                if process.returncode == 0:
                    output_lines = process.stdout.strip().splitlines()
                    if output_lines and "Caption" in output_lines[0]:
                        for line in output_lines[1:]:
                            if line.strip():
                                parts = line.split()
                                try:
                                    adapter_ram = int(parts[-1])
                                    adapter_ram_mb = adapter_ram / 1048576
                                    if adapter_ram_mb >= 1024:
                                        adapter_ram_display = f"{round(adapter_ram_mb/1024)} GB"
                                    else:
                                        adapter_ram_display = f"{round(adapter_ram_mb)} MB"
                                    caption = " ".join(parts[:-1])
                                    gpus.append(f"{caption}, {adapter_ram_display}")
                                except Exception:
                                    gpus.append(line.strip())
                    else:
                        gpus.append("WMIC command output format unexpected.")
                else:
                    gpus.append(f"Error detecting GPUs (WMIC): Command failed with exit code {process.returncode}.")
            if not gpus:
                gpus.append("No GPUs detected or PowerShell/WMIC unavailable.")

        elif platform.system() == "Linux":
            try:
                process = subprocess.run(["lspci"], capture_output=True, text=True, check=True)
                for line in process.stdout.splitlines():
                    if "VGA compatible controller" in line or "3D controller" in line:
                        gpus.append(line.split(":", 2)[-1].strip())
            except (subprocess.CalledProcessError, FileNotFoundError):
                try:
                    process = subprocess.run(["lshw", "-C", "display"], capture_output=True, text=True, check=True)
                    for line in process.stdout.splitlines():
                        if "product:" in line.lower():
                            gpus.append(line.split(":", 1)[-1].strip())
                except (subprocess.CalledProcessError, FileNotFoundError):
                    gpus.append("Error detecting GPUs: lspci and lshw commands failed or not found on Linux.")
        
        if not gpus:
            gpus.append("No GPUs detected or OS not supported for GPU detection.")
            
    except Exception as e:
        gpus.append(f"An unexpected error occurred during GPU detection: {str(e)}")
    return gpus


def get_battery_info() -> dict:
    """Get battery status and condition information."""
    battery_info = {
        "present": False,
        "percent": None,
        "charging": None,
        "secsleft": None,
        "cycle_count": None,
        "health": None,
        "status": "Not detected"
    }
    try:
        batt = psutil.sensors_battery()
        if batt is not None:
            battery_info["present"] = True
            battery_info["percent"] = batt.percent
            battery_info["charging"] = batt.power_plugged
            battery_info["secsleft"] = batt.secsleft
            battery_info["status"] = (
                "Charging" if batt.power_plugged else "Discharging"
            )
        # Try to get more info (cycle count, health) platform-specific
        if platform.system() == "Windows":
            try:
                # Cycle count and health via PowerShell
                ps = subprocess.run([
                    "powershell", "-Command",
                    "Get-WmiObject -Class BatteryStatus -Namespace root\\wmi | Select-Object -Property CycleCount,DesignCapacity,FullChargedCapacity"
                ], capture_output=True, text=True, check=False)
                for line in ps.stdout.splitlines():
                    if "CycleCount" in line:
                        try:
                            battery_info["cycle_count"] = int(line.split(":")[-1].strip())
                        except Exception:
                            pass
                    if "DesignCapacity" in line:
                        try:
                            battery_info["design_capacity"] = int(line.split(":")[-1].strip())
                        except Exception:
                            pass
                    if "FullChargedCapacity" in line:
                        try:
                            battery_info["full_charged_capacity"] = int(line.split(":")[-1].strip())
                        except Exception:
                            pass
                if (
                    battery_info.get("design_capacity")
                    and battery_info.get("full_charged_capacity")
                ):
                    health = (
                        battery_info["full_charged_capacity"] / battery_info["design_capacity"] * 100
                    )
                    battery_info["health"] = round(health, 1)
            except Exception:
                pass
        elif platform.system() == "Linux":
            # Try sysfs for extra details
            try:
                for bat in os.listdir("/sys/class/power_supply"):
                    if bat.startswith("BAT"):
                        base = f"/sys/class/power_supply/{bat}/"
                        def read_file(fname):
                            try:
                                with open(base + fname) as f:
                                    return f.read().strip()
                            except Exception:
                                return None
                        battery_info["cycle_count"] = read_file("cycle_count")
                        battery_info["health"] = read_file("health") or read_file("capacity")
                        break
            except Exception:
                pass
    except Exception:
        pass
    return battery_info


def get_device_type() -> str:
    """
    Attempts to detect if the device is a laptop, desktop, or unknown using OS-specific heuristics.
    Returns: 'laptop', 'desktop', or 'unknown'
    """
    import platform, glob
    system = platform.system()
    # --- Windows ---
    if system == "Windows":
        # 1. Check for battery
        try:
            output = subprocess.check_output(
                'wmic path Win32_Battery get BatteryStatus', shell=True
            ).decode(errors='ignore')
            if any(l.strip().isdigit() for l in output.splitlines() if l.strip()):
                return "laptop"
        except Exception:
            pass
        # 2. Check chassis type
        try:
            output = subprocess.check_output(
                'wmic SystemEnclosure get ChassisTypes', shell=True
            ).decode(errors='ignore')
            for line in output.splitlines():
                line = line.strip()
                if line.isdigit() and int(line) in [8, 9, 10, 14]:
                    return "laptop"
        except Exception:
            pass
        # 3. Check PCSystemType
        try:
            output = subprocess.check_output(
                'wmic computersystem get PCSystemType', shell=True
            ).decode(errors='ignore')
            for line in output.splitlines():
                line = line.strip()
                if line == '2':
                    return "laptop"
                elif line == '1':
                    return "desktop"
        except Exception:
            pass
        return "unknown"
    # --- Linux ---
    elif system == "Linux":
        # 1. Battery check
        try:
            if glob.glob('/sys/class/power_supply/BAT*'):
                return "laptop"
        except Exception:
            pass
        # 2. Chassis type check
        try:
            with open('/sys/class/dmi/id/chassis_type') as f:
                chassis_type = int(f.read().strip())
                if chassis_type in [8, 9, 10, 14]:
                    return "laptop"
                elif chassis_type == 3:
                    return "desktop"
        except Exception:
            pass
        # 3. Product name/family check
        for dmi_file in ["/sys/class/dmi/id/product_name", "/sys/class/dmi/id/product_family"]:
            try:
                with open(dmi_file) as f:
                    val = f.read().lower()
                    if "laptop" in val or "notebook" in val:
                        return "laptop"
                    elif "desktop" in val:
                        return "desktop"
            except Exception:
                pass
        return "unknown"
    # --- Other OS ---
    else:
        return "unknown"

def get_system_info(root_tk_instance=None) -> Dict[str, Any]:
    """Gathers detailed system information, including make, model, serial, computrace, battery."""
    info = {
        "hostname": platform.node(),
        "os_version": f"{platform.system()} {platform.release()} ({platform.version()})",
        "architecture": platform.machine(),
        "cpu": get_cpu_info_detailed(),
        "memory": "N/A",
        "disks": [],
        "gpus": get_gpu_info(),
        "screen_resolution": get_screen_resolution(root_tk_instance),
        "network_interfaces": [],
        "make": "Unknown",
        "model": "Unknown",
        "serial_number": get_serial_number(),  # Call get_serial_number() directly
        "computrace": "Unknown",
        "battery": get_battery_info()
    }

    # --- Make, Model, Serial, Computrace ---
    try:
        if platform.system() == "Linux":
            # Get make and model from non-privileged sources
            try:
                # Try reading from /sys/class/dmi/id/ files (no sudo required)
                make_path = "/sys/class/dmi/id/sys_vendor"
                model_path = "/sys/class/dmi/id/product_name"
                
                try:
                    with open(make_path, 'r') as f:
                        make = f.read().strip()
                        if make and make != "None" and make != "To be filled by O.E.M.":
                            info["make"] = make
                except (FileNotFoundError, PermissionError):
                    pass
                    
                try:
                    with open(model_path, 'r') as f:
                        model = f.read().strip()
                        if model and model != "None" and model != "To be filled by O.E.M.":
                            info["model"] = model
                except (FileNotFoundError, PermissionError):
                    pass
            except Exception as e:
                LOGGER.warning(f"Error getting make/model from /sys files: {e}")
                
            # Try lshw for make/model if /sys files didn't work
            if info["make"] == "Unknown" or info["model"] == "Unknown":
                try:
                    if shutil.which("lshw") is not None:
                        result = subprocess.run(["lshw", "-c", "system"], capture_output=True, text=True, check=False)
                        if result.returncode == 0:
                            for line in result.stdout.splitlines():
                                if "vendor:" in line.lower() and info["make"] == "Unknown":
                                    info["make"] = line.split(":", 1)[1].strip()
                                if "product:" in line.lower() and info["model"] == "Unknown":
                                    info["model"] = line.split(":", 1)[1].strip()
                except Exception as e:
                    LOGGER.warning(f"Error getting make/model from lshw: {e}")
            
            # Use the more robust get_serial_number function that tries multiple methods
            info["serial_number"] = get_serial_number()
            
            # Set computrace to Unknown without using dmidecode
            info["computrace"] = "Unknown (requires elevated privileges)"
        elif platform.system() == "Windows":
            # Make, Model, Serial
            try:
                make = subprocess.run([
                    "powershell", "-Command",
                    "(Get-WmiObject Win32_ComputerSystem).Manufacturer"
                ], capture_output=True, text=True, check=True)
                info["make"] = make.stdout.strip() or "Unknown"
            except Exception:
                pass
            try:
                model = subprocess.run([
                    "powershell", "-Command",
                    "(Get-WmiObject Win32_ComputerSystem).Model"
                ], capture_output=True, text=True, check=True)
                info["model"] = model.stdout.strip() or "Unknown"
            except Exception:
                pass
            try:
                # Use the command that works for the user
                serial = subprocess.run([
                    "powershell", "-Command",
                    "(Get-WmiObject Win32_BIOS).SerialNumber"
                ], capture_output=True, text=True, check=True)
                info["serial_number"] = serial.stdout.strip() or "Unknown"
            except Exception:
                pass
            # Computrace
            try:
                bios = subprocess.run([
                    "powershell", "-Command",
                    "Get-WmiObject -Class Win32_BIOS | Format-List *"
                ], capture_output=True, text=True, check=True)
                bios_out = bios.stdout.lower()
                if "computrace" in bios_out or "absolute" in bios_out:
                    lines = [l for l in bios.stdout.splitlines() if "computrace" in l.lower() or "absolute" in l.lower()]
                    info["computrace"] = ", ".join(lines) if lines else "Present (see BIOS details)"
                else:
                    info["computrace"] = "Not detected"
            except Exception:
                info["computrace"] = "Unknown (no access to BIOS info)"
    except Exception:
        pass

    # --- RAM ---
    try:
        mem = psutil.virtual_memory()
        info["memory"] = f"{mem.total / (1024**3):.2f} GB (Available: {mem.available / (1024**3):.2f} GB)"
    except Exception as e:
        info["memory"] = f"Error detecting memory: {e}"

    # --- Disks ---
    try:
        info["disks"] = get_detailed_drive_info()
    except Exception as e:
        # Fallback to minimal psutil info on error
        try:
            partitions = psutil.disk_partitions()
            info["disks"] = [p.device for p in partitions] if partitions else []
        except Exception:
            info["disks"] = []
        info["disks_error"] = str(e)

    # --- Network Interfaces ---
    net_details = []
    try:
        net_if_addrs = psutil.net_if_addrs()
        for interface_name, interface_addresses in net_if_addrs.items():
            for addr in interface_addresses:
                if str(addr.family) == 'AddressFamily.AF_INET':
                    net_details.append(f"{interface_name}: IPv4={addr.address} (Mask={addr.netmask})")
        info["network_interfaces"] = net_details if net_details else ["No network interfaces with IPv4 found."]
    except Exception as e:
        info["network_interfaces"] = [f"Error detecting network interfaces: {e}"]

    return info
