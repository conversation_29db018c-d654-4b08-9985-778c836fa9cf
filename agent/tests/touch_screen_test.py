#!/usr/bin/env python3
"""
Arcoa Nexus Touch Screen Test Module

Tests touch screen functionality including:
- Target accuracy (touching specific points)
- Multi-touch capability
- Pinch/zoom gestures
"""
import datetime
import tkinter as tk
from typing import Dict, Any, Callable

from agent.tests.test_framework import test, TestCategory, TestSeverity, TestStatus

class TouchScreenTest(tk.Toplevel):
    """GUI for testing touch screen functionality."""
    def __init__(self, parent, log_callback: Callable = None):
        super().__init__(parent)
        self.parent = parent
        self.log_callback = log_callback
        self.title("Touch Screen Test")
        
        # Configure fullscreen window
        self.attributes('-fullscreen', True)
        self.configure(bg='black')
        
        # Test state tracking
        self.targets_touched = 0
        self.multi_touch_detected = False
        self.pinch_zoom_completed = False
        
        # Setup UI components
        self.setup_ui()
        
        # Bind touch events
        self.bind('<Button-1>', self.on_touch)  # Primary touch
        self.bind('<Button-2>', self.on_secondary_touch)  # Secondary touch (right-click)
        self.bind('<Button-3>', self.on_secondary_touch)  # Secondary touch (right-click)
        
        # Track touch points for multi-touch and gestures
        self.touch_points = {}
        self.last_distance = 0
        self.gesture_start_time = 0
        
    def log(self, message, level="info"):
        """Log messages through callback if available."""
        if self.log_callback:
            self.log_callback(message, level)
            
    def setup_ui(self):
        """Initialize test UI components."""
        self.canvas = tk.Canvas(self, bg='black', highlightthickness=0)
        self.canvas.pack(fill=tk.BOTH, expand=True)
        
        # Add instruction label
        self.instruction = self.canvas.create_text(
            self.winfo_screenwidth()/2, 50,
            text="Touch the targets on screen",
            fill="white",
            font=("Arial", 24)
        )
        
        # Draw initial targets and feedback circle
        self.draw_targets()
        self.feedback_circle = self.canvas.create_oval(
            self.winfo_screenwidth()/2 - 100,  # x1
            self.winfo_screenheight()/2 - 100,  # y1
            self.winfo_screenwidth()/2 + 100,   # x2
            self.winfo_screenheight()/2 + 100,  # y2
            outline='cyan',
            width=3,
            fill=''
        )
        
    def draw_targets(self):
        """Draw touch targets on screen."""
        width = self.winfo_screenwidth()
        height = self.winfo_screenheight()
        
        # Clear existing targets
        self.canvas.delete("target")
        
        # Draw 5 targets in different positions
        positions = [
            (width/4, height/4),    # Top-left
            (3*width/4, height/4),  # Top-right 
            (width/2, height/2),    # Center
            (width/4, 3*height/4),  # Bottom-left
            (3*width/4, 3*height/4) # Bottom-right
        ]
        
        self.targets = []
        for x, y in positions:
            target = self.canvas.create_oval(
                x-40, y-40, x+40, y+40,
                fill='red', outline='white', width=2,
                tags="target"
            )
            self.targets.append((target, x, y))
            
    def on_touch(self, event):
        """Handle primary touch events."""
        # Record touch point
        self.touch_points[1] = (event.x, event.y)
        self.check_multi_touch()
        
        # Check if touch hit any target
        for target, x, y in self.targets:
            if (x-40 <= event.x <= x+40) and (y-40 <= event.y <= y+40):
                self.canvas.itemconfig(target, fill='green')
                self.targets_touched += 1
                self.log(f"Target touched ({self.targets_touched}/5)")
                
        # Update instruction when all targets touched
        if self.targets_touched >= 5:
            self.canvas.itemconfig(
                self.instruction,
                text="Now test multi-touch (right-click with mouse while touching)"
            )

    def on_secondary_touch(self, event):
        """Handle secondary touch events (simulated with right-click)."""
        # Record secondary touch point
        self.touch_points[2] = (event.x, event.y)
        self.check_multi_touch()
        return "break"  # Prevent context menu

    def check_multi_touch(self):
        """Check if multiple touches are active and detect gestures."""
        if len(self.touch_points) >= 2:
            self.multi_touch_detected = True
            
            # Get current touch points
            (x1, y1), (x2, y2) = self.touch_points.values()
            
            # Calculate current distance between touches
            current_distance = ((x2 - x1)**2 + (y2 - y1)**2)**0.5
            
            if self.last_distance == 0:
                # First measurement - just store initial distance
                self.last_distance = current_distance
                self.gesture_start_time = datetime.datetime.now().timestamp()
            else:
                # Check for pinch/zoom gesture
                delta = current_distance - self.last_distance
                if abs(delta) > 50:  # Significant movement detected
                    if delta > 0:
                        gesture = "zoom out"
                    else:
                        gesture = "zoom in"
                    
                    # Update feedback circle size based on gesture
                    scale_factor = 1 + (delta / 200)  # Scale factor based on gesture distance
                    current_coords = self.canvas.coords(self.feedback_circle)
                    center_x = (current_coords[0] + current_coords[2]) / 2
                    center_y = (current_coords[1] + current_coords[3]) / 2
                    new_size = max(20, min(500, abs(current_coords[2] - current_coords[0]) * scale_factor))
                    
                    self.canvas.coords(
                        self.feedback_circle,
                        center_x - new_size/2,
                        center_y - new_size/2,
                        center_x + new_size/2,
                        center_y + new_size/2
                    )
                    
                    self.pinch_zoom_completed = True
                    self.canvas.itemconfig(
                        self.instruction,
                        text=f"Gesture detected: {gesture}! Test complete"
                    )
                    self.log(f"Pinch/zoom gesture completed: {gesture}")
            
            self.last_distance = current_distance
            
            if not self.pinch_zoom_completed:
                self.canvas.itemconfig(
                    self.instruction,
                    text="Multi-touch detected! Move fingers apart/together to test pinch/zoom"
                )
                self.log("Multi-touch capability verified")
            
    def get_results(self) -> Dict[str, Any]:
        """Compile test results."""
        status = TestStatus.PASS if (
            self.targets_touched >= 5 
            and self.multi_touch_detected
            and self.pinch_zoom_completed
        ) else TestStatus.FAIL
        
        notes = [
            f"Targets touched: {self.targets_touched}/5",
            f"Multi-touch detected: {'Yes' if self.multi_touch_detected else 'No'}",
            f"Pinch/zoom completed: {'Yes' if self.pinch_zoom_completed else 'No'}"
        ]
        
        return {
            "status": status.value,
            "notes": "\n".join(notes)
        }

@test(
    category=TestCategory.INPUT,
    severity=TestSeverity.HIGH,
    description="Tests touch screen functionality including targets, multi-touch and gestures"
)
def run_touch_screen_test(parent_window, log_callback=None) -> Dict[str, Any]:
    """
    Run the touch screen test and return results.
    
    Args:
        parent_window: Parent tkinter window
        log_callback: Optional callback for logging
        
    Returns:
        Dictionary with test results and timing
    """
    start_dt = datetime.datetime.now()
    
    test_window = TouchScreenTest(parent_window, log_callback)
    parent_window.wait_window(test_window)
    
    end_dt = datetime.datetime.now()
    
    return {
        "test_details": test_window.get_results(),
        "started_at": start_dt.isoformat(),
        "finished_at": end_dt.isoformat()
    }
