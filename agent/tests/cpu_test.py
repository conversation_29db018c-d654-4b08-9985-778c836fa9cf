#!/usr/bin/env python3
"""
Arcoa Nexus CPU Test Module (Enhanced)

This module implements comprehensive CPU testing with enhanced metrics,
error handling, and cross-platform compatibility.
"""
import datetime
import math
import multiprocessing
import platform
import time
from typing import Dict, Any, List, Optional, Tuple, Callable

import psutil

from agent.tests.test_framework import (
    TestCategory, TestResult, TestSeverity, TestStatus, test
)


def _get_cpu_temperatures() -> Optional[List[float]]:
    """Retrieve CPU-related temperatures from all available sensors."""
    try:
        sensors = psutil.sensors_temperatures()
        temps = []
        for sensor_name, entries in sensors.items():
            for entry in entries:
                label_lower = entry.label.lower()
                if any(key in label_lower for key in ['cpu', 'core', 'package']):
                    temps.append(entry.current)
        return temps if temps else None
    except Exception as e:
        return None


def _calculate_stats(data: List[float]) -> Dict[str, float]:
    """Calculate statistical metrics for a dataset."""
    if not data:
        return {}
    n = len(data)
    mean = sum(data) / n
    variance = sum((x - mean) ** 2 for x in data) / n
    return {
        "avg": mean,
        "min": min(data),
        "max": max(data),
        "std_dev": math.sqrt(variance)
    }


@test(
    category=TestCategory.CPU,
    severity=TestSeverity.HIGH,
    description="Comprehensive CPU test with detailed metrics and thermal analysis"
)
def run_basic_cpu_test(duration_sec: int = 10, log_callback: Optional[Callable] = None) -> Dict[str, Any]:
    """Run an enhanced CPU test with detailed performance monitoring."""
    start_dt = datetime.datetime.now()
    metrics = {
        "load_periods": [],
        "frequencies": [],
        "temps": [],
        "timestamps": []
    }

    # Initial system checks
    logical_cores = psutil.cpu_count(logical=True)
    physical_cores = psutil.cpu_count(logical=False)
    cpu_info = {
        "logical_cores": logical_cores,
        "physical_cores": physical_cores,
        "arch": platform.machine(),
        "model": platform.processor()
    }

    for i in range(duration_sec):
        try:
            # Collect CPU metrics
            cpu_load = psutil.cpu_percent(interval=1.0, percpu=True)
            freq = psutil.cpu_freq().current if hasattr(psutil, "cpu_freq") else None
            temps = _get_cpu_temperatures()

            metrics["load_periods"].append(cpu_load)
            metrics["frequencies"].append(freq)
            metrics["temps"].append(temps)
            metrics["timestamps"].append(datetime.datetime.now().isoformat())

            if log_callback:
                progress = (i + 1) / duration_sec * 100
                log_callback(f"Monitoring CPU ({i+1}/{duration_sec}s) | "
                             f"Load: {sum(cpu_load)/len(cpu_load):.1f}% | "
                             f"Freq: {freq or 'N/A'} MHz | "
                             f"Temp: {max(temps) if temps else 'N/A'}°C", 
                             "info")
        except Exception as e:
            if log_callback:
                log_callback(f"Error collecting metrics: {str(e)}", "error")

    # Calculate detailed statistics
    freq_data = [f for f in metrics["frequencies"] if f is not None]
    temp_data = [t for ts in metrics["temps"] if ts for t in ts]
    
    test_details = {
        "status": TestStatus.PASS.value,
        "cpu_info": cpu_info,
        "load_stats": {
            "per_core": [
                _calculate_stats([period[i] for period in metrics["load_periods"]])
                for i in range(len(metrics["load_periods"][0]))
            ] if metrics["load_periods"] else [],
            "system_avg": psutil.getloadavg() if hasattr(psutil, "getloadavg") else []
        },
        "frequency_stats": _calculate_stats(freq_data) if freq_data else {},
        "temperature_stats": _calculate_stats(temp_data) if temp_data else {},
        "thermal_throttling": False,
        "notes": f"Enhanced CPU test over {duration_sec} seconds",
        "raw_metrics": metrics
    }

    # Thermal throttling analysis
    if freq_data and temp_data:
        max_freq = max(freq_data)
        high_temp_threshold = 85  # Configurable temperature threshold
        high_temp_indices = [i for i, ts in enumerate(metrics["temps"]) 
                            if ts and max(ts) >= high_temp_threshold]
        high_temp_freqs = [metrics["frequencies"][i] for i in high_temp_indices 
                          if i < len(metrics["frequencies"]) and metrics["frequencies"][i]]
        
        if high_temp_freqs:
            avg_high_temp_freq = sum(high_temp_freqs) / len(high_temp_freqs)
            if avg_high_temp_freq < max_freq * 0.95:
                test_details["thermal_throttling"] = True
                test_details["notes"] += "; Thermal throttling suspected"

    return {
        "test_details": test_details,
        "started_at": start_dt.isoformat(),
        "finished_at": datetime.datetime.now().isoformat()
    }


def _cpu_stress_task(end_time: float):
    """CPU-intensive workload for stress testing."""
    try:
        while time.time() < end_time:
            # Matrix multiplication stress pattern
            matrix_size = 100
            matrix = [[i * j for j in range(matrix_size)] for i in range(matrix_size)]
            result = [[sum(a*b for a,b in zip(row, col)) for col in zip(*matrix)] 
                     for row in matrix]
    except KeyboardInterrupt:
        pass


@test(
    category=TestCategory.CPU,
    severity=TestSeverity.CRITICAL,
    description="Advanced CPU stress test with multi-process load generation"
)
def run_cpu_stress_test(duration_sec: int = 30, log_callback: Optional[Callable] = None) -> Dict[str, Any]:
    """Execute a multi-process CPU stress test with real-time monitoring."""
    start_dt = datetime.datetime.now()
    metrics = {
        "load_periods": [],
        "frequencies": [],
        "temps": [],
        "timestamps": []
    }

    # Start stress processes
    end_time = time.time() + duration_sec
    processes = []
    for _ in range(multiprocessing.cpu_count()):
        proc = multiprocessing.Process(target=_cpu_stress_task, args=(end_time,))
        proc.start()
        processes.append(proc)

    # Monitoring loop
    try:
        while time.time() < end_time:
            try:
                cpu_load = psutil.cpu_percent(interval=1, percpu=True)
                freq = psutil.cpu_freq().current if hasattr(psutil, "cpu_freq") else None
                temps = _get_cpu_temperatures()

                metrics["load_periods"].append(cpu_load)
                metrics["frequencies"].append(freq)
                metrics["temps"].append(temps)
                metrics["timestamps"].append(datetime.datetime.now().isoformat())

                if log_callback:
                    elapsed = int(time.time() - start_dt.timestamp())
                    log_callback(f"Stress test ({elapsed}s/{duration_sec}s) | "
                                f"Load: {sum(cpu_load)/len(cpu_load):.1f}% | "
                                f"Freq: {freq or 'N/A'} MHz | "
                                f"Max Temp: {max(temps) if temps else 'N/A'}°C",
                                "info")
            except Exception as e:
                if log_callback:
                    log_callback(f"Monitoring error: {str(e)}", "error")
    finally:
        for proc in processes:
            if proc.is_alive():
                proc.terminate()
        for proc in processes:
            proc.join()

    # Generate comprehensive report
    freq_data = [f for f in metrics["frequencies"] if f is not None]
    temp_data = [t for ts in metrics["temps"] if ts for t in ts]
    
    test_details = {
        "status": TestStatus.PASS.value,
        "load_stats": {
            "per_core": [
                _calculate_stats([period[i] for period in metrics["load_periods"]])
                for i in range(len(metrics["load_periods"][0]))
            ] if metrics["load_periods"] else [],
            "system_avg": psutil.getloadavg() if hasattr(psutil, "getloadavg") else []
        },
        "frequency_stats": _calculate_stats(freq_data) if freq_data else {},
        "temperature_stats": _calculate_stats(temp_data) if temp_data else {},
        "thermal_throttling": False,
        "notes": f"Advanced stress test completed ({duration_sec}s)",
        "raw_metrics": metrics
    }

    # Post-test analysis
    if freq_data and temp_data:
        max_freq = max(freq_data)
        high_temp_threshold = 85
        high_temp_indices = [i for i, ts in enumerate(metrics["temps"]) 
                            if ts and max(ts) >= high_temp_threshold]
        high_temp_freqs = [metrics["frequencies"][i] for i in high_temp_indices 
                          if i < len(metrics["frequencies"]) and metrics["frequencies"][i]]
        
        if high_temp_freqs and (sum(high_temp_freqs)/len(high_temp_freqs) < max_freq * 0.93):
            test_details["thermal_throttling"] = True
            test_details["notes"] += "; Significant thermal throttling detected"

    return {
        "test_details": test_details,
        "started_at": start_dt.isoformat(),
        "finished_at": datetime.datetime.now().isoformat()
    }
