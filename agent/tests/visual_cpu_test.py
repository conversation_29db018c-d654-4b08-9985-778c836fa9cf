#!/usr/bin/env python3
"""
Arcoa Nexus Visual CPU Test (Tkinter Visual)

A robust, visual (Tkinter) CPU stress test for diagnostics. Stresses all logical cores,
shows live progress and CPU load, and displays result. Uses multiprocessing for better CPU utilization.
"""
import time
import datetime
import threading
import multiprocessing
import psutil
import tkinter as tk
from tkinter import ttk
from typing import Dict, Any, Optional, Callable

from agent.tests.test_framework import (
    TestCategory, TestSeverity, TestStatus, test
)

def _cpu_stress_worker(stop_event):
    x = 1.0
    while not stop_event.is_set():
        for _ in range(1000):  # Increased intensity per loop
            x = (x * 1.000001 + 0.000001) % 1000000

class VisualCPUTestWindow(tk.Toplevel):
    def __init__(self, parent, duration_seconds: int, log_callback=None):
        super().__init__(parent)
        self.duration_seconds = duration_seconds
        self.log_callback = log_callback
        self.start_time = None
        self.cpu_loads = []
        self.stop_event = multiprocessing.Event()  # Changed to multiprocessing.Event
        self.workers = []
        self.num_cores = psutil.cpu_count(logical=True) or 1
        self.monitor_thread = None  # Added for CPU monitoring
        self.title("CPU Stress Test")
        #self.geometry("500x250")
        self.attributes('-fullscreen', True)
        self.protocol("WM_DELETE_WINDOW", self._on_close)
        self._setup_ui()
        self._start_test()

    def _setup_ui(self):
        self.progress_var = tk.DoubleVar(value=0)
        self.progress = ttk.Progressbar(self, variable=self.progress_var, maximum=100)
        self.progress.pack(fill=tk.X, padx=20, pady=20)

        self.cpu_label = tk.Label(self, text="CPU Load: 0%", font=("Arial", 14))
        self.cpu_label.pack(pady=10)

        self.time_label = tk.Label(self, text=f"Time left: {self.duration_seconds}s", font=("Arial", 12))
        self.time_label.pack(pady=5)

        self.status_label = tk.Label(self, text="", font=("Arial", 14, "bold"))
        self.status_label.pack(pady=10)

        self.close_btn = ttk.Button(self, text="Close", command=self._on_close, state=tk.DISABLED)
        self.close_btn.pack(pady=10)

    def _start_test(self):
        self.start_time = time.time()
        # Start worker processes instead of threads
        for _ in range(self.num_cores):
            p = multiprocessing.Process(target=_cpu_stress_worker, args=(self.stop_event,))
            p.start()
            self.workers.append(p)
        # Start CPU monitoring thread
        self.monitor_thread = threading.Thread(target=self._monitor_cpu)
        self.monitor_thread.start()
        self._update_ui(0)

    def _monitor_cpu(self):
        """Monitor CPU load every 0.5 seconds."""
        while not self.stop_event.is_set():
            load = psutil.cpu_percent(interval=0.5)  # Average over 0.5s
            self.cpu_loads.append(load)

    def _update_ui(self, elapsed):
        if elapsed >= self.duration_seconds:
            self._finish_test()
            return
        # Show the latest CPU load if available
        if self.cpu_loads:
            load = self.cpu_loads[-1]
        else:
            load = 0
        percent = (elapsed / self.duration_seconds) * 100
        self.progress_var.set(percent)
        self.cpu_label.config(text=f"CPU Load: {load:.1f}%")
        self.time_label.config(text=f"Time left: {self.duration_seconds - elapsed}s")
        self.after(500, lambda: self._update_ui(int(time.time() - self.start_time)))

    def _finish_test(self):
        self.stop_event.set()
        self.monitor_thread.join()  # Wait for monitoring to stop
        for p in self.workers:
            p.join()  # Wait for all processes to stop
        avg_load = sum(self.cpu_loads) / len(self.cpu_loads) if self.cpu_loads else 0
        max_load = max(self.cpu_loads) if self.cpu_loads else 0
        status = TestStatus.PASS if avg_load > 70 else TestStatus.FAIL
        result = "PASS" if status == TestStatus.PASS else "FAIL"
        self.status_label.config(text=f"Test {result} | Avg: {avg_load:.1f}% | Max: {max_load:.1f}%")
        self.close_btn.config(state=tk.NORMAL)
        if self.log_callback:
            self.log_callback(f"CPU Visual Test complete: {result} (Avg: {avg_load:.1f}%)")
        self.result = {
            "status": status.value,
            "notes": f"Visual CPU test complete. Avg: {avg_load:.1f}%, Max: {max_load:.1f}%",
            "avg_cpu_load": avg_load,
            "max_cpu_load": max_load,
            "duration_seconds": self.duration_seconds,
            "cpu_load_samples": self.cpu_loads,
            "num_cores": self.num_cores,
        }

    def _on_close(self):
        self.stop_event.set()
        self.destroy()

@test(
    category=TestCategory.CPU,
    severity=TestSeverity.HIGH,
    description="Visual CPU stress test with Tkinter progress bar and live load"
)
def run_visual_cpu_test(
    parent_window=None,
    duration_seconds: int = 15,
    log_callback: Optional[Callable[[str], None]] = None
) -> Dict[str, Any]:
    """
    Run a visual CPU stress test with a Tkinter window.
    Args:
        parent_window: Optional parent window (or None)
        duration_seconds: Test duration
        log_callback: Optional callback for progress
    Returns:
        dict with status, metrics, and timing
    """
    import tkinter as tk
    root = parent_window or tk.Tk()
    root.withdraw()  # Hide the main window if created here
    start_dt = datetime.datetime.now()
    win = VisualCPUTestWindow(root, duration_seconds, log_callback)
    win.grab_set()
    root.wait_window(win)
    end_dt = datetime.datetime.now()
    result = getattr(win, 'result', {
        "status": TestStatus.ERROR.value,
        "notes": "Test window closed unexpectedly"
    })
    return {
        "test_details": result,
        "started_at": start_dt.isoformat(),
        "finished_at": end_dt.isoformat()
    }