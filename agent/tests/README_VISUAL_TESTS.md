# Visual CPU and RAM Tests

This document describes the visual CPU and RAM tests that provide a full-screen, interactive testing experience with real-time progress visualization.

## Overview

The visual tests provide the following features:

- Full-screen UI with progress bars and real-time statistics
- Threaded implementation to prevent UI freezing
- Configurable test duration and memory size
- Detailed visualization of CPU and memory usage
- Ability to start, stop, and close tests at any time

## Running the Tests

### From the Main Application

1. Launch the main Arcoa Nexus Diagnostics application
2. In the "Tests" section, look for the "Visual Tests" group
3. Check either "Visual CPU Test" or "Visual RAM Test" (or both)
4. Click "Run Tests" to start the selected tests

### Using the Test Launcher

A standalone test launcher is provided for convenience:

```bash
python run_visual_tests.py
```

This will open a simple launcher UI where you can select the test type and parameters.

### Direct Command Line

You can also run the tests directly from the command line:

```bash
# Run the visual CPU test
python run_visual_tests.py --cpu --duration 60

# Run the visual RAM test
python run_visual_tests.py --ram --ram-size 2048 --duration 45
```

## Test Parameters

### Visual CPU Test

- **Duration**: How long to run the CPU stress test (in seconds)
  - Default: 30 seconds
  - Configurable via `visual_cpu_duration` setting

### Visual RAM Test

- **Memory Size**: How much memory to test (in MB)
  - Default: 1024 MB (1 GB)
  - Configurable via `visual_ram_size_mb` setting
- **Duration**: How long to run the RAM test (in seconds)
  - Default: 30 seconds
  - Configurable via `visual_ram_duration` setting

## Configuration

Test parameters can be configured in several ways:

1. Environment variables:
   ```bash
   export ARCOA_VISUAL_CPU_DURATION=60
   export ARCOA_VISUAL_RAM_SIZE_MB=2048
   export ARCOA_VISUAL_RAM_DURATION=45
   ```

2. Editing the `agent/tests/test_config.py` file:
   ```python
   DEFAULT_SETTINGS = {
       # ...
       "visual_cpu_duration": 30,     # Duration of visual CPU test in seconds
       "visual_ram_size_mb": 1024,    # Default visual RAM test size in MB
       "visual_ram_duration": 30,     # Duration of visual RAM test in seconds
       # ...
   }
   ```

## Test Details

### Visual CPU Test

The CPU test performs the following operations:

1. Collects CPU information (model, cores, threads)
2. Launches stress processes for each CPU core
3. Monitors CPU usage and temperature in real-time
4. Displays per-core utilization with progress bars
5. Reports final results including average and maximum load

### Visual RAM Test

The RAM test performs the following operations:

1. Collects memory information (total, available)
2. Allocates memory blocks of configurable size
3. Writes and verifies different test patterns
4. Displays real-time progress and memory speed
5. Reports final results including any detected errors

## Test Results

Both tests provide detailed results that include:

- Test status (PASS/FAIL)
- Detailed metrics (CPU load, memory speed, etc.)
- Error information if any issues were detected
- Duration and timing information

These results are included in the test reports sent to the server.
