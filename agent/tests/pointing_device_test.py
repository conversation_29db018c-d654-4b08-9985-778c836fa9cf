#!/usr/bin/env python3
"""
Arcoa Nexus Modern Pointing Device Test

Enhanced touchpad and pointer stick test with modern dark UI and image-based guidance.
"""
import os
import datetime
import tkinter as tk
from tkinter import ttk
from typing import Dict, Any, Callable, Optional
from agent.gui.theme import COLORS, FONTS, SIZES

from agent.tests.test_framework import (
    TestCategory, TestSeverity, TestStatus, test
)


class TestUIConfig:
    """Namespace for UI configuration values for StreamlinedPointingTest."""
    # Values from create_test_elements_now
    title_y_offset: int = 20
    circle_radius: int = 40
    circle_center_y: int = 100  # Absolute Y position for circle center
    pointer_circle_outline_width: int = 2

    touchpad_top_y: int = 300  # Absolute Y position for touchpad top
    touchpad_height: int = 180
    touchpad_width: int = 400
    touchpad_outline_width: int = 2

    button_height: int = 50
    button_width: int = 130
    button_spacing_divisor: int = 3  # touchpad_width will be divided by this
    button_outline_width: int = 2
    
    # Offset from the edge of the touchpad (center of button to edge of touchpad)
    button_y_offset_above_touchpad: int = 5 
    button_y_offset_below_touchpad: int = 5

    # Offset from the bottom of the canvas for instructions.
    # Original calculation: canvas_height - (canvas_height * 0.15) // 2 - 60
    # This is relative to a calculated point, let's use the fixed part.
    instruction_y_offset_from_bottom_ref_point: int = 60 

    done_button_width: int = 150
    done_button_height: int = 50
    done_button_padding_from_edge: int = 130
    done_button_outline_width: int = 3

    # Values from exit_fullscreen
    exit_fullscreen_width: int = 1024
    exit_fullscreen_height: int = 768
    # For future use if reference screen dimensions are available at init
    # exit_fullscreen_width_ratio: float = 1200 / 1920 
    # exit_fullscreen_height_ratio: float = 900 / 1080


class StreamlinedPointingTest(tk.Toplevel):
    """Modern test for touchpad and pointer stick functionality with dark UI."""
    def __init__(self, parent, log_callback: Optional[Callable] = None):
        super().__init__(parent)
        self.parent = parent
        self.log_callback = log_callback
        self.config = TestUIConfig() # Instantiate the config
        self.result_callback = None # Initialize result_callback
        self.title("Touchpad & Pointer Test")
        
        self.configure(bg=COLORS["pointing_test_bg"])
        self.attributes('-fullscreen', True)
        self.bind('<Escape>', self.exit_fullscreen)

        self.update_idletasks()
        self.screen_width = self.winfo_screenwidth()
        self.screen_height = self.winfo_screenheight()
        self.log(f"Full screen mode enabled: {self.screen_width}x{self.screen_height}")

        # NEW: Centralized configuration and state for all testable hardware elements
        self.test_elements_config = {
            "Pointer Movement": {"type": "movement", "status": TestStatus.NOT_APPLICABLE, "ui_indicator_key": "P"},
            "Touchpad Targets": {"type": "touchpad_area", "status": TestStatus.NOT_APPLICABLE, "ui_indicator_key": "TP",
                                 "sub_elements": {"top": False, "right": False, "bottom": False, "left": False}},
            "Pointer Circle": {"type": "pointer_stick_area", "status": TestStatus.NOT_APPLICABLE, "ui_indicator_key": "P", # Shares indicator with movement for now
                               "sub_elements": {"entered": False, "exited": False}},
            "Left Button": {"type": "button", "event_num_std": 1, "status": TestStatus.NOT_APPLICABLE, "ui_indicator_key": "left"},
            "Middle Button": {"type": "button", "event_num_std": 2, "status": TestStatus.NOT_APPLICABLE, "ui_indicator_key": "middle"},
            "Right Button": {"type": "button", "event_num_std": 3, "status": TestStatus.NOT_APPLICABLE, "ui_indicator_key": "right"},
            "Scroll Up": {"type": "scroll", "event_num_linux": 4, "status": TestStatus.NOT_APPLICABLE, "ui_indicator_key": "scroll_up"}, # Placeholder key
            "Scroll Down": {"type": "scroll", "event_num_linux": 5, "status": TestStatus.NOT_APPLICABLE, "ui_indicator_key": "scroll_down"}, # Placeholder key
            "Back Button": {"type": "xbutton", "event_num_std": 8, "status": TestStatus.NOT_APPLICABLE, "ui_indicator_key": "back"}, # Placeholder key, Tkinter event <Button-8>
            "Forward Button": {"type": "xbutton", "event_num_std": 9, "status": TestStatus.NOT_APPLICABLE, "ui_indicator_key": "forward"} # Placeholder key, Tkinter event <Button-9>
        }

        # These provide detailed tracking for complex elements; their completion updates the main status in test_elements_config
        self.touchpad_squares_details = self.test_elements_config["Touchpad Targets"]["sub_elements"]
        self.pointer_circle_details = self.test_elements_config["Pointer Circle"]["sub_elements"]
        
        self.force_completed = False
        self._is_finishing = False  # Flag to prevent re-entry into finish()
        self._focus_after_id = None # To store the ID of the after call for focus_force
        self.final_results = {"status": TestStatus.ERROR.value, "notes": "Test did not initialize or complete properly.", "details": {}} # Default results
        self.progress_var = tk.StringVar(value="Initializing test...") # Initialize progress_var here

        # Renamed self.button_states to self.ui_button_states to clarify it's for UI drawn buttons,
        # not physical mouse buttons whose states are now in self.test_elements_config.
        self.ui_button_states = {
            "left_top": 0,
            "middle_top": 0,
            "right_top": 0,
            "left_bottom": 0,
            "middle_bottom": 0,
            "right_bottom": 0
        }
        
        # Mapping for standard mouse button event numbers to our config keys (physical buttons)
        # This helps on_button_click to identify which element to update.
        self.physical_button_event_to_key_map = {
            1: "Left Button",
            2: "Middle Button",
            3: "Right Button",
            # Linux scroll events (4, 5) and XButtons (e.g., 8, 9) will be handled by specific bindings
        }

        # self.buttons_tested set is likely deprecated by the new status system.
        # self.touchpad_used is deprecated.
        # self.button_click_types is deprecated by info in test_elements_config.
        
        self.button_backgrounds = {}
        self.touchpad_targets = {}
        self.pointer_circle = None

        self.images = {}
        self.load_images()
        self.setup_ui()
        self.transient(parent)
        self.grab_set()
        self.focus_force()
        self.lift()
        # self.after(100, self.focus_force)
        self._focus_after_id = self.after(100, self.focus_force) # Store the ID
        self.log("Pointing Device Test Started - Test available features and click DONE when finished.")
        self.center_cursor()
        self.check_progress() # This will need updates to read from test_elements_config

    def load_images(self):
        """Load image resources for the test UI."""
        try:
            # Get the base directory
            base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            images_dir = os.path.join(base_dir, "pictures")

            # Define image paths
            image_files = {
                "touchpad": os.path.join(images_dir, "touchpad.png"),
                "touchstick": os.path.join(images_dir, "touchstick.png"),
                "left_button": os.path.join(images_dir, "click_button.png"),
                "middle_button": os.path.join(images_dir, "middle_button.png"),
                "right_button": os.path.join(images_dir, "click_button.png")
            }

            # Load images
            for name, path in image_files.items():
                if os.path.exists(path):
                    self.images[name] = tk.PhotoImage(file=path)
                    # Resize if needed
                    # self.images[name] = self.images[name].subsample(2, 2)
                else:
                    self.log(f"Warning: Image not found: {path}", "warning")
        except Exception as e:
            self.log(f"Error loading images: {str(e)}", "error")

    def log(self, message, level="info"):
        """Log a message using the callback if available"""
        if self.log_callback:
            self.log_callback(message, level)

    def setup_ui(self):
        """Set up a modern dark theme UI for the test."""
        # Style for a clean, modern look
        style = ttk.Style()
        style.configure("TFrame", background="#121212")
        style.configure("TLabel", background="#121212", foreground="#FFFFFF", font=(FONTS["pointing_test_font"]))
                # WARNING: Do NOT use the global 'TButton' style in test windows. Use a unique style name to avoid polluting the main app theme.
        style.configure("PointTest.TButton", background="#FF5B1C", foreground="#FFFFFF", font=(FONTS["pointing_test_font"]))
        style.configure("Status.TLabel", background="#121212", foreground="#00FF00", font=(FONTS["pointing_test_status_font"]))
        style.configure("Success.TLabel", background="#121212", foreground="#00FF00", font=(FONTS["pointing_test_status_font"]))
        
        # Main frame
        main_frame = ttk.Frame(self)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create a canvas that fills the frame
        self.canvas = tk.Canvas(
            main_frame,
            bg="#121212",
            highlightthickness=0
        )
        self.canvas.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Draw test elements after the window is fully created
        self.after(100, self.create_test_elements_now)

        # Mouse events on canvas for position tracking
        self.canvas.bind("<Motion>", self.on_motion)
        
        # Set up click events for different mouse buttons
        # For each button type, bind the appropriate mouse button event
        self.canvas.bind("<Button-1>", lambda e: self.on_button_click(e, 1))  # Left click
        self.canvas.bind("<Button-2>", lambda e: self.on_button_click(e, 2))  # Middle click
        self.canvas.bind("<Button-3>", lambda e: self.on_button_click(e, 3))  # Right click

        # Bind scroll events
        self.canvas.bind("<Button-4>", lambda e: self.on_scroll_event(e, "Scroll Up")) # Linux Scroll Up
        self.canvas.bind("<Button-5>", lambda e: self.on_scroll_event(e, "Scroll Down")) # Linux Scroll Down
        self.canvas.bind("<MouseWheel>", self.on_mouse_wheel_event) # General scroll for Windows/macOS

        # Bind XButton events (commonly Back/Forward)
        try:
            self.canvas.bind("<Button-8>", lambda e: self.on_xbutton_event(e, "Back Button"))
            self.log("Binding for <Button-8> (Back Button) successful.")
        except tk.TclError:
            self.log("Could not bind <Button-8> (Back Button). This button may not be supported on this system or mouse.", "warning")
            # Optionally, mark 'Back Button' as N/A if binding fails and it's critical
            # self.test_elements_config["Back Button"]["status"] = TestStatus.NOT_APPLICABLE

        try:
            self.canvas.bind("<Button-9>", lambda e: self.on_xbutton_event(e, "Forward Button"))
            self.log("Binding for <Button-9> (Forward Button) successful.")
        except tk.TclError:
            self.log("Could not bind <Button-9> (Forward Button). This button may not be supported on this system or mouse.", "warning")
            # Optionally, mark 'Forward Button' as N/A
            # self.test_elements_config["Forward Button"]["status"] = TestStatus.NOT_APPLICABLE

        # Status indicators at bottom
        status_frame = ttk.Frame(main_frame)
        status_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=10)
        
        # Direction status indicators
        self.direction_status = {}
        for i, direction in enumerate(["top", "right", "bottom", "left"]):
            self.direction_status[direction] = ttk.Label(
                status_frame,
                text=f"{direction[0].upper()}: •",
                style="Status.TLabel"
            )
            self.direction_status[direction].pack(side=tk.LEFT, padx=5)
            
        # Touchpad status indicator
        self.touchpad_status = ttk.Label(
            status_frame,
            text="TP: •",
            style="Status.TLabel"
        )
        self.touchpad_status.pack(side=tk.LEFT, padx=5)

        # Button status
        self.button_status = {}
        for button in ["left", "middle", "right"]:
            self.button_status[button] = ttk.Label(
                status_frame,
                text=f"{button[0].upper()}: •",
                style="Status.TLabel"
            )
            self.button_status[button].pack(side=tk.LEFT, padx=5)

        # Pointer stick status
        self.pointer_status = ttk.Label(
            status_frame,
            text="P: •",
            style="Status.TLabel"
        )
        self.pointer_status.pack(side=tk.LEFT, padx=5)

        # Scroll and XButton status indicators
        self.scroll_status = {}
        for scroll_dir_key in ["scroll_up", "scroll_down"]:
            element_name = "Scroll Up" if scroll_dir_key == "scroll_up" else "Scroll Down"
            # Use a simpler label for now, actual text will be updated by check_progress
            label_text = f"{element_name[0:2].upper()}{element_name[-2:].upper()[0]}: •" 
            self.scroll_status[scroll_dir_key] = ttk.Label(
                status_frame,
                text=label_text, # e.g., SU: •, SD: •
                style="Status.TLabel"
            )
            self.scroll_status[scroll_dir_key].pack(side=tk.LEFT, padx=5)

        self.xbutton_status = {}
        for xbutton_key in ["back", "forward"]:
            element_name = "Back Button" if xbutton_key == "back" else "Forward Button"
            label_text = f"{element_name[0:2].upper()}: •"
            self.xbutton_status[xbutton_key] = ttk.Label(
                status_frame,
                text=label_text, # e.g., BA: •, FO: •
                style="Status.TLabel"
            )
            self.xbutton_status[xbutton_key].pack(side=tk.LEFT, padx=5)

        # Skip button
        self.skip_pointer_button = ttk.Button(
            status_frame,
            text="Skip",
            command=self.skip_pointer_test,
            width=5
        )
        self.skip_pointer_button.pack(side=tk.LEFT, padx=5)

        # Progress indicator
        self.progress_var = tk.StringVar(value="Test available features and click DONE when finished.")
        status_label = ttk.Label(
            status_frame, 
            textvariable=self.progress_var,
            font=(FONTS["pointing_test_font"])
        )
        status_label.pack(side=tk.LEFT, padx=5, pady=5, fill=tk.X, expand=True)
        
        # Done button (initially enabled)
        # Create a dedicated frame for the Done button to ensure visibility
        done_frame = ttk.Frame(main_frame, style="TFrame")
        done_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=5)
        
        self.done_button = ttk.Button(
            done_frame,
            text="DONE - TEST COMPLETE",
            command=self.finish,
            state="normal", # Explicitly ensure it's normal
            width=25  # Make button wider
        )
        self.done_button.pack(side=tk.RIGHT, padx=20, pady=5)
        self.done_button.config(state=tk.NORMAL) # Double ensure state is normal

        # Force button
        self.force_button = ttk.Button(
            status_frame,
            text="Force",
            width=5,
            command=lambda: self.finish(force_complete=True)
        )
        self.force_button.pack(side=tk.RIGHT, padx=5)

    def create_test_elements_now(self):
        """Create a test UI based on the mockup layout with proper full-screen positioning."""
        # Use actual screen dimensions for layout calculation
        canvas_width = self.winfo_width() or self.screen_width
        canvas_height = self.winfo_height() or self.screen_height
        
        # Ensure canvas is properly sized for full screen
        self.canvas.configure(width=canvas_width, height=canvas_height)

        # Log that we're creating elements
        self.log(f"Creating full-screen test elements: {canvas_width}x{canvas_height}")

        # We're keeping only center position for reference
        center_x = canvas_width / 2
        
        # We still need touchpad_targets dictionary for tracking, but not drawing the squares
        # Initialize an empty dictionary as we're not drawing actual targets
        self.touchpad_targets = {}
        
        # Add instructions at the top explaining the test
        title_y = self.config.title_y_offset
        self.canvas.create_text(
            center_x, title_y,
            text="Touchpad & Button Test",
            fill="#FFFFFF",
            font=(FONTS["pointing_test_font"]),
            justify=tk.CENTER
        )
        self.log(f"Title created at x={center_x}, y={title_y}")

        # ===== MIDDLE SECTION: Pointer Stick Target =====
        # Create red circle in center for pointer stick
        circle_radius = self.config.circle_radius
        # Use circle_center_y from config, which is an absolute Y.
        # If it were a percentage: circle_y = canvas_height * self.config.circle_center_y_percentage
        circle_y = self.config.circle_center_y
        
        # Draw red circle with border
        self.pointer_circle = self.canvas.create_oval(
            center_x - circle_radius, circle_y - circle_radius,
            center_x + circle_radius, circle_y + circle_radius,
            fill="#CC3333", outline="#FFFFFF", width=self.config.pointer_circle_outline_width
        )
        self.log(f"Pointer circle created at x={center_x}, y={circle_y} with radius={circle_radius}")

        # Add minimal pointer stick instructions
        self.canvas.create_text(
            center_x, circle_y,
            text="≡",  # Center symbol to represent pointer stick
            fill="#FFFFFF",
            font=(FONTS["pointing_test_font"]),
            justify=tk.CENTER
        )

        # ===== BOTTOM SECTION: Touchpad and Buttons =====
        # Draw touchpad area at the bottom
        # Use touchpad_top_y from config, which is an absolute Y.
        # If it were a percentage: touchpad_top = canvas_height * self.config.touchpad_top_y_percentage
        touchpad_top = self.config.touchpad_top_y
        touchpad_height = self.config.touchpad_height
        touchpad_width = self.config.touchpad_width # Using fixed width from config
        
        touchpad_bottom = touchpad_top + touchpad_height
        touchpad_left = center_x - touchpad_width / 2
        touchpad_right = center_x + touchpad_width / 2
        
        # Draw touchpad background
        self.touchpad_area = self.canvas.create_rectangle(
            touchpad_left, touchpad_top,
            touchpad_right, touchpad_bottom,
            fill="#222222", outline="#444444", width=self.config.touchpad_outline_width, tags="touchpad"
        )
        
        # Store touchpad boundaries for hit detection
        self.touchpad_bounds = (touchpad_left, touchpad_top, touchpad_right, touchpad_bottom)
        self.log(f"Touchpad area created with bounds: {self.touchpad_bounds}")
        
        # Top row of buttons (above touchpad)
        button_height = self.config.button_height
        button_width = self.config.button_width
        # Calculate Y position for buttons above touchpad
        button_y_above = touchpad_top - self.config.button_y_offset_above_touchpad - (button_height / 2)
        
        button_spacing = touchpad_width / self.config.button_spacing_divisor

        # Create buttons above touchpad
        button_types = ["left", "middle", "right"]
        self.button_areas = {}
        
        for i, button_type in enumerate(button_types):
            # Calculate position
            x = touchpad_left + (i + 0.5) * button_spacing
            y = button_y_above # Use calculated Y for buttons above touchpad
            
            # Button boundaries
            x1 = x - button_width / 2
            y1 = y - button_height / 2
            x2 = x + button_width / 2
            y2 = y + button_height / 2
            
            # Store for click detection
            self.button_areas[f"{button_type}_top"] = (x1, y1, x2, y2)
            self.log(f"Button '{button_type}_top' area: {self.button_areas[f'{button_type}_top']}")
            
            # Draw button and store reference
            button_bg = self.canvas.create_rectangle(
                x1, y1, x2, y2,
                fill="#222222", outline="#FFFFFF", width=self.config.button_outline_width
            )
            
            # Store reference to button for later highlighting
            self.button_backgrounds[f"{button_type}_top"] = button_bg
            
            # Add label
            self.canvas.create_text(
                x, y,
                text=f"{button_type.upper()} CLICK",
                fill="#FFFFFF",
                font=(FONTS["pointing_test_font"])
            )

        # Bottom row of buttons (below touchpad)
        # Calculate Y position for buttons below touchpad
        button_y_below = touchpad_bottom + self.config.button_y_offset_below_touchpad + (button_height / 2)
        
        # Proportional padding for bottom elements to avoid overlap with status bar or going off-screen
        # This was a fixed calculation before: bottom_padding = canvas_height * 0.15
        # We ensure bottom_button_y does not exceed canvas_height minus some padding.
        # Let's use a more direct approach for the instruction_y and done_button_y based on config.
        
        # Create buttons below touchpad
        for i, button_type in enumerate(button_types):
            # Calculate position
            x = touchpad_left + (i + 0.5) * button_spacing
            y = button_y_below # Use calculated Y for buttons below touchpad
            
            # Button boundaries
            x1 = x - button_width / 2
            y1 = y - button_height / 2
            x2 = x + button_width / 2
            y2 = y + button_height / 2
            
            # Store for click detection
            self.button_areas[f"{button_type}_bottom"] = (x1, y1, x2, y2)
            self.log(f"Button '{button_type}_bottom' area: {self.button_areas[f'{button_type}_bottom']}")
            
            # Draw button with visible outline and store reference
            button_bg = self.canvas.create_rectangle(
                x1, y1, x2, y2,
                fill="#222222", outline="#FFFFFF", width=self.config.button_outline_width
            )
            
            # Store reference to button for later highlighting
            self.button_backgrounds[f"{button_type}_bottom"] = button_bg
            
            # Add label
            self.canvas.create_text(
                x, y,
                text=f"{button_type.upper()} CLICK",
                fill="#FFFFFF",
                font=(FONTS["pointing_test_font"])
            )
            
        # Add instructions at very bottom but ensure they're visible
        # instruction_y = canvas_height - bottom_padding // 2 - 60 # Original
        # Using a direct offset from bottom for instruction text
        # self.config.instruction_y_offset_from_bottom_ref_point was 60.
        # A more robust way might be:
        # instruction_y = canvas_height - self.config.done_button_height - self.config.done_button_padding_from_edge - self.config.instruction_y_offset_from_done_button
        # For now, let's use the configured offset from bottom, assuming it's enough.
        # This was: canvas_height - (canvas_height * 0.15) // 2 - 60
        # Simplified: canvas_height * 0.925 - 60.
        # Let's try to position it above the done button area.
        done_button_overall_height_from_bottom = self.config.done_button_height + self.config.done_button_padding_from_edge
        instruction_y = canvas_height - done_button_overall_height_from_bottom - self.config.instruction_y_offset_from_bottom_ref_point

        self.canvas.create_text(
            center_x, instruction_y,
            text="Use the touchpad, click all buttons, and interact with the pointer stick",
            fill="#FFFFFF",
            font=(FONTS["pointing_test_font"]),
            justify=tk.CENTER
        )
        self.log(f"Instruction text created at y={instruction_y}")
        
        # Create a highly visible Done button directly on the canvas - positioned at bottom right
        done_button_w = self.config.done_button_width
        done_button_h = self.config.done_button_height
        done_padding = self.config.done_button_padding_from_edge
        
        # Position at bottom right with padding
        done_button_x = canvas_width - (done_button_w / 2) - done_padding
        done_button_y = canvas_height - (done_button_h / 2) - done_padding # Center Y of button
        
        self.canvas_done_button = self.canvas.create_rectangle(
            done_button_x - (done_button_w / 2), done_button_y - (done_button_h / 2),
            done_button_x + (done_button_w / 2), done_button_y + (done_button_h / 2),
            fill="#222222", outline="#FFFFFF", width=self.config.done_button_outline_width,
            state="normal"
        )
        self.log(f"Canvas Done button created at x={done_button_x}, y={done_button_y}, w={done_button_w}, h={done_button_h}")
        
        # Add Done text
        self.canvas_done_text = self.canvas.create_text(
            done_button_x, done_button_y,
            text="DONE - TEST COMPLETE",
            fill="#FFFFFF",
            font=(FONTS["pointing_test_font"]),
        )
        
        # Bind click event to canvas done button
        self.canvas.tag_bind(self.canvas_done_button, "<Button-1>", lambda e: self.finish())
        self.canvas.tag_bind(self.canvas_done_text, "<Button-1>", lambda e: self.finish())
        self.log("Event bindings for canvas Done button set.")

    def create_test_elements(self):
        """Legacy method - not used anymore"""
        pass

    def center_cursor(self):
        """Try to position cursor at center of canvas"""
        try:
            width = self.canvas.winfo_width()
            height = self.canvas.winfo_height()
            self.canvas.event_generate("<Motion>", warp=True, x=width//2, y=height//4)
        except Exception:
            pass  # If we can't move the cursor, that's okay

    def on_motion(self, event):
        """Handle all motion events on the canvas"""
        x, y = event.x, event.y

        # Record general pointer movement once
        if self.test_elements_config["Pointer Movement"]["status"] == TestStatus.NOT_APPLICABLE:
            self.test_elements_config["Pointer Movement"]["status"] = TestStatus.PASS
            self.log("Pointer Movement detected (general canvas motion).")
            # UI indicators will refresh when update_progress_status is called by other events
            # or by check_progress.

        # Check pointer stick interaction
        self.check_pointer_stick_target(x, y)

        # Check if mouse is over the touchpad area
        if hasattr(self, 'touchpad_bounds'):
            tx1, ty1, tx2, ty2 = self.touchpad_bounds
            if tx1 <= x <= tx2 and ty1 <= y <= ty2 and not self.test_elements_config["Touchpad Targets"]["status"] == TestStatus.PASS:
                self.test_elements_config["Touchpad Targets"]["status"] = TestStatus.PASS
                # Update touchpad status
                self.canvas.itemconfig(self.touchpad_area, outline="#00FF00", width=3)
                self.log("Touchpad area used")
                
                # Add a "Touchpad Used" indicator
                touchpad_center_x = (tx1 + tx2) / 2
                touchpad_center_y = (ty1 + ty2) / 2
                self.canvas.create_text(
                    touchpad_center_x, touchpad_center_y,
                    text="TOUCHPAD USED",
                    fill="#00FF00",
                    font=(FONTS["pointing_test_font"])
                )
                
                # Update status in the status bar if a touchpad status exists
                if hasattr(self, 'touchpad_status'):
                    self.touchpad_status.configure(
                        text="TP: ✓", 
                        foreground="#00FF00"
                    )
                
                # Check if test is complete
                self.update_progress_status()

    def check_touchpad_targets(self, x, y):
        """No longer checking individual targets since squares are removed.
        This method is kept for compatibility but doesn't do anything"""
        # We no longer check the touchpad targets since we've removed the squares
        # The touchpad movement is now checked only in the touchpad area
        pass

    def check_pointer_stick_target(self, x, y):
        """Check pointer stick circle interaction"""
        # Only check if the pointer_circle exists and we haven't already exited
        if self.pointer_circle is not None and not self.pointer_circle_details["exited"]:
            try:
                circle_coords = self.canvas.coords(self.pointer_circle)

                # If we have coordinates, check for interactions
                if circle_coords:
                    # Calculate center and radius
                    cx = (circle_coords[0] + circle_coords[2]) / 2
                    cy = (circle_coords[1] + circle_coords[3]) / 2
                    radius = (circle_coords[2] - circle_coords[0]) / 2

                    # Check if cursor is inside circle
                    dist_squared = (x - cx) ** 2 + (y - cy) ** 2
                    radius_squared = radius ** 2

                    # If inside circle, mark as entered
                    if dist_squared <= radius_squared:
                        self.pointer_circle_details["entered"] = True
                        self.canvas.itemconfig(self.pointer_circle, fill="#FF0000")  # Red when entered

                    # If previously entered and now outside, mark as exited
                    elif self.pointer_circle_details["entered"] and dist_squared > radius_squared:
                        self.pointer_circle_details["exited"] = True
                        self.canvas.itemconfig(self.pointer_circle, fill="#00FF00")  # Green when completed
                        self.test_elements_config["Pointer Circle"]["status"] = TestStatus.PASS
                        self.log("Pointer stick circle target completed")
                        self.update_progress_status()
            except Exception as e:
                # Log any errors with pointer circle (shouldn't happen with our fix)
                self.log(f"Error checking pointer circle: {str(e)}", "error")

    def on_button_click(self, event, click_type):
        """Handle mouse button clicks on the canvas to test physical buttons and interact with UI test elements."""
        x, y = event.x, event.y

        # --- Part 1: Record the physical button press --- 
        # Map the Tkinter event button number (click_type) to our config key
        physical_button_key = self.physical_button_event_to_key_map.get(click_type)
        
        if physical_button_key and physical_button_key in self.test_elements_config:
            # If this physical button hasn't failed, mark it as PASS.
            # If a button is later explicitly failed by the user via UI, that would override this.
            if self.test_elements_config[physical_button_key]["status"] != TestStatus.FAIL:
                 self.test_elements_config[physical_button_key]["status"] = TestStatus.PASS
            self.log(f"{physical_button_key} click detected.")
            # self.update_progress_status() # Call this at the end after UI interaction

        # --- Part 2: Interact with on-screen UI test elements (the drawn buttons) ---
        # This part remains largely the same, but uses self.ui_button_states
        # Iterate over self.button_areas to get the coordinates for hit detection
        for area_name, coords_tuple in self.button_areas.items():
            x1, y1, x2, y2 = coords_tuple # Correctly unpack coordinates
            if x1 <= x <= x2 and y1 <= y <= y2:
                # We've found a UI button that was clicked, now determine if it's the right type
                # The 'button_type' here refers to the *type of test* (e.g., 'left' click test area)
                ui_test_button_type = area_name.split('_')[0]  # 'left', 'middle', or 'right'
                
                # The 'expected_click' is the physical mouse button we expect the user to press 
                # when interacting with this specific UI test button.
                # For example, for the 'left_top' UI button, we expect a physical left click (event_num_std: 1).
                expected_physical_click_key = f"{ui_test_button_type.capitalize()} Button"
                expected_physical_event_num = self.test_elements_config[expected_physical_click_key].get("event_num_std")

                is_correct_physical_click = (click_type == expected_physical_event_num)
                
                current_ui_state = self.ui_button_states[area_name]
                
                if current_ui_state == 0:  # Untested UI element
                    new_ui_state = 1 if is_correct_physical_click else -1
                elif current_ui_state == 1: # UI element was Passed
                    new_ui_state = -1 # Clicking again means Fail
                else: # UI element was Failed (-1)
                    new_ui_state = 1 if is_correct_physical_click else -1 # Can be set to Pass if correct, or stay Fail
                
                self.ui_button_states[area_name] = new_ui_state
                
                # Get the canvas item ID from self.button_backgrounds to change its color
                canvas_item_id = self.button_backgrounds.get(area_name)
                if canvas_item_id:
                    if new_ui_state == 1:  # Passed UI element
                        self.canvas.itemconfig(canvas_item_id, fill="#00FF00")  # Green
                        self.log(f"UI Element {area_name}: PASSED")
                    else:  # Failed UI element
                        self.canvas.itemconfig(canvas_item_id, fill="#FF0000")  # Red
                        self.log(f"UI Element {area_name}: FAILED - Expected physical click {expected_physical_event_num}, got {click_type}", "warning")
                else:
                    self.log(f"Warning: Canvas item ID not found for UI element {area_name}", "warning")
                
                # self.update_progress_status() # Call this at the end
                # return # Processed one UI button, stop.
        
        self.update_progress_status() # Update all statuses after processing click

    def on_scroll_event(self, event, scroll_direction):
        """Handle scroll events"""
        # Update scroll status
        self.test_elements_config[scroll_direction]["status"] = TestStatus.PASS
        self.log(f"{scroll_direction} scroll event detected")
        self.update_progress_status()

    def on_mouse_wheel_event(self, event):
        """Handle mouse wheel events"""
        # Update scroll status
        if event.delta > 0:
            self.test_elements_config["Scroll Up"]["status"] = TestStatus.PASS
            self.log("Mouse wheel up scroll event detected")
        else:
            self.test_elements_config["Scroll Down"]["status"] = TestStatus.PASS
            self.log("Mouse wheel down scroll event detected")
        self.update_progress_status()

    def on_xbutton_event(self, event, xbutton_name):
        """Handle XButton events"""
        # Update XButton status
        self.test_elements_config[xbutton_name]["status"] = TestStatus.PASS
        self.log(f"{xbutton_name} XButton event detected")
        self.update_progress_status()

    def skip_pointer_test(self):
        """Skip the pointer stick test for laptops without one"""
        self.pointer_status.configure(text="P: N/A", style="Status.TLabel")
        self.skip_pointer_button.configure(state="disabled")
        self.log("Pointer stick test skipped (not present on device)")

        # Update progress
        self.update_progress_status()

    def check_progress(self):
        """Check if testing is complete (called periodically)"""
        self.update_progress_status()

        # Continue checking only if not done
        if self.done_button.cget("state") == "disabled": # This condition will likely always be false now
            self.after(200, self.check_progress)

    def update_progress_status(self):
        """Update all UI status indicators based on self.test_elements_config."""

        status_colors = {
            TestStatus.PASS: "#00FF00",  # Green
            TestStatus.FAIL: "#FF0000",  # Red
            TestStatus.SKIPPED: "#FFFF00", # Yellow
            TestStatus.NOT_APPLICABLE: "#808080", # Grey
            TestStatus.ERROR: "#FF00FF" # Magenta
        }
        default_color = "#FFFFFF" # White for unknown status

        # Update Pointer Movement / Pointer Circle status (they share an indicator for now)
        pm_status = self.test_elements_config["Pointer Movement"]["status"]
        pc_status = self.test_elements_config["Pointer Circle"]["status"]
        # Prioritize showing PASS/FAIL from pointer circle if it's tested, else show movement
        pointer_display_status = pc_status if pc_status not in [TestStatus.NOT_APPLICABLE, TestStatus.SKIPPED] else pm_status
        p_text = f"P: {pointer_display_status.value[:1].upper()}"
        p_color = status_colors.get(pointer_display_status, default_color)
        if hasattr(self, 'pointer_status'): # Ensure UI element exists
            self.pointer_status.configure(text=p_text, foreground=p_color)

        # Update Touchpad Targets status
        tp_status = self.test_elements_config["Touchpad Targets"]["status"]
        tp_text = f"TP: {tp_status.value[:1].upper()}"
        tp_color = status_colors.get(tp_status, default_color)
        if hasattr(self, 'touchpad_status'):
            self.touchpad_status.configure(text=tp_text, foreground=tp_color)

        # Update Physical Buttons status (Left, Middle, Right)
        for btn_key in ["Left Button", "Middle Button", "Right Button"]:
            element_config = self.test_elements_config[btn_key]
            status = element_config["status"]
            ui_indicator_key = element_config["ui_indicator_key"] # 'left', 'middle', 'right'
            if hasattr(self, 'button_status') and ui_indicator_key in self.button_status:
                text = f"{ui_indicator_key[0].upper()}: {status.value[:1].upper()}"
                color = status_colors.get(status, default_color)
                self.button_status[ui_indicator_key].configure(text=text, foreground=color)

        # Update Scroll status (Scroll Up, Scroll Down)
        for scroll_key in ["Scroll Up", "Scroll Down"]:
            element_config = self.test_elements_config[scroll_key]
            status = element_config["status"]
            ui_indicator_key = element_config["ui_indicator_key"] # 'scroll_up', 'scroll_down'
            if hasattr(self, 'scroll_status') and ui_indicator_key in self.scroll_status:
                # Create a short label like SU: P, SD: N
                label_prefix = "SU" if scroll_key == "Scroll Up" else "SD"
                text = f"{label_prefix}: {status.value[:1].upper()}"
                color = status_colors.get(status, default_color)
                self.scroll_status[ui_indicator_key].configure(text=text, foreground=color)
        
        # Update XButton status (Back Button, Forward Button)
        for xbtn_key in ["Back Button", "Forward Button"]:
            element_config = self.test_elements_config[xbtn_key]
            status = element_config["status"]
            ui_indicator_key = element_config["ui_indicator_key"] # 'back', 'forward'
            if hasattr(self, 'xbutton_status') and ui_indicator_key in self.xbutton_status:
                label_prefix = "BA" if xbtn_key == "Back Button" else "FO"
                text = f"{label_prefix}: {status.value[:1].upper()}"
                color = status_colors.get(status, default_color)
                self.xbutton_status[ui_indicator_key].configure(text=text, foreground=color)

        # Update main progress variable - keep it general as detailed status is now per-element
        self.progress_var.set("Test available features. Click DONE when finished.")

        # The logic for enabling/disabling the done_button based on test completion 
        # is removed as the button is now always active.
        # If specific conditions were to re-disable it, that logic would go here.

    def exit_fullscreen(self, event=None):
        """Exit full screen mode"""
        self.attributes('-fullscreen', False)  # Disable full screen
        
        # Retrieve configured dimensions
        new_width = self.config.exit_fullscreen_width
        new_height = self.config.exit_fullscreen_height
        
        # Center on screen
        # We need to call update_idletasks() for winfo_screenwidth/height to be accurate
        # if the window was just created or changed state.
        # However, in this context, it's usually safe to assume screen dimensions are stable.
        # If issues arise, uncommenting self.update_idletasks() here might be needed.
        # self.update_idletasks() 
        
        screen_width = self.winfo_screenwidth()
        screen_height = self.winfo_screenheight()
        
        x = (screen_width // 2) - (new_width // 2)
        y = (screen_height // 2) - (new_height // 2)
        
        new_geometry = f"{new_width}x{new_height}+{x}+{y}"
        self.geometry(new_geometry)
        
        # Log the new geometry. It's good practice to call update_idletasks() 
        # before requesting winfo_width/height if the geometry was just set,
        # to ensure the reported values are the new ones.
        self.update_idletasks() 
        # current_w = self.winfo_width() # This would confirm the applied width
        # current_h = self.winfo_height() # This would confirm the applied height
        self.log(f"Exited full screen mode to geometry: {new_geometry}")
        
        return "break"  # Prevent further processing of the event

    def finish(self, force_complete=False):
        if self._is_finishing: # Re-entry guard
            self.log("Finish called while already finishing. Aborting duplicate call.", "warning")
            return
        self._is_finishing = True
        self.log(f"Finish method initiated. Force complete: {force_complete}")

        # Cancel known pending 'after' jobs
        if self._focus_after_id:
            self.after_cancel(self._focus_after_id)
            self._focus_after_id = None
            self.log("Cancelled pending focus_force 'after' call.")

        self.force_completed = force_complete
        
        current_results = self.get_results()
        self.final_results = current_results # Set final_results for the run_pointing_device_test wrapper
        
        self.log(f"Results compiled. Overall status: {current_results.get('status')}")

        if self.result_callback:
            try:
                self.log("Calling result_callback.")
                self.result_callback(current_results)
                self.log("result_callback completed.")
            except Exception as e:
                self.log(f"Error in result_callback: {e}", "error")
        else:
            self.log("No result_callback to call.")

        try:
            self.log("Calling update_idletasks before destroy.")
            self.update_idletasks() # Process pending Tkinter events to ensure clean state
            self.log("Calling self.destroy().")
            self.destroy()
            self.log("self.destroy() completed.")
        except tk.TclError as e:
            self.log(f"TclError during finish (likely update_idletasks or destroy): {str(e)}", "error")
            # self.final_results is already set, but this indicates an issue during shutdown.
        except Exception as e:
            self.log(f"Unexpected error during finish: {str(e)}", "error")

    def get_results(self) -> Dict[str, Any]:
        """Compile and return detailed test results from self.test_elements_config."""
        notes_list = []
        any_failed = False
        core_functionality_passed = False

        detailed_statuses = {}

        for element_name, config in self.test_elements_config.items():
            status = config["status"]
            include_in_report = False

            if self.force_completed:
                include_in_report = True
            elif element_name in ["Pointer Movement", "Touchpad Targets"]:
                # For non-forced reports, never include these two as line items
                include_in_report = False 
            elif status != TestStatus.NOT_APPLICABLE:
                # For other elements, include if they were interacted with (not N/A)
                include_in_report = True

            if include_in_report:
                notes_list.append(f"{element_name}: {status.value}")
                detailed_statuses[element_name.lower().replace(' ', '_')] = status.value

            # Overall status determination still considers the true state of all elements,
            # regardless of whether they are included in the notes/details for this report.
            if status == TestStatus.FAIL:
                any_failed = True
            
            if element_name in ["Pointer Movement", "Touchpad Targets"] and status == TestStatus.PASS:
                core_functionality_passed = True
            if element_name == "Pointer Circle" and status == TestStatus.PASS:
                core_functionality_passed = True

        # Add UI button states to the results
        ui_state_to_status_map = {
            0: TestStatus.NOT_APPLICABLE.value,
            1: TestStatus.PASS.value,
            -1: TestStatus.FAIL.value
        }

        for ui_button_name, ui_raw_state in self.ui_button_states.items():
            ui_status_str = ui_state_to_status_map.get(ui_raw_state, TestStatus.ERROR.value) # Default to error if unknown state
            # Format name like "Left Top Button"
            descriptive_ui_name = f"{ui_button_name.replace('_', ' ').title()} Button"
            
            # Always include UI buttons in the report to show their status (pass, fail, or N/A)
            include_ui_element_in_report = True 
            
            if include_ui_element_in_report: # This will now always be true for UI buttons
                notes_list.append(f"{descriptive_ui_name}: {ui_status_str}")
                detailed_statuses[descriptive_ui_name.lower().replace(' ', '_')] = ui_status_str

        # Determine overall status
        overall_status = TestStatus.FAIL # Default to FAIL
        if self.force_completed:
            overall_status = TestStatus.PASS # Or SKIPPED, depending on desired behavior for forced completion
            notes_list.append("Test was force-completed.")
        elif any_failed:
            overall_status = TestStatus.FAIL
        elif core_functionality_passed:
            overall_status = TestStatus.PASS
        # If no core functionality passed and nothing failed, it remains FAIL (or could be SKIPPED/INCOMPLETE)
        
        final_notes = "\n".join(notes_list)
        self.log(f"Final Test Results - Overall: {overall_status.value}, Details: {final_notes}")

        return {
            "status": overall_status.value, # string value of TestStatus
            "notes": final_notes,
            "details": detailed_statuses
        }

@test(
    category=TestCategory.INPUT,
    severity=TestSeverity.HIGH,
    description="Tests touchpad movement, buttons, and pointer stick functionality"
)
def run_pointing_device_test(parent_window, log_callback=None) -> Dict[str, Any]:
    """
    Run the modern pointing device test and return results.

    Args:
        parent_window: Parent tkinter window
        log_callback: Optional callback for logging

    Returns:
        Dictionary with test results
    """
    # Record start time
    start_dt = datetime.datetime.now()

    # Create and show the test window
    pointing_test = StreamlinedPointingTest(parent_window, log_callback)

    # Wait for the test window to close
    parent_window.wait_window(pointing_test)

    # Record end time
    end_dt = datetime.datetime.now()

    # Compile the results
    try:
        # test_details = pointing_test.get_results() # OLD: This calls on a potentially destroyed widget
        test_details = pointing_test.final_results # NEW: Use pre-computed results
    except Exception as e:
        # This fallback is if final_results itself is somehow inaccessible or not set, 
        # though it's initialized in __init__.
        test_details = {
            "status": TestStatus.ERROR.value,
            "notes": f"Test was closed unexpectedly. Error: {str(e)}"
        }

    return {
        "test_details": test_details,
        "started_at": start_dt.isoformat(),
        "finished_at": end_dt.isoformat()
    }
