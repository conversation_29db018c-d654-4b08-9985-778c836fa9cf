#!/usr/bin/env python3
"""
Arcoa Nexus Keyboard Test Module

This module implements keyboard testing functionality for the Arcoa Nexus diagnostics tool.
"""
import datetime
import tkinter as tk
from tkinter import ttk, font
from typing import Dict, Any, Callable, List, Set, Optional
from agent.tests.test_framework import test, TestCategory, TestSeverity


# Standard keyboard layout definitions
KEYBOARD_LAYOUT = {
    # Row 1 (top) - usually function keys
    "row1": [
        {"key": "Escape", "display": "Esc", "width": 1.5},
        {"key": "F1", "width": 1},
        {"key": "F2", "width": 1},
        {"key": "F3", "width": 1},
        {"key": "F4", "width": 1},
        {"key": "F5", "width": 1},
        {"key": "F6", "width": 1},
        {"key": "F7", "width": 1},
        {"key": "F8", "width": 1},
        {"key": "F9", "width": 1},
        {"key": "F10", "width": 1},
        {"key": "F11", "width": 1},
        {"key": "F12", "width": 1},
        {"key": "Print", "display": "PrtSc", "width": 1},
        {"key": "Scroll_Lock", "display": "ScrLk", "width": 1},
        {"key": "Pause", "display": "Pause", "width": 1}
    ],
    # Row 2 - numbers row
    "row2": [
        {"key": "grave", "display": "`", "width": 1},
        {"key": "1", "width": 1},
        {"key": "2", "width": 1},
        {"key": "3", "width": 1},
        {"key": "4", "width": 1},
        {"key": "5", "width": 1},
        {"key": "6", "width": 1},
        {"key": "7", "width": 1},
        {"key": "8", "width": 1},
        {"key": "9", "width": 1},
        {"key": "0", "width": 1},
        {"key": "minus", "display": "-", "width": 1},
        {"key": "equal", "display": "=", "width": 1},
        {"key": "BackSpace", "display": "Backspace", "width": 2},
        {"key": "Insert", "display": "Ins", "width": 1},
        {"key": "Home", "width": 1},
        {"key": "Page_Up", "display": "PgUp", "width": 1}
    ],
    # Row 3 - QWERTY row
    "row3": [
        {"key": "Tab", "width": 1.5},
        {"key": "q", "width": 1},
        {"key": "w", "width": 1},
        {"key": "e", "width": 1},
        {"key": "r", "width": 1},
        {"key": "t", "width": 1},
        {"key": "y", "width": 1},
        {"key": "u", "width": 1},
        {"key": "i", "width": 1},
        {"key": "o", "width": 1},
        {"key": "p", "width": 1},
        {"key": "bracketleft", "display": "[", "width": 1},
        {"key": "bracketright", "display": "]", "width": 1},
        {"key": "backslash", "display": "\\", "width": 1.5},
        {"key": "Delete", "display": "Del", "width": 1},
        {"key": "End", "width": 1},
        {"key": "Page_Down", "display": "PgDn", "width": 1}
    ],
    # Row 4 - ASDF row
    "row4": [
        {"key": "Caps_Lock", "display": "Caps", "width": 1.75},
        {"key": "a", "width": 1},
        {"key": "s", "width": 1},
        {"key": "d", "width": 1},
        {"key": "f", "width": 1},
        {"key": "g", "width": 1},
        {"key": "h", "width": 1},
        {"key": "j", "width": 1},
        {"key": "k", "width": 1},
        {"key": "l", "width": 1},
        {"key": "semicolon", "display": ";", "width": 1},
        {"key": "apostrophe", "display": "'", "width": 1},
        {"key": "Return", "display": "Enter", "width": 2.25},
    ],
    # Row 5 - ZXCV row
    "row5": [
        {"key": "Shift_L", "display": "Shift", "width": 2.25},
        {"key": "z", "width": 1},
        {"key": "x", "width": 1},
        {"key": "c", "width": 1},
        {"key": "v", "width": 1},
        {"key": "b", "width": 1},
        {"key": "n", "width": 1},
        {"key": "m", "width": 1},
        {"key": "comma", "display": ",", "width": 1},
        {"key": "period", "display": ".", "width": 1},
        {"key": "slash", "display": "/", "width": 1},
        {"key": "Shift_R", "display": "Shift", "width": 2.75},
        {"key": "Up", "display": "↑", "width": 1},
    ],
    # Row 6 - bottom row
    "row6": [
        {"key": "Control_L", "display": "Ctrl", "width": 1.25},
        {"key": "Super_L", "display": "Win", "width": 1.25},
        {"key": "Alt_L", "display": "Alt", "width": 1.25},
        {"key": "space", "display": "Space Bar", "width": 6.25},
        {"key": "Alt_R", "display": "Alt", "width": 1.25},
        {"key": "Menu", "display": "Menu", "width": 1.25},
        {"key": "Control_R", "display": "Ctrl", "width": 1.25},
        {"key": "Left", "display": "←", "width": 1},
        {"key": "Down", "display": "↓", "width": 1},
        {"key": "Right", "display": "→", "width": 1},
    ],

    # Numpad rows
    "numpad_row1": [
        {"key": "Num_Lock", "display": "Num", "width": 1},
        {"key": "KP_Divide", "display": "/", "width": 1},
        {"key": "KP_Multiply", "display": "*", "width": 1},
        {"key": "KP_Subtract", "display": "-", "width": 1},
    ],
    "numpad_row2": [
        {"key": "KP_7", "display": "7", "width": 1},
        {"key": "KP_8", "display": "8", "width": 1},
        {"key": "KP_9", "display": "9", "width": 1},
        {"key": "KP_Add", "display": "+", "width": 1, "height": 2},
    ],
    "numpad_row3": [
        {"key": "KP_4", "display": "4", "width": 1},
        {"key": "KP_5", "display": "5", "width": 1},
        {"key": "KP_6", "display": "6", "width": 1},
    ],
    "numpad_row4": [
        {"key": "KP_1", "display": "1", "width": 1},
        {"key": "KP_2", "display": "2", "width": 1},
        {"key": "KP_3", "display": "3", "width": 1},
        {"key": "KP_Enter", "display": "Enter", "width": 1, "height": 2},
    ],
    "numpad_row5": [
        {"key": "KP_0", "display": "0", "width": 2},
        {"key": "KP_Decimal", "display": ".", "width": 1},
    ],
}

# Key mapping for Tkinter key binding
KEY_MAP = {
    # Special key mappings (event.keysym to KeyboardLayout key)
    "Escape": "Escape",
    "F1": "F1", "F2": "F2", "F3": "F3", "F4": "F4",
    "F5": "F5", "F6": "F6", "F7": "F7", "F8": "F8",
    "F9": "F9", "F10": "F10", "F11": "F11", "F12": "F12",
    "Print": "Print", "Scroll_Lock": "Scroll_Lock", "Pause": "Pause",
    "grave": "grave", "asciitilde": "grave",
    "BackSpace": "BackSpace", "Insert": "Insert", "Home": "Home", "Page_Up": "Page_Up",
    "Tab": "Tab", "Delete": "Delete", "End": "End", "Page_Down": "Page_Down",
    "Caps_Lock": "Caps_Lock", "Return": "Return",
    "Shift_L": "Shift_L", "Shift_R": "Shift_R", "Up": "Up",
    "Control_L": "Control_L", "Super_L": "Super_L", "Alt_L": "Alt_L",
    "space": "space",
    "Alt_R": "Alt_R", "Menu": "Menu", "Control_R": "Control_R",
    "Left": "Left", "Down": "Down", "Right": "Right",
    "bracketleft": "bracketleft", "bracketright": "bracketright",
    "backslash": "backslash", "semicolon": "semicolon", "apostrophe": "apostrophe",
    "comma": "comma", "period": "period", "slash": "slash",
    "minus": "minus", "equal": "equal",

    # Numpad keys
    "Num_Lock": "Num_Lock", "Numlock": "Num_Lock",
    "KP_Divide": "KP_Divide", "KP_Multiply": "KP_Multiply",
    "KP_Subtract": "KP_Subtract", "KP_Add": "KP_Add",
    "KP_Enter": "KP_Enter", "KP_Decimal": "KP_Decimal",
    "KP_0": "KP_0", "KP_1": "KP_1", "KP_2": "KP_2", "KP_3": "KP_3",
    "KP_4": "KP_4", "KP_5": "KP_5", "KP_6": "KP_6",
    "KP_7": "KP_7", "KP_8": "KP_8", "KP_9": "KP_9",
    # Alternative numpad key names that might be used by some systems
    "KP_Home": "KP_7", "KP_Up": "KP_8", "KP_Prior": "KP_9", "KP_Page_Up": "KP_9",
    "KP_Left": "KP_4", "KP_Begin": "KP_5", "KP_Right": "KP_6",
    "KP_End": "KP_1", "KP_Down": "KP_2", "KP_Next": "KP_3", "KP_Page_Down": "KP_3",
    "KP_Insert": "KP_0", "KP_Delete": "KP_Decimal",
}


# Add all normal keys (a-z, 0-9)
for c in "abcdefghijklmnopqrstuvwxyz0123456789":
    KEY_MAP[c] = c


class KeyboardTestWindow(tk.Toplevel):
    """Window that displays a graphical keyboard and tracks key presses"""
    def __init__(self, parent, log_callback: Optional[Callable] = None):
        super().__init__(parent)
        self.parent = parent
        self.log_callback = log_callback
        self.title("Keyboard Test")
        self.attributes('-fullscreen', True) # Set window to fullscreen

        self.pressed_keys = set()
        self.key_widgets = {}

        # Keyboard layout
        self.keyboard_layout = KEYBOARD_LAYOUT

        # Set up the UI
        self.setup_ui()

        # Bind key events
        self.bind_keys()

        # Make window modal
        self.transient(parent)
        self.grab_set()

        # Ensure window gets focus
        self.focus_force()
        self.lift()  # Bring window to the top

        # Schedule another focus grab after a short delay
        # This helps ensure focus on some window managers
        self.after(100, self.focus_force)

        # Calculate total keys (must be done after creating the keyboard layout)
        self.total_keys = len(self.key_widgets)
        self.test_complete = False
        self.force_completed = False

        # Start log
        self.log("Keyboard Test Started. Press each key on the keyboard.")
        self.update_progress()

    def log(self, message, level="info"):
        """Log a message using the callback if available"""
        if self.log_callback:
            self.log_callback(message, level)

    def setup_ui(self):
        """Set up the keyboard test UI components"""
        # Configure dark theme styles
        style = ttk.Style()
        style.theme_use('clam') # Use 'clam' theme as a base for customization

        # Configure dark theme colors
        dark_bg = "#2e2e2e"
        dark_fg = "#ffffff"
        dark_button_bg = "#505050"
        dark_button_fg = "#ffffff"
        dark_highlight = "#4CAF50" # Green for pressed keys
        dark_warning = "#ff9800" # Orange for warnings
        dark_error = "#f44336" # Red for errors

        style.configure('.', background=dark_bg, foreground=dark_fg)
        style.configure('TFrame', background=dark_bg)
        style.configure('TLabel', background=dark_bg, foreground=dark_fg)
        style.configure('TButton', background=dark_button_bg, foreground=dark_button_fg, borderwidth=1)
        style.map('TButton',
                  background=[('active', dark_button_bg), ('pressed', dark_highlight)],
                  foreground=[('active', dark_button_fg), ('pressed', dark_fg)])

        # Progress bar style
        style.configure("TProgressbar", background=dark_highlight, troughcolor="#505050")

        # Big button style for controls
        style.configure("Big.TButton", font=("Arial", 14, "bold"), padding=15, background=dark_button_bg, foreground=dark_button_fg)
        style.map("Big.TButton",
                  background=[('active', dark_button_bg), ('pressed', dark_highlight)],
                  foreground=[('active', dark_button_fg), ('pressed', dark_fg)])


        # Main frame
        main_frame = ttk.Frame(self, padding="30") # Increased padding for fullscreen
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Top info section
        info_frame = ttk.Frame(main_frame)
        info_frame.pack(fill=tk.X, pady=(0, 30)) # Increased padding

        ttk.Label(
            info_frame,
            text="Press each key on the keyboard to test. Keys will highlight when pressed.",
            font=("Arial", 18), # Increased font size
            foreground=dark_fg,
            background=dark_bg
        ).pack(side=tk.LEFT)

        # Progress label
        self.progress_var = tk.StringVar(value="0% Tested (0/0 keys)")
        self.progress_label = ttk.Label(
            info_frame,
            textvariable=self.progress_var,
            font=("Arial", 18), # Increased font size
            foreground=dark_fg,
            background=dark_bg
        )
        self.progress_label.pack(side=tk.RIGHT)

        # Progress bar
        self.progress_bar = ttk.Progressbar(main_frame, orient=tk.HORIZONTAL, length=100, mode='determinate')
        self.progress_bar.pack(fill=tk.X, pady=(0, 30)) # Increased padding

        # Keyboard display area
        keyboard_container = ttk.Frame(main_frame)
        keyboard_container.pack(fill=tk.BOTH, expand=True, pady=10) # Reduced padding

        # Create a frame for the main keyboard
        keyboard_frame = ttk.Frame(keyboard_container)
        keyboard_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10)) # Reduced padding

        # Create a frame for the numpad
        numpad_frame = ttk.Frame(keyboard_container)
        # Calculate top padding to align the numpad's top row with the main keyboard's number row (row2)
        # Height of row1 frame (base_height + 2*key_padding) + pady between row1 and row2
        top_padding = (70 + 2 * 6) + 6 # base_height = 70, key_padding = 6, pady = 6
        numpad_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(10, 0), pady=(80, 0)) # Reduced padding, adjusted top padding

        # Create a frame for each row of the main keyboard
        self.row_frames = {}
        for row_name in ["row1", "row2", "row3", "row4", "row5", "row6"]:
            row_frame = ttk.Frame(keyboard_frame)
            row_frame.pack(fill=tk.X, pady=6) # Increased padding
            self.row_frames[row_name] = row_frame

        # Create a frame for each row of the numpad
        for row_name in ["numpad_row1", "numpad_row2", "numpad_row3", "numpad_row4", "numpad_row5"]:
            row_frame = ttk.Frame(numpad_frame)
            row_frame.pack(fill=tk.X, pady=6) # Increased padding
            self.row_frames[row_name] = row_frame

        # Create the keyboard UI
        self.create_keyboard_layout()

        # Bottom controls - with larger, more visible buttons
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(10, 10)) # Reduced top and bottom padding

        # Instruction label to make it clear how to complete the test
        instruction_label = ttk.Label(
            control_frame,
            text="*** CLICK BUTTONS WITH MOUSE ONLY TO COMPLETE TEST - DO NOT USE KEYBOARD! ***",
            font=("Arial", 12, "bold"), # Reduced font size
            foreground=dark_warning, # Changed color for dark theme
            background=dark_bg
        )
        instruction_label.pack(side=tk.LEFT, padx=30, pady=30) # Increased padding

        # Force Complete button - always enabled for hardware issues
        # Using click binding instead of command to prevent keyboard triggering
        self.force_button = ttk.Button(
            control_frame,
            text="Force Complete (Hardware Issue)",
            style="Big.TButton"
        )
        self.force_button.pack(side=tk.RIGHT, padx=(0, 30), pady=30) # Increased padding
        # Bind to explicit mouse click only
        self.force_button.bind("<Button-1>", lambda e: self.complete_test(force_complete=True))

        # Done button to manually complete the test when all keys work
        # Using click binding instead of command to prevent keyboard triggering
        self.done_button = ttk.Button(
            control_frame,
            text="Complete Test",
            style="Big.TButton"
        )
        self.done_button.pack(side=tk.RIGHT, padx=30, pady=30) # Increased padding
        # Bind to explicit mouse click only
        self.done_button.bind("<Button-1>", lambda e: self.complete_test(force_complete=False))

        # Disable keyboard navigation to/from buttons
        self.force_button.configure(takefocus=0)
        self.done_button.configure(takefocus=0)

    def create_keyboard_layout(self):
        """Create the visual keyboard layout"""
        # Base key size
        base_width = 70 # Increased base size for fullscreen
        base_height = 70 # Increased base size for fullscreen
        key_padding = 6 # Increased padding

        # Special keys that span multiple rows (like KP_Add and KP_Enter)
        multi_row_keys = {}

        # Render each row
        for row_name, row_keys in KEYBOARD_LAYOUT.items():
            row_frame = self.row_frames[row_name]

            # Create each key in the row
            for key_info in row_keys:
                key_name = key_info["key"]
                key_display = key_info.get("display", key_name)
                key_width = key_info.get("width", 1.0)
                key_height = key_info.get("height", 1.0)

                # Skip keys that are handled by multi-row logic
                if key_name in multi_row_keys:
                    continue

                # Calculate the pixel dimensions
                pixel_width = int(base_width * key_width)
                pixel_height = int(base_height * key_height)

                # If this key spans multiple rows, we need special handling
                if key_height > 1:
                    # Store info for this multi-row key
                    multi_row_keys[key_name] = {
                        "row_name": row_name,
                        "key_info": key_info,
                        "pixel_width": pixel_width,
                        "pixel_height": pixel_height
                    }

                    # For numpad plus and enter keys, we need to create a taller frame
                    if "numpad" in row_name:
                        # Create a frame for the key that spans multiple rows
                        key_frame = ttk.Frame(row_frame, width=pixel_width, height=pixel_height)
                        key_frame.pack(side=tk.LEFT, padx=key_padding, pady=key_padding)
                        key_frame.pack_propagate(False)

                        # Create the key button
                        key_button = tk.Label(
                            key_frame,
                            text=key_display,
                            background="#505050", # Dark theme color
                            foreground="#ffffff", # Dark theme color
                            font=("Arial", 12), # Increased font size
                            borderwidth=1,
                            relief="raised",
                            anchor="center"
                        )
                        key_button.pack(fill=tk.BOTH, expand=True)

                        # Store reference to the key widget
                        self.key_widgets[key_name] = key_button

                        # For the next row, we need to create a placeholder
                        if key_name == "KP_Add":
                            # Skip a cell in the next row (numpad_row3)
                            placeholder = ttk.Frame(self.row_frames["numpad_row3"], width=pixel_width, height=base_height)
                            placeholder.pack(side=tk.LEFT, padx=key_padding, pady=key_padding)
                            placeholder.pack_propagate(False)
                        elif key_name == "KP_Enter":
                            # Skip a cell in the next row (numpad_row5)
                            placeholder = ttk.Frame(self.row_frames["numpad_row5"], width=pixel_width, height=base_height)
                            placeholder.pack(side=tk.RIGHT, padx=key_padding, pady=key_padding)
                            placeholder.pack_propagate(False)
                    continue

                # Create a frame for the key
                key_frame = ttk.Frame(row_frame, width=pixel_width, height=pixel_height)
                key_frame.pack(side=tk.LEFT, padx=key_padding, pady=key_padding)
                key_frame.pack_propagate(False)

                # Create the key button
                key_button = tk.Label(
                    key_frame,
                    text=key_display,
                    background="#505050", # Dark theme color
                    foreground="#ffffff", # Dark theme color
                    font=("Arial", 12), # Increased font size
                    borderwidth=1,
                    relief="raised",
                    anchor="center"
                )
                key_button.pack(fill=tk.BOTH, expand=True)

                # Store reference to the key widget
                self.key_widgets[key_name] = key_button

    def bind_keys(self):
        """Bind key events to track key presses"""
        self.bind("<KeyPress>", self.on_key_press)
        self.bind("<KeyRelease>", self.on_key_release)

        # Bind special keys that might not be caught by normal bindings
        for keysym in ["<Shift_L>", "<Shift_R>", "<Control_L>", "<Control_R>",
                      "<Alt_L>", "<Alt_R>", "<Super_L>", "<Super_R>",
                      "<Num_Lock>"]: # Removed "Numlock"
            self.bind(keysym, self.on_special_key)

    def on_key_press(self, event):
        """Handle key press events"""
        keysym = event.keysym

        # Debug: Log all key events to help identify numpad keys
        # self.log(f"Key event: keysym={keysym}, keycode={event.keycode}, char={repr(event.char)}")

        # Handle numpad keys specifically
        if keysym.startswith('KP_'):
            # Already in the right format
            pass
        elif keysym in ['plus', 'minus', 'asterisk', 'slash', 'period'] and event.keycode in [106, 107, 109, 110, 111]:
            # Map numpad operator keys
            numpad_map = {
                'plus': 'KP_Add',
                'minus': 'KP_Subtract',
                'asterisk': 'KP_Multiply',
                'slash': 'KP_Divide',
                'period': 'KP_Decimal'
            }
            keysym = numpad_map.get(keysym, keysym)
        elif keysym.isdigit() and event.keycode >= 96 and event.keycode <= 105:
            # Map numpad digits (0-9)
            keysym = f"KP_{keysym}"
        elif keysym == 'Return' and event.keycode == 13:
            # Check if it's the numpad Enter key
            # This is tricky as it might have the same keycode as the main Enter key
            # We'll use a heuristic based on the keycode
            if hasattr(event, 'state') and (event.state & 0x2000):  # Check for numpad state
                keysym = 'KP_Enter'

        # Handle case for letters (a-z) when Caps Lock is on
        if len(keysym) == 1 and keysym.isalpha() and keysym.isupper():
            keysym = keysym.lower()  # Convert uppercase to lowercase

        # Map the keysym to our keyboard layout
        if keysym in KEY_MAP:
            mapped_key = KEY_MAP[keysym]

            # Mark key as pressed
            if mapped_key in self.key_widgets and mapped_key not in self.pressed_keys:
                self.pressed_keys.add(mapped_key)
                self.key_widgets[mapped_key].configure(
                    background="#4CAF50", # Green highlight for pressed keys
                    foreground="#FFFFFF"
                )
                self.log(f"Key pressed: {mapped_key}")

                # Update progress
                self.update_progress()

    def on_key_release(self, event):
        """Handle key release events"""
        # We don't need to do anything on release for this test
        pass

    def on_special_key(self, event):
        """Handle special key events that might not be caught"""
        # Use event.keysym to get the key name directly
        key_name = event.keysym

        if key_name in self.key_widgets and key_name not in self.pressed_keys:
            self.pressed_keys.add(key_name)
            self.key_widgets[key_name].configure(
                background="#4CAF50", # Green highlight for pressed keys
                foreground="#FFFFFF"
            )
            self.log(f"Special key pressed: {key_name}")

            # Update progress
            self.update_progress()

    def update_progress(self):
        """Update the progress display"""
        pressed_count = len(self.pressed_keys)
        percentage = int((pressed_count / self.total_keys) * 100)

        self.progress_var.set(f"{percentage}% Tested ({pressed_count}/{self.total_keys} keys)")
        self.progress_bar["value"] = percentage

        # Change done button text at 100%
        if percentage >= 100 and not self.test_complete:
            self.done_button.configure(text="Test Complete! Click to close")
            self.log("All keys tested! You can close the test window.", "success")

    def complete_test(self, force_complete=False):
        """Complete the test and close the window"""
        self.test_complete = True

        if force_complete:
            self.force_completed = True
            self.log("Keyboard test force-completed due to hardware limitations", "warning")

        self.destroy()

    def get_results(self) -> Dict[str, Any]:
        """Return test results in the standard format"""
        # Calculate percentage of keys pressed
        pressed_count = len(self.pressed_keys)
        percent = int((pressed_count / self.total_keys) * 100) if self.total_keys > 0 else 0

        # Determine status based on force_completed flag and percentage
        # Force-completed tests always result in 'fail' status
        if self.force_completed:
            status = "fail"
            notes = f"Keyboard test force-completed due to hardware issues. Tested {pressed_count} out of {self.total_keys} keys ({percent}%)."
        else:
            # Consider test passed if at least 80% of keys were tested
            # (some keys like Menu might be hard to test on some keyboards)
            status = "pass" if percent >= 80 else "fail"
            notes = f"Tested {pressed_count} out of {self.total_keys} keys ({percent}%)."

        # Generate list of untested keys
        untested_keys = [k for k in self.key_widgets.keys() if k not in self.pressed_keys]
        if untested_keys:
            notes += f" Missing keys: {', '.join(untested_keys)}."

        return {
            "status": status,
            "tested_keys": list(self.pressed_keys),
            "untested_keys": untested_keys,
            "percent_tested": percent,
            "force_completed": self.force_completed,
            "notes": notes
        }

@test(
    category=TestCategory.INPUT,
    severity=TestSeverity.HIGH,
    description="Interactive keyboard test that verifies all keys using a full-screen graphical interface."
)
def run_keyboard_test(parent_window, log_callback=None) -> Dict[str, Any]:
    """Run the keyboard test and return results"""
    start_dt = datetime.datetime.now()

    # Create and show the keyboard test window
    keyboard_test = KeyboardTestWindow(parent_window, log_callback)

    # Wait for the test window to close
    parent_window.wait_window(keyboard_test)

    # Compile the results
    end_dt = datetime.datetime.now()

    try:
        test_details = keyboard_test.get_results()
    except Exception:
        # Window might have been closed unexpectedly
        test_details = {
            "status": "fail",
            "notes": "Test was closed unexpectedly."
        }

    return {
        "test_details": test_details,
        "started_at": start_dt.isoformat(),
        "finished_at": end_dt.isoformat(),
    }
