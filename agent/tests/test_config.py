#!/usr/bin/env python3
"""
Arcoa Nexus Test Configuration

This module provides configuration settings for the test framework.
"""
import os
from typing import Dict, Any, List, Optional

# Test execution settings
DEFAULT_SETTINGS = {
    # General settings
    "report_dir": "test_reports",  # Directory to save test reports
    "log_level": "INFO",           # Logging level (DEBUG, INFO, WARNING, ERROR)

    # CPU test settings
    "cpu_stress_duration": 10,     # Duration of CPU stress test in seconds
    "cpu_min_load": 50.0,          # Minimum CPU load percentage for passing
    "visual_cpu_duration": 30,     # Duration of visual CPU test in seconds

    # RAM test settings
    "ram_test_size_mb": 1024,      # Default RAM test size in MB
    "ram_advanced_duration": 30,   # Duration of advanced RAM test in seconds
    "visual_ram_size_mb": 1024,    # Default visual RAM test size in MB
    "visual_ram_duration": 30,     # Duration of visual RAM test in seconds

    # Drive wipe settings
    "enable_real_wipe": False,     # Whether to enable real drive wiping (DANGEROUS!)
    "wipe_verification": True,     # Whether to verify drive wipes

    # Display test settings
    "display_test_duration": 5,    # Duration to show each test pattern in seconds

    # Keyboard test settings
    "keyboard_min_keys": 80,       # Minimum percentage of keys for passing

    # Pointing device test settings
    "pointing_timeout": 120,       # Timeout for pointing device test in seconds
}


def get_setting(key: str, default: Any = None) -> Any:
    """
    Get a setting from environment variables or default settings.

    Environment variables take precedence over default settings.
    Environment variables should be prefixed with ARCOA_ and uppercase.

    Args:
        key: The setting key
        default: Default value if not found

    Returns:
        The setting value
    """
    # Check environment variables first
    env_key = f"ARCOA_{key.upper()}"
    if env_key in os.environ:
        value = os.environ[env_key]

        # Try to convert to appropriate type based on default
        if key in DEFAULT_SETTINGS:
            default_value = DEFAULT_SETTINGS[key]
            if isinstance(default_value, bool):
                return value.lower() in ("1", "true", "yes", "y", "on")
            elif isinstance(default_value, int):
                try:
                    return int(value)
                except ValueError:
                    pass
            elif isinstance(default_value, float):
                try:
                    return float(value)
                except ValueError:
                    pass

        return value

    # Fall back to default settings
    if key in DEFAULT_SETTINGS:
        return DEFAULT_SETTINGS[key]

    # Use provided default or None
    return default


def get_test_suite(suite_name: str = "default") -> List[str]:
    """
    Get a predefined test suite by name.

    Args:
        suite_name: Name of the test suite

    Returns:
        List of test function paths
    """
    # Define available test suites
    suites = {
        "default": [
            "agent.tests.cpu_test.run_basic_cpu_test",
            "agent.tests.ram_test.run_ram_test",
        ],
        "quick": [
            "agent.tests.cpu_test.run_basic_cpu_test",
            "agent.tests.ram_test.run_ram_test",
        ],
        "full": [
            "agent.tests.cpu_test.run_basic_cpu_test",
            "agent.tests.cpu_test.run_cpu_stress_test",
            "agent.tests.ram_test.run_ram_test",
            "agent.tests.ram_test.run_advanced_ram_test",
        ],
        "visual": [
            "agent.tests.visual_cpu_test.run_visual_cpu_test",
            "agent.tests.visual_ram_test.run_visual_ram_test",
        ],
        "battery": [
            "agent.tests.battery_test.run_battery_test",
            "agent.tests.battery_test.run_battery_discharge_test",
            "agent.tests.battery_test.run_battery_charge_test",
            "agent.tests.battery_test.run_battery_full_assessment",
        ],
        "wipe": [
            "agent.tests.drive_wipe_test.run_secure_wipe_test",
            "agent.tests.drive_wipe_test.run_wipe_verification_test",
        ],
        "stress": [
            "agent.tests.cpu_test.run_cpu_stress_test",
            "agent.tests.ram_test.run_advanced_ram_test",
        ],
        "visual_stress": [
            "agent.tests.visual_cpu_test.run_visual_cpu_test",
            "agent.tests.visual_ram_test.run_visual_ram_test",
        ],
    }

    # Return the requested suite or default if not found
    return suites.get(suite_name.lower(), suites["default"])


def get_test_args(test_name: str) -> Dict[str, Any]:
    """
    Get default arguments for a specific test.

    Args:
        test_name: Full path to the test function

    Returns:
        Dictionary of default arguments
    """
    # Define default arguments for tests
    args = {
        "agent.tests.cpu_test.run_cpu_stress_test": {
            "duration_seconds": get_setting("cpu_stress_duration")
        },
        "agent.tests.ram_test.run_ram_test": {
            "test_size_mb": get_setting("ram_test_size_mb")
        },
        "agent.tests.ram_test.run_advanced_ram_test": {
            "duration_seconds": get_setting("ram_advanced_duration")
        },
        "agent.tests.visual_cpu_test.run_visual_cpu_test": {
            "duration_seconds": get_setting("visual_cpu_duration")
        },
        "agent.tests.visual_ram_test.run_visual_ram_test": {
            "test_size_mb": get_setting("visual_ram_size_mb"),
            "duration_seconds": get_setting("visual_ram_duration")
        },
    }

    # Return arguments for the requested test or empty dict if not found
    return args.get(test_name, {})


# Set up environment variables based on config if not already set
if get_setting("enable_real_wipe"):
    os.environ["ARCOA_ENABLE_WIPE"] = "1"

