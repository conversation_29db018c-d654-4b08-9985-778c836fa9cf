#!/usr/bin/env python3
"""
Arcoa Nexus RAM Test Module

This module implements RAM testing functionality for the Arcoa Nexus diagnostics tool.
"""
import datetime
import time
from typing import Dict, Any, Optional

import psutil

from agent.tests.test_framework import (
    TestCategory, TestSeverity, TestStatus, test
)


@test(
    category=TestCategory.MEMORY,
    severity=TestSeverity.HIGH,
    description="Basic RAM test that allocates, writes, and verifies memory patterns"
)
def run_ram_test(test_size_mb=1024, log_callback=None) -> Dict[str, Any]:
    """
    Quick RAM test: allocate, write, and verify a chunk of memory.

    Args:
        test_size_mb: Memory size to test in MB (default: 1024/1GB)
        log_callback: Optional callback for logging

    Returns:
        Dictionary with test results
    """
    if log_callback:
        log_callback(f"Starting RAM test with target size: {test_size_mb} MB")

    # Get system memory information
    mem_info = psutil.virtual_memory()
    total_mb = mem_info.total / (1024 * 1024)
    available_mb = mem_info.available / (1024 * 1024)

    if log_callback:
        log_callback(f"System memory: {total_mb:.0f} MB total, {available_mb:.0f} MB available")

    # Determine test size based on available memory
    try:
        # Use at most 25% of available memory, but no more than 2GB
        avail_gb = available_mb / 1024
        if avail_gb > 2:
            test_size_mb = min(2048, int(avail_gb * 0.25 * 1024))
            if log_callback:
                log_callback(f"Adjusted test size to {test_size_mb} MB based on available memory")
    except Exception as e:
        if log_callback:
            log_callback(f"Error determining test size: {e}", "warning")

    test_size_bytes = test_size_mb * 1024 * 1024
    details = {
        "total_memory_mb": int(total_mb),
        "available_memory_mb": int(available_mb),
        "test_size_mb": test_size_mb,
        "patterns_tested": ["0xAA", "0x55"],  # We'll test with alternating patterns
        "errors": 0
    }

    try:
        # Allocate memory
        if log_callback:
            log_callback(f"Allocating {test_size_mb} MB of memory...")

        buf = bytearray(test_size_bytes)

        # Test with multiple patterns
        patterns = [b'\xAA', b'\x55']  # Alternating bits: 10101010 and 01010101

        for pattern_idx, pattern_byte in enumerate(patterns):
            pattern_hex = f"0x{pattern_byte[0]:02X}"

            # Write pattern
            if log_callback:
                log_callback(f"Writing pattern {pattern_hex} to memory...")

            start_write = time.time()
            for i in range(0, test_size_bytes, 4096):
                chunk_size = min(4096, test_size_bytes - i)
                buf[i:i+chunk_size] = pattern_byte * chunk_size

            write_time = time.time() - start_write
            details[f"write_time_{pattern_hex}"] = write_time

            # Verify pattern
            if log_callback:
                log_callback(f"Verifying pattern {pattern_hex}...")

            start_verify = time.time()
            errors = 0
            for i in range(0, test_size_bytes, 4096):
                chunk_size = min(4096, test_size_bytes - i)
                if buf[i:i+chunk_size] != pattern_byte * chunk_size:
                    errors += 1
                    if errors <= 10 and log_callback:  # Limit error reporting
                        log_callback(f"Memory error at offset {i}", "error")

            verify_time = time.time() - start_verify
            details[f"verify_time_{pattern_hex}"] = verify_time
            details[f"errors_{pattern_hex}"] = errors
            details["errors"] += errors

            if log_callback:
                log_callback(f"Pattern {pattern_hex} test completed in {write_time+verify_time:.2f}s with {errors} errors")

        # Determine status
        status = TestStatus.PASS if details["errors"] == 0 else TestStatus.FAIL
        notes = f"Tested {test_size_mb} MB with {len(patterns)} patterns. Errors: {details['errors']}."

    except MemoryError:
        status = TestStatus.FAIL
        notes = f"Could not allocate {test_size_mb} MB of memory. Available: {available_mb:.0f} MB."
        details["error_type"] = "MemoryError"

    except Exception as e:
        status = TestStatus.FAIL
        notes = f"Unexpected error: {e}"
        details["error_type"] = str(type(e).__name__)
        details["error_message"] = str(e)

    # Add memory speed metrics if available
    try:
        total_bytes = test_size_bytes * len(details.get("patterns_tested", []))
        total_time = sum(details.get(f"write_time_{p}", 0) + details.get(f"verify_time_{p}", 0)
                         for p in details.get("patterns_tested", []))

        if total_time > 0:
            mb_per_sec = (total_bytes / total_time) / (1024 * 1024)
            details["memory_speed_mb_per_sec"] = mb_per_sec
            notes += f" Memory speed: {mb_per_sec:.1f} MB/s."
    except Exception:
        pass

    return {
        "test_details": {
            "status": status.value,
            "notes": notes,
            **details
        }
    }


@test(
    category=TestCategory.MEMORY,
    severity=TestSeverity.MEDIUM,
    description="Advanced RAM test with multiple patterns and stress testing"
)
def run_advanced_ram_test(duration_seconds=30, log_callback=None) -> Dict[str, Any]:
    """
    Advanced RAM test that performs continuous allocation and verification for a specified duration.

    Args:
        duration_seconds: Duration of the test in seconds
        log_callback: Optional callback for logging

    Returns:
        Dictionary with test results
    """
    if log_callback:
        log_callback(f"Starting advanced RAM test for {duration_seconds} seconds")

    # Get system memory information
    mem_info = psutil.virtual_memory()
    total_mb = mem_info.total / (1024 * 1024)
    available_mb = mem_info.available / (1024 * 1024)

    # Use 20% of available memory for each test cycle
    cycle_size_mb = int(available_mb * 0.2)
    cycle_size_bytes = cycle_size_mb * 1024 * 1024

    if log_callback:
        log_callback(f"System memory: {total_mb:.0f} MB total, {available_mb:.0f} MB available")
        log_callback(f"Using {cycle_size_mb} MB per test cycle")

    # Test patterns (alternating bits, all ones, all zeros, etc.)
    patterns = [b'\xAA', b'\x55', b'\xFF', b'\x00']

    # Track statistics
    cycles_completed = 0
    total_errors = 0
    start_time = time.time()
    end_time = start_time + duration_seconds

    details = {
        "total_memory_mb": int(total_mb),
        "available_memory_mb": int(available_mb),
        "cycle_size_mb": cycle_size_mb,
        "patterns_tested": [f"0x{p[0]:02X}" for p in patterns],
        "cycles_completed": 0,
        "errors": 0
    }

    try:
        # Run test cycles until time expires
        while time.time() < end_time:
            if log_callback:
                log_callback(f"Starting test cycle {cycles_completed+1}...")

            # Allocate memory for this cycle
            buf = bytearray(cycle_size_bytes)

            # Test with each pattern
            for pattern_byte in patterns:
                pattern_hex = f"0x{pattern_byte[0]:02X}"

                # Write pattern
                for i in range(0, cycle_size_bytes, 4096):
                    chunk_size = min(4096, cycle_size_bytes - i)
                    buf[i:i+chunk_size] = pattern_byte * chunk_size

                # Verify pattern
                errors = 0
                for i in range(0, cycle_size_bytes, 4096):
                    chunk_size = min(4096, cycle_size_bytes - i)
                    if buf[i:i+chunk_size] != pattern_byte * chunk_size:
                        errors += 1

                total_errors += errors
                if errors > 0 and log_callback:
                    log_callback(f"Pattern {pattern_hex} test found {errors} errors", "warning")

            cycles_completed += 1

            # Release memory explicitly
            del buf

            # Status update every few cycles
            if cycles_completed % 5 == 0 and log_callback:
                elapsed = time.time() - start_time
                remaining = max(0, duration_seconds - elapsed)
                log_callback(f"Completed {cycles_completed} cycles. {remaining:.0f}s remaining.")

    except MemoryError:
        if log_callback:
            log_callback(f"Memory allocation error after {cycles_completed} cycles", "error")

    except Exception as e:
        if log_callback:
            log_callback(f"Test error: {e}", "error")

    # Update details
    details["cycles_completed"] = cycles_completed
    details["errors"] = total_errors
    details["test_duration_seconds"] = time.time() - start_time

    # Determine status
    status = TestStatus.PASS if total_errors == 0 else TestStatus.FAIL
    notes = f"Completed {cycles_completed} test cycles using {cycle_size_mb} MB per cycle. Errors: {total_errors}."

    return {
        "test_details": {
            "status": status.value,
            "notes": notes,
            **details
        }
    }
