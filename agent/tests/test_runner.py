#!/usr/bin/env python3
"""
Arcoa Nexus Test Runner

This module provides functionality to run tests in sequence or in parallel,
with detailed reporting and logging capabilities.
"""
import asyncio
import datetime
import json
import logging
import os
import sys
import time
from concurrent.futures import Thread<PERSON>oolExecutor
from typing import Any, Callable, Dict, List, Optional, Set, Tuple, Union

from agent.tests.test_framework import (
    TestCategory, TestResult, TestSeverity, TestStatus, 
    get_available_tests, run_test_by_name
)

# Configure logging
logger = logging.getLogger("arcoa.tests.runner")


class TestRunner:
    """Test runner for executing multiple tests with reporting."""
    
    def __init__(
        self,
        log_callback: Optional[Callable] = None,
        report_dir: Optional[str] = None,
        parallel: bool = False,
        max_workers: int = 4
    ):
        """
        Initialize the test runner.
        
        Args:
            log_callback: Optional callback for logging messages
            report_dir: Directory to save test reports
            parallel: Whether to run tests in parallel
            max_workers: Maximum number of parallel workers
        """
        self.log_callback = log_callback or (lambda msg, lvl="info": logger.info(msg))
        self.report_dir = report_dir
        self.parallel = parallel
        self.max_workers = max_workers
        self.results: Dict[str, Dict[str, Any]] = {}
        
        # Create report directory if needed
        if self.report_dir and not os.path.exists(self.report_dir):
            os.makedirs(self.report_dir)
    
    def log(self, message: str, level: str = "info") -> None:
        """Log a message using the callback or logger."""
        self.log_callback(message, level)
        
        # Also log to the standard logger
        log_method = getattr(logger, level.lower(), logger.info)
        log_method(message)
    
    def run_tests(
        self,
        tests: List[str],
        categories: Optional[Set[TestCategory]] = None,
        severities: Optional[Set[TestSeverity]] = None,
        test_args: Optional[Dict[str, Dict[str, Any]]] = None
    ) -> Dict[str, Dict[str, Any]]:
        """
        Run a list of tests.
        
        Args:
            tests: List of test names to run
            categories: Optional set of categories to filter tests
            severities: Optional set of severities to filter tests
            test_args: Optional dictionary of test arguments keyed by test name
            
        Returns:
            Dictionary of test results keyed by test name
        """
        # Filter tests if categories or severities are specified
        available_tests = get_available_tests()
        filtered_tests = []
        
        for test_info in available_tests:
            test_path = test_info["full_path"]
            
            # Skip if not in the requested tests
            if tests and test_path not in tests:
                continue
                
            # Filter by category
            if categories and TestCategory(test_info["category"]) not in categories:
                continue
                
            # Filter by severity
            if severities and TestSeverity(test_info["severity"]) not in severities:
                continue
                
            filtered_tests.append(test_path)
        
        # Log test plan
        self.log(f"Running {len(filtered_tests)} tests")
        for test in filtered_tests:
            self.log(f"  - {test}")
        
        # Prepare arguments for each test
        test_args = test_args or {}
        args_with_defaults = {}
        for test in filtered_tests:
            args_with_defaults[test] = {
                "log_callback": self.log_callback,
                **(test_args.get(test, {}))
            }
        
        # Run tests
        start_time = time.time()
        
        if self.parallel and len(filtered_tests) > 1:
            # Run tests in parallel
            self.results = self._run_parallel(filtered_tests, args_with_defaults)
        else:
            # Run tests sequentially
            self.results = self._run_sequential(filtered_tests, args_with_defaults)
        
        # Calculate total time
        total_time = time.time() - start_time
        
        # Log summary
        self._log_summary(total_time)
        
        # Save report if directory is specified
        if self.report_dir:
            self._save_report()
        
        return self.results
    
    def _run_sequential(
        self,
        tests: List[str],
        args_dict: Dict[str, Dict[str, Any]]
    ) -> Dict[str, Dict[str, Any]]:
        """Run tests sequentially."""
        results = {}
        
        for i, test_name in enumerate(tests):
            self.log(f"Running test {i+1}/{len(tests)}: {test_name}")
            
            try:
                # Get arguments for this test
                test_args = args_dict.get(test_name, {})
                
                # Run the test
                result = run_test_by_name(test_name, **test_args)
                results[test_name] = result
                
                # Log result
                status = result.get("test_details", {}).get("status", "error")
                self.log(f"Test {test_name} completed with status: {status}")
            except Exception as e:
                self.log(f"Error running test {test_name}: {str(e)}", "error")
                results[test_name] = TestResult(
                    test_name=test_name,
                    status=TestStatus.ERROR,
                    notes=f"Exception during test execution: {str(e)}"
                ).to_dict()
        
        return results
    
    def _run_parallel(
        self,
        tests: List[str],
        args_dict: Dict[str, Dict[str, Any]]
    ) -> Dict[str, Dict[str, Any]]:
        """Run tests in parallel using a thread pool."""
        results = {}
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all tests
            future_to_test = {
                executor.submit(run_test_by_name, test_name, **args_dict.get(test_name, {})): test_name
                for test_name in tests
            }
            
            # Process results as they complete
            for i, future in enumerate(future_to_test):
                test_name = future_to_test[future]
                try:
                    result = future.result()
                    results[test_name] = result
                    
                    # Log result
                    status = result.get("test_details", {}).get("status", "error")
                    self.log(f"Test {test_name} completed with status: {status}")
                except Exception as e:
                    self.log(f"Error running test {test_name}: {str(e)}", "error")
                    results[test_name] = TestResult(
                        test_name=test_name,
                        status=TestStatus.ERROR,
                        notes=f"Exception during test execution: {str(e)}"
                    ).to_dict()
        
        return results
    
    def _log_summary(self, total_time: float) -> None:
        """Log a summary of test results."""
        # Count results by status
        status_counts = {status.value: 0 for status in TestStatus}
        for result in self.results.values():
            status = result.get("test_details", {}).get("status", "error")
            status_counts[status] = status_counts.get(status, 0) + 1
        
        # Log summary
        self.log(f"Test run completed in {total_time:.2f} seconds")
        self.log(f"Total tests: {len(self.results)}")
        for status, count in status_counts.items():
            if count > 0:
                self.log(f"  {status}: {count}")
    
    def _save_report(self) -> None:
        """Save test results to a JSON file."""
        if not self.report_dir:
            return
            
        # Create a timestamp for the filename
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = os.path.join(self.report_dir, f"test_report_{timestamp}.json")
        
        # Save the results
        with open(filename, "w") as f:
            json.dump({
                "timestamp": timestamp,
                "results": self.results
            }, f, indent=2)
            
        self.log(f"Test report saved to {filename}")


def main():
    """Command-line interface for the test runner."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Arcoa Nexus Test Runner")
    parser.add_argument(
        "--tests",
        nargs="*",
        help="Specific tests to run (if not specified, runs all tests)"
    )
    parser.add_argument(
        "--categories",
        nargs="*",
        choices=[c.value for c in TestCategory],
        help="Test categories to run"
    )
    parser.add_argument(
        "--severities",
        nargs="*",
        choices=[s.value for s in TestSeverity],
        help="Test severities to run"
    )
    parser.add_argument(
        "--parallel",
        action="store_true",
        help="Run tests in parallel"
    )
    parser.add_argument(
        "--workers",
        type=int,
        default=4,
        help="Maximum number of parallel workers"
    )
    parser.add_argument(
        "--report-dir",
        help="Directory to save test reports"
    )
    parser.add_argument(
        "--list",
        action="store_true",
        help="List available tests and exit"
    )
    
    args = parser.parse_args()
    
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    
    # List tests if requested
    if args.list:
        tests = get_available_tests()
        print(f"Available tests ({len(tests)}):")
        for test in tests:
            print(f"  {test['full_path']}")
            print(f"    Category: {test['category']}")
            print(f"    Severity: {test['severity']}")
            if test['description']:
                print(f"    Description: {test['description']}")
            print()
        return
    
    # Create test runner
    runner = TestRunner(
        report_dir=args.report_dir,
        parallel=args.parallel,
        max_workers=args.workers
    )
    
    # Convert categories and severities to sets if specified
    categories = set(TestCategory(c) for c in args.categories) if args.categories else None
    severities = set(TestSeverity(s) for s in args.severities) if args.severities else None
    
    # Run tests
    runner.run_tests(
        tests=args.tests,
        categories=categories,
        severities=severities
    )


if __name__ == "__main__":
    main()
