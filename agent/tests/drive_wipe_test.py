"""Secure Drive Wipe Test Module

This module implements secure drive wiping functionality for the Arcoa Nexus diagnostics tool.
**NO ACTUAL WIPING IS PERFORMED BY DEFAULT.**

To enable real wiping, set the environment variable ``ARCOA_ENABLE_WIPE=1`` *and* run
with administrative privileges.
"""
from __future__ import annotations

import datetime
import os
import platform
import subprocess
import time
from typing import Dict, Any, List, Optional, Tuple, Union

import psutil

import threading
import hashlib
from agent.tests.test_framework import (
    TestCategory,
    TestSeverity,
    TestStatus,
    test,
)

# New GUI window
from agent.gui.drive_wipe_gui import DriveWipeWindow

# ---------------------------------------------------------------------------
# Helper / backend functions (safe simulation by default)
# ---------------------------------------------------------------------------

ZERO_MB_SHA256 = (
    "122dfdb93ab951c1efc849390009083098f5800a1bf27359d877861791902553"  # 1 MiB zeros
)


def calculate_boundary_hashes(device_path: str, stage: str, mb_to_read: int = 1):
    """Return SHA-256 of first and last *mb_to_read* of *device_path*.

    On platforms where we cannot access block devices (or when not root), this
    function returns empty hashes. No exception is raised.
    """
    try:
        # Determine size in 512-byte blocks via blockdev
        sz_out = subprocess.check_output(["blockdev", "--getsz", device_path], text=True).strip()
        total_blocks = int(sz_out)
        block_bytes = total_blocks * 512
        offset = block_bytes - mb_to_read * 1024 * 1024

        # Read first chunk
        first = subprocess.check_output(
            [
                "dd",
                f"if={device_path}",
                "bs=1M",
                f"count={mb_to_read}",
            ],
            stderr=subprocess.DEVNULL,
        )
        first_hash = hashlib.sha256(first).hexdigest()

        # Read last chunk
        last = subprocess.check_output(
            [
                "dd",
                f"if={device_path}",
                "bs=1M",
                f"skip={offset // (1024*1024)}",
                f"count={mb_to_read}",
            ],
            stderr=subprocess.DEVNULL,
        )
        last_hash = hashlib.sha256(last).hexdigest()
    except Exception:
        first_hash = last_hash = ""

    return {
        f"{stage}_first_1mb_sha256": first_hash,
        f"{stage}_last_1mb_sha256": last_hash,
    }


# The following _run_* functions are stubs that SIMULATE wiping by sleeping.
# They return structures matching the spec but DO NOT perform destructive ops.


def _run_nwipe(device_path, method_name, progress_cb, log_cb):
    log_cb(f"[SIM] Running nwipe {method_name} on {device_path}")
    for p in range(0, 101, 10):
        progress_cb(device_path, 0, p, "nwipe simulation")
        time.sleep(0.1)
    return {"status": "pass", "details": "Simulated nwipe", "errors": ""}


def _run_ata_secure_erase(device_path, progress_cb, log_cb):
    log_cb(f"[SIM] ATA Secure Erase on {device_path}")
    for p in range(0, 101, 20):
        progress_cb(device_path, 0, p, "ata se sim")
        time.sleep(0.1)
    return {"status": "pass", "details": "Simulated ATA SE", "errors": ""}


def _run_nvme_sanitize(device_path, progress_cb, log_cb):
    log_cb(f"[SIM] NVMe Sanitize on {device_path}")
    for p in range(0, 101, 25):
        progress_cb(device_path, 0, p, "nvme sanitize sim")
        time.sleep(0.1)
    return {"status": "pass", "details": "Simulated NVMe sanitize", "errors": ""}


def _run_blkdiscard(device_path, progress_cb, log_cb):
    log_cb(f"[SIM] blkdiscard on {device_path}")
    progress_cb(device_path, 0, 100, "blkdiscard sim")
    time.sleep(0.1)
    return {"status": "pass", "details": "Simulated blkdiscard", "errors": ""}


WIPE_METHOD_MAP = {
    "zero_fill": _run_nwipe,
    "random_fill": _run_nwipe,
    "dodshort": _run_nwipe,
    "gutmann": _run_nwipe,
    "ata_secure_erase": _run_ata_secure_erase,
    "nvme_sanitize": _run_nvme_sanitize,
    "blkdiscard": _run_blkdiscard,
}


def perform_single_wipe_threaded(
    drive_path: str,
    method_name: str,
    drive_info: Dict[str, Any],
    progress_cb_gui,
    log_cb_gui,
    result_cb_main_gui,
):
    """Simulated single-drive wipe executed in its own thread."""
    start_ts = datetime.datetime.utcnow().isoformat()
    pre_hash = calculate_boundary_hashes(drive_path, "pre")

    runner = WIPE_METHOD_MAP.get(method_name, _run_nwipe)
    res = runner(drive_path, method_name, progress_cb_gui, log_cb_gui)

    post_hash = calculate_boundary_hashes(drive_path, "post")

    # Simple verification for zero_fill/blkdiscard
    verification = "not_applicable"
    if method_name in {"zero_fill", "blkdiscard"}:
        verification = (
            "pass"
            if post_hash.get("post_first_1mb_sha256") == ZERO_MB_SHA256
            and post_hash.get("post_last_1mb_sha256") == ZERO_MB_SHA256
            else "fail"
        )

    final = {
        "device_path": drive_path,
        "method": method_name,
        "status": res["status"],
        "details": res.get("details"),
        "errors": res.get("errors"),
        "verification": verification,
        "hashes": {**pre_hash, **post_hash},
        "start_time": start_ts,
        "end_time": datetime.datetime.utcnow().isoformat(),
    }

    result_cb_main_gui(drive_path, final)


def process_wipe_queue(
    drives_to_wipe: List[str],
    method_name: str,
    all_drives_info: List[Dict[str, Any]],
    progress_cb_gui,
    log_cb_gui,
    result_cb_main_gui,
    all_done_cb_gui,
):
    total = len(drives_to_wipe)
    for idx, d in enumerate(drives_to_wipe, 1):
        drive_info = next((x for x in all_drives_info if x["path"] == d), {})
        progress_cb_gui(d, (idx - 1) / total * 100, 0, "starting")
        perform_single_wipe_threaded(
            d,
            method_name,
            drive_info,
            lambda cur, _ov, prog, msg: progress_cb_gui(cur, ((idx - 1) / total * 100) + prog / total, prog, msg),
            log_cb_gui,
            result_cb_main_gui,
        )
    all_done_cb_gui()


# ---------------------------------------------------------------------------
# GUI entry-point
# ---------------------------------------------------------------------------

@test(
    category=TestCategory.WIPE,
    severity=TestSeverity.CRITICAL,
    description="Interactive secure wipe using GUI and background threads",
)
def run_secure_wipe_test(parent_window, log_callback=None):
    """Entry-point called by main_window when user checks **Secure Drive Wipe**.

    Opens DriveWipeWindow and blocks until the window is closed. Returns a
    simple summary; per-drive details are handled via callbacks into the main
    GUI logger.
    """

    if log_callback is None:
        log_callback = lambda msg, lvl="info": None

    # Collect detailed drive list using drive_info
    from agent.hardware.drive_info import get_detailed_drive_info

    drives_info = get_detailed_drive_info()

    def start_actual_wipe(drives_selected, method_key, prog_cb, log_cb, result_cb, all_done_cb):
        t = threading.Thread(
            target=process_wipe_queue,
            args=(
                drives_selected,
                method_key,
                drives_info,
                prog_cb,
                log_cb,
                result_cb,
                all_done_cb,
            ),
            daemon=True,
        )
        t.start()

    wipe_win = DriveWipeWindow(parent_window, drives_info, start_actual_wipe, log_callback)
    parent_window.wait_window(wipe_win)

    # Retrieve the collected results from the DriveWipeWindow instance
    detailed_results = wipe_win.wipe_results_collected
    
    # Log how many results were collected for debugging or info
    if log_callback:
        log_callback(f"Retrieved {len(detailed_results)} detailed wipe results from DriveWipeWindow.", "info")

    return detailed_results
