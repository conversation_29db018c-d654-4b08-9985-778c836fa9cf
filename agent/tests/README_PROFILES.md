# Test Profiles

This document describes the test profile system for the Arcoa Nexus diagnostics tool.

## Overview

Test profiles allow you to create and manage predefined sets of tests for specific device types. For example, you can create profiles for:

- Desktop computers
- Laptop computers
- Servers
- Tablets
- Custom configurations

Each profile defines which tests to run and can include custom parameters for those tests.

## Using Profiles

### From the Main Application

1. Launch the Arcoa Nexus Diagnostics application
2. In the "Tests" section, use the profile dropdown to select a profile
3. Click "Load" to load the selected profile
4. The appropriate tests will be automatically selected
5. Click "Run Tests" to run the selected tests

### Managing Profiles

To manage profiles:

1. Click the "Edit Profiles" button in the main application
2. The Profile Editor window will open
3. From here you can:
   - Select an existing profile to view or edit
   - Create a new profile
   - Clone an existing profile
   - Delete a profile
   - Save changes to a profile

## Profile Editor

The Profile Editor provides a user-friendly interface for creating and editing test profiles:

- **Profile List**: Shows all available profiles
- **Profile Details**: Edit the name, device type, and description
- **Test Selection**: Choose which tests to include in the profile
- **Test Parameters**: (Future enhancement) Configure parameters for each test

### Creating a New Profile

1. Click the "New" button
2. Enter a name for the profile
3. Select the device type
4. Enter a description
5. Select the tests to include
6. Click "Save Profile"

### Editing an Existing Profile

1. Select the profile from the list
2. Modify the profile details as needed
3. Select or deselect tests
4. Click "Save Profile"

### Cloning a Profile

1. Select the profile you want to clone
2. Click the "Clone" button
3. Enter a name for the new profile
4. Make any desired changes
5. Click "Save Profile"

## Default Profiles

The system comes with several default profiles:

1. **Desktop**: Standard tests for desktop computers
   - CPU Test
   - CPU Stress Test
   - RAM Test
   - Advanced RAM Test
   - LCD Test
   - Keyboard Test

2. **Laptop**: Standard tests for laptop computers
   - CPU Test
   - RAM Test
   - LCD Test
   - Keyboard Test
   - Touchpad & Pointer Test

3. **Visual Tests**: Visual tests for interactive diagnostics
   - Visual CPU Test
   - Visual RAM Test

4. **Quick Check**: Quick basic tests for rapid diagnostics
   - CPU Test
   - RAM Test

5. **Stress Test**: Extended stress tests for stability verification
   - CPU Stress Test (60 seconds)
   - Advanced RAM Test (120 seconds)

6. **Secure Wipe**: Secure data wiping for device decommissioning
   - Secure Drive Wipe
   - Wipe Verification Test

## Technical Details

### Profile Storage

Profiles are stored as JSON files in the `agent/tests/profiles` directory. Each profile is saved in a separate file named after the profile.

### Profile Structure

A profile contains the following information:

- **name**: The profile name
- **description**: A description of the profile
- **device_type**: The type of device the profile is for
- **tests**: A list of test paths to run
- **test_args**: Optional arguments for specific tests

Example profile JSON:

```json
{
  "name": "Desktop",
  "description": "Standard tests for desktop computers",
  "device_type": "Desktop",
  "tests": [
    "agent.tests.cpu_test.run_basic_cpu_test",
    "agent.tests.cpu_test.run_cpu_stress_test",
    "agent.tests.ram_test.run_ram_test",
    "agent.tests.ram_test.run_advanced_ram_test",
    "agent.tests.display_test.run_lcd_test_gui",
    "agent.tests.keyboard_test.run_keyboard_test"
  ],
  "test_args": {
    "agent.tests.cpu_test.run_cpu_stress_test": {
      "duration_seconds": 30
    }
  }
}
```

### Adding Custom Tests to Profiles

When creating custom tests, you can add them to profiles by including their full module path in the `tests` list. For example:

```
agent.tests.custom_test.run_my_custom_test
```

The test will be automatically discovered and included in the profile editor.
