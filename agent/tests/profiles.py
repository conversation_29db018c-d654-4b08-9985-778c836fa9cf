#!/usr/bin/env python3
"""
Arcoa Nexus Test Profiles

This module provides functionality for managing test profiles.
Profiles define sets of tests to run for specific device types.
"""
import json
import os
import logging
from typing import Dict, List, Any, Optional

# Configure logging
logger = logging.getLogger("arcoa.tests.profiles")

# Default profiles directory
PROFILES_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), "profiles")

# Ensure profiles directory exists
def ensure_profiles_dir():
    """Ensure the profiles directory exists."""
    if not os.path.exists(PROFILES_DIR):
        try:
            os.makedirs(PROFILES_DIR)
            logger.info(f"Created profiles directory: {PROFILES_DIR}")
            return True
        except Exception as e:
            logger.error(f"Failed to create profiles directory: {e}")
            return False
    return True

# Create the directory when module is loaded
ensure_profiles_dir()


class Profile:
    """Represents a test profile for a specific device type."""

    def __init__(self, name: str, description: str = "", tests: List[str] = None,
                 device_type: str = "Generic", test_args: Dict[str, Dict[str, Any]] = None):
        """
        Initialize a test profile.

        Args:
            name: Profile name
            description: Profile description
            tests: List of test paths to run
            device_type: Type of device (Desktop, Laptop, etc.)
            test_args: Optional arguments for specific tests
        """
        self.name = name
        self.description = description
        self.tests = tests or []
        self.device_type = device_type
        self.test_args = test_args or {}

    def to_dict(self) -> Dict[str, Any]:
        """Convert profile to dictionary for serialization."""
        return {
            "name": self.name,
            "description": self.description,
            "tests": self.tests,
            "device_type": self.device_type,
            "test_args": self.test_args
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Profile':
        """Create a profile from a dictionary."""
        return cls(
            name=data.get("name", "Unnamed Profile"),
            description=data.get("description", ""),
            tests=data.get("tests", []),
            device_type=data.get("device_type", "Generic"),
            test_args=data.get("test_args", {})
        )


def get_profile_path(profile_name: str) -> str:
    """
    Get the file path for a profile.

    Args:
        profile_name: Name of the profile

    Returns:
        Path to the profile file
    """
    # Sanitize profile name for use as filename
    safe_name = "".join(c if c.isalnum() else "_" for c in profile_name)
    return os.path.join(PROFILES_DIR, f"{safe_name}.json")


def save_profile(profile: Profile) -> bool:
    """
    Save a profile to disk.

    Args:
        profile: Profile to save

    Returns:
        True if successful, False otherwise
    """
    try:
        profile_path = get_profile_path(profile.name)
        with open(profile_path, 'w') as f:
            json.dump(profile.to_dict(), f, indent=2)
        logger.info(f"Saved profile '{profile.name}' to {profile_path}")
        return True
    except Exception as e:
        logger.error(f"Failed to save profile '{profile.name}': {e}")
        return False


def load_profile(profile_name: str) -> Optional[Profile]:
    """
    Load a profile from disk.

    Args:
        profile_name: Name of the profile to load

    Returns:
        Loaded profile or None if not found
    """
    try:
        profile_path = get_profile_path(profile_name)
        if not os.path.exists(profile_path):
            logger.warning(f"Profile '{profile_name}' not found at {profile_path}")
            return None

        with open(profile_path, 'r') as f:
            data = json.load(f)

        profile = Profile.from_dict(data)
        logger.info(f"Loaded profile '{profile.name}' from {profile_path}")
        return profile
    except Exception as e:
        logger.error(f"Failed to load profile '{profile_name}': {e}")
        return None


def get_all_profiles() -> List[Profile]:
    """
    Get all available profiles.

    Returns:
        List of all profiles
    """
    profiles = []

    if not os.path.exists(PROFILES_DIR):
        logger.warning(f"Profiles directory not found: {PROFILES_DIR}")
        return profiles

    for filename in os.listdir(PROFILES_DIR):
        if filename.endswith(".json"):
            try:
                with open(os.path.join(PROFILES_DIR, filename), 'r') as f:
                    data = json.load(f)
                profile = Profile.from_dict(data)
                profiles.append(profile)
            except Exception as e:
                logger.error(f"Failed to load profile from {filename}: {e}")

    return profiles


def delete_profile(profile_name: str) -> bool:
    """
    Delete a profile.

    Args:
        profile_name: Name of the profile to delete

    Returns:
        True if successful, False otherwise
    """
    try:
        profile_path = get_profile_path(profile_name)
        if not os.path.exists(profile_path):
            logger.warning(f"Profile '{profile_name}' not found at {profile_path}")
            return False

        os.remove(profile_path)
        logger.info(f"Deleted profile '{profile_name}' from {profile_path}")
        return True
    except Exception as e:
        logger.error(f"Failed to delete profile '{profile_name}': {e}")
        return False


# Default profiles
DEFAULT_PROFILES = [
    Profile(
        name="Desktop",
        description="Standard tests for desktop computers",
        device_type="Desktop",
        tests=[
            "agent.tests.cpu_test.run_basic_cpu_test",
            "agent.tests.cpu_test.run_cpu_stress_test",
            "agent.tests.ram_test.run_ram_test",
            "agent.tests.ram_test.run_advanced_ram_test",
            "agent.tests.display_test.run_lcd_test_gui",
            "agent.tests.keyboard_test.run_keyboard_test",
        ]
    ),
    Profile(
        name="Laptop",
        description="Standard tests for laptop computers",
        device_type="Laptop",
        tests=[
            "agent.tests.cpu_test.run_basic_cpu_test",
            "agent.tests.ram_test.run_ram_test",
            "agent.tests.display_test.run_lcd_test_gui",
            "agent.tests.keyboard_test.run_keyboard_test",
            "agent.tests.pointing_device_test.run_pointing_device_test",
            "agent.tests.battery_test.run_battery_test",  # Add battery health test to laptop profile
        ]
    ),
    Profile(
        name="Battery",
        description="Battery health and performance tests for laptops and portable devices",
        device_type="Laptop",
        tests=[
            "agent.tests.battery_test.run_battery_test",
            "agent.tests.battery_test.run_battery_discharge_test",
            "agent.tests.battery_test.run_battery_charge_test",
            "agent.tests.battery_test.run_battery_full_assessment"
        ],
        test_args={
            "agent.tests.battery_test.run_battery_discharge_test": {
                "duration_seconds": 120  # 2 minute discharge test
            },
            "agent.tests.battery_test.run_battery_charge_test": {
                "duration_seconds": 180  # 3 minute charge test
            }
        }
    ),
    Profile(
        name="Visual Tests",
        description="Visual tests for interactive diagnostics",
        device_type="Any",
        tests=[
            "agent.tests.visual_cpu_test.run_visual_cpu_test",
            "agent.tests.visual_ram_test.run_visual_ram_test",
        ]
    ),
    Profile(
        name="Quick Check",
        description="Quick basic tests for rapid diagnostics",
        device_type="Any",
        tests=[
            "agent.tests.cpu_test.run_basic_cpu_test",
            "agent.tests.ram_test.run_ram_test",
        ]
    ),
    Profile(
        name="Stress Test",
        description="Extended stress tests for stability verification",
        device_type="Any",
        tests=[
            "agent.tests.cpu_test.run_cpu_stress_test",
            "agent.tests.ram_test.run_advanced_ram_test",
        ],
        test_args={
            "agent.tests.cpu_test.run_cpu_stress_test": {
                "duration_seconds": 60  # Longer stress test
            },
            "agent.tests.ram_test.run_advanced_ram_test": {
                "duration_seconds": 120  # Longer RAM test
            }
        }
    ),
    Profile(
        name="Secure Wipe",
        description="Secure data wiping for device decommissioning",
        device_type="Any",
        tests=[
            "agent.tests.drive_wipe_test.run_secure_wipe_test",
            "agent.tests.drive_wipe_test.run_wipe_verification_test",
        ]
    )
]


def create_default_profiles():
    """Create default profiles if they don't exist."""
    # Ensure profiles directory exists
    if not ensure_profiles_dir():
        logger.error("Failed to create profiles directory, cannot create default profiles")
        return

    # Create each default profile if it doesn't exist
    for profile in DEFAULT_PROFILES:
        profile_path = get_profile_path(profile.name)
        if not os.path.exists(profile_path):
            if save_profile(profile):
                logger.info(f"Created default profile: {profile.name}")
            else:
                logger.error(f"Failed to create default profile: {profile.name}")


# Default profiles are no longer created automatically at startup.
# To create default profiles, call create_default_profiles() explicitly from the profile editor or setup script.

