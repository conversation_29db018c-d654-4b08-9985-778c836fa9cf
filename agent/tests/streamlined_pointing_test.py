#!/usr/bin/env python3
"""
Arcoa Nexus Streamlined Pointing Device Test

Efficient touchpad and pointer stick test optimized for high-volume testing.
"""
import datetime
import tkinter as tk
from tkinter import ttk
from typing import Dict, Any, Callable, Optional


class StreamlinedPointingTest(tk.Toplevel):
    """Efficient test for touchpad and pointer stick functionality."""
    def __init__(self, parent, log_callback: Optional[Callable] = None):
        super().__init__(parent)
        self.parent = parent
        self.log_callback = log_callback
        self.title("Touchpad & Pointer Test")
        self.geometry("600x500")
        
        # Center window
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f"{width}x{height}+{x}+{y}")
        
        # Test state variables
        self.touchpad_squares_hit = {"top": False, "right": False, "bottom": False, "left": False}
        self.pointer_entered_circle = False
        self.pointer_exited_circle = False
        
        # Button tracking
        self.buttons_clicked = {
            "left": False,
            "middle": False,
            "right": False
        }
        
        # Set up the UI
        self.setup_ui()
        
        # Make window modal
        self.transient(parent)
        self.grab_set()
        
        # Ensure window gets focus
        self.focus_force()
        self.lift()
        self.after(100, self.focus_force)
        
        # Start log and center cursor
        self.log("Pointing Device Test Started - Complete all highlighted tasks")
        self.center_cursor()
        
        # Start periodic check for test progress
        self.check_progress()
    
    def log(self, message, level="info"):
        """Log a message using the callback if available"""
        if self.log_callback:
            self.log_callback(message, level)
    
    def setup_ui(self):
        """Set up the streamlined test UI"""
        # Main container
        main_frame = ttk.Frame(self, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Instructions at top
        instructions_frame = ttk.Frame(main_frame)
        instructions_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(
            instructions_frame, 
            text="Complete ALL tests below (u2022 = not done, u2713 = completed)", 
            font=("Arial", 11, "bold")
        ).pack(side=tk.LEFT)
        
        # Main test canvas - all tests in one
        self.canvas = tk.Canvas(main_frame, bg="white")
        self.canvas.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # Track mouse events
        self.canvas.bind("<Motion>", self.on_motion)
        self.canvas.bind("<Button-1>", lambda e: self.on_button_click(e, "left"))
        self.canvas.bind("<Button-2>", lambda e: self.on_button_click(e, "middle"))
        self.canvas.bind("<Button-3>", lambda e: self.on_button_click(e, "right"))
        
        # Status tracking frame 
        status_frame = ttk.Frame(main_frame)
        status_frame.pack(fill=tk.X, pady=(5, 0))
        
        # Touchpad direction status
        touchpad_frame = ttk.LabelFrame(status_frame, text="Touchpad Movement")
        touchpad_frame.pack(side=tk.LEFT, padx=(0, 5), fill=tk.X, expand=True)
        
        self.direction_status = {}
        for i, direction in enumerate(["top", "right", "bottom", "left"]):
            self.direction_status[direction] = ttk.Label(
                touchpad_frame, 
                text=f"{direction.title()}: u2022", 
                foreground="#555555"
            )
            self.direction_status[direction].grid(row=0, column=i, padx=5, pady=2)
        
        # Button status
        button_frame = ttk.LabelFrame(status_frame, text="Buttons")
        button_frame.pack(side=tk.LEFT, padx=(0, 5), fill=tk.X, expand=True)
        
        self.button_status = {}
        for i, button in enumerate(["left", "middle", "right"]):
            self.button_status[button] = ttk.Label(
                button_frame, 
                text=f"{button.title()}: u2022", 
                foreground="#555555"
            )
            self.button_status[button].grid(row=0, column=i, padx=5, pady=2)
        
        # Pointer stick status
        pointer_frame = ttk.LabelFrame(status_frame, text="Pointer Stick")
        pointer_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        self.pointer_status = ttk.Label(
            pointer_frame,
            text="TrackPoint: u2022", 
            foreground="#555555"
        )
        self.pointer_status.grid(row=0, column=0, padx=5, pady=2)
        
        self.skip_pointer_button = ttk.Button(
            pointer_frame,
            text="Skip (N/A)",
            command=self.skip_pointer_test,
            width=8
        )
        self.skip_pointer_button.grid(row=0, column=1, padx=5, pady=2)
        
        # Bottom controls
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(10, 0))
        
        # Progress indicator
        self.progress_var = tk.StringVar(value="Incomplete...")
        ttk.Label(control_frame, textvariable=self.progress_var).pack(side=tk.LEFT)
        
        # Done button - enabled when minimum tests complete
        self.done_button = ttk.Button(
            control_frame,
            text="Done",
            state="disabled",
            command=self.finish
        )
        self.done_button.pack(side=tk.RIGHT)
        
        # Draw test elements after the window is fully created
        self.after(100, self.create_test_elements)
    
    def create_test_elements(self):
        """Create all test elements on a single canvas"""
        canvas = self.canvas
        width = canvas.winfo_width()
        height = canvas.winfo_height()
        
        # Divide into two main areas
        divider_y = height * 0.55
        canvas.create_line(0, divider_y, width, divider_y, fill="#cccccc", dash=(4, 2))
        
        # ===== TOP SECTION: Touchpad Movement Test =====
        top_center_x = width // 2
        top_center_y = divider_y // 2
        
        # Draw section label
        canvas.create_text(
            top_center_x, 20,
            text="TEST 1: Touchpad Movement & Buttons",
            font=("Arial", 10, "bold"),
            fill="#333333"
        )
        
        # Draw center point
        square_size = 35
        offset = 70  # Distance from center to square
        
        canvas.create_oval(
            top_center_x - 5, top_center_y - 5, 
            top_center_x + 5, top_center_y + 5, 
            fill="black"
        )
        
        # Create touchpad targets
        self.touchpad_targets = {
            "top": canvas.create_rectangle(
                top_center_x - square_size//2, top_center_y - offset - square_size,
                top_center_x + square_size//2, top_center_y - offset,
                fill="#3498db", tags="top"
            ),
            "right": canvas.create_rectangle(
                top_center_x + offset, top_center_y - square_size//2,
                top_center_x + offset + square_size, top_center_y + square_size//2,
                fill="#2ecc71", tags="right"
            ),
            "bottom": canvas.create_rectangle(
                top_center_x - square_size//2, top_center_y + offset,
                top_center_x + square_size//2, top_center_y + offset + square_size,
                fill="#e74c3c", tags="bottom"
            ),
            "left": canvas.create_rectangle(
                top_center_x - offset - square_size, top_center_y - square_size//2,
                top_center_x - offset, top_center_y + square_size//2,
                fill="#f1c40f", tags="left"
            )
        }
        
        # Add minimal instruction
        canvas.create_text(
            top_center_x, top_center_y - 25,
            text="Touch all 4 colored squares",
            font=("Arial", 9),
            fill="#555555"
        )
        
        # ===== BOTTOM SECTION: Pointer Stick Test =====
        bottom_center_x = width // 2
        bottom_center_y = divider_y + (height - divider_y) // 2
        
        # Draw section label
        canvas.create_text(
            bottom_center_x, divider_y + 20,
            text="TEST 2: Pointer Stick (TrackPoint) - Skip if not present",
            font=("Arial", 10, "bold"),
            fill="#333333"
        )
        
        # Draw pointer stick target
        radius = 40
        self.pointer_circle = canvas.create_oval(
            bottom_center_x - radius, bottom_center_y - radius,
            bottom_center_x + radius, bottom_center_y + radius,
            fill="#9b59b6", outline="#8e44ad", width=2
        )
        
        # Draw instruction text in center
        canvas.create_text(
            bottom_center_x, bottom_center_y,
            text="1. IN with touchpad\n2. OUT with pointer",
            font=("Arial", 9),
            fill="white"
        )
    
    def center_cursor(self):
        """Try to position cursor at center of canvas"""
        try:
            width = self.canvas.winfo_width()
            height = self.canvas.winfo_height()
            self.canvas.event_generate("<Motion>", warp=True, x=width//2, y=height//4)
        except Exception:
            pass  # If we can't move the cursor, that's okay
    
    def on_motion(self, event):
        """Handle all motion events on the canvas"""
        x, y = event.x, event.y
        height = self.canvas.winfo_height()
        divider_y = height * 0.55
        
        # Touchpad target detection (top section)
        if y < divider_y:
            self.check_touchpad_targets(x, y)
        # Pointer stick test (bottom section)
        else:
            self.check_pointer_stick_target(x, y)
    
    def check_touchpad_targets(self, x, y):
        """Check if touchpad moved to hit any targets"""
        for direction, target_id in self.touchpad_targets.items():
            if not self.touchpad_squares_hit[direction]:
                coords = self.canvas.coords(target_id)
                if coords[0] <= x <= coords[2] and coords[1] <= y <= coords[3]:
                    self.touchpad_squares_hit[direction] = True
                    self.direction_status[direction].configure(text=f"{direction.title()}: u2713", foreground="green")
                    # Visual indication it's been hit
                    self.canvas.itemconfig(target_id, stipple="gray50")
    
    def check_pointer_stick_target(self, x, y):
        """Check pointer stick circle interaction"""
        # Get circle dimensions
        circle_coords = self.canvas.coords(self.pointer_circle)
        center_x = (circle_coords[0] + circle_coords[2]) / 2
        center_y = (circle_coords[1] + circle_coords[3]) / 2
        radius = (circle_coords[2] - circle_coords[0]) / 2
        
        # Calculate distance from center
        distance = ((x - center_x) ** 2 + (y - center_y) ** 2) ** 0.5
        
        # Check if cursor is inside or outside circle
        if not self.pointer_entered_circle and distance <= radius:
            # Entered circle with touchpad - step 1 complete
            self.pointer_entered_circle = True
            self.canvas.itemconfig(self.pointer_circle, fill="#e74c3c")  # Change to red
            
        elif self.pointer_entered_circle and not self.pointer_exited_circle and distance > radius:
            # Exited circle with pointer stick - test passed
            self.pointer_exited_circle = True
            self.pointer_status.configure(text="TrackPoint: u2713", foreground="green")
            self.canvas.itemconfig(self.pointer_circle, fill="#2ecc71")  # Change to green
            self.skip_pointer_button.configure(state="disabled")
    
    def on_button_click(self, event, button_type):
        """Handle button clicks"""
        x, y = event.x, event.y
        
        if not self.buttons_clicked[button_type]:
            self.buttons_clicked[button_type] = True
            self.button_status[button_type].configure(text=f"{button_type.title()}: u2713", foreground="green")
            
            # Draw indicators based on button type
            if button_type == "left":
                self.canvas.create_oval(x-4, y-4, x+4, y+4, fill="blue")
            elif button_type == "middle":
                self.canvas.create_polygon(x, y-6, x+6, y, x, y+6, x-6, y, fill="purple")
            elif button_type == "right":
                self.canvas.create_rectangle(x-4, y-4, x+4, y+4, fill="red")
    
    def skip_pointer_test(self):
        """Skip the pointer stick test for laptops without one"""
        self.pointer_status.configure(text="TrackPoint: N/A", foreground="gray")
        self.skip_pointer_button.configure(state="disabled")
    
    def check_progress(self):
        """Check if testing is complete"""
        touchpad_done = all(self.touchpad_squares_hit.values())
        buttons_done = self.buttons_clicked["left"] and self.buttons_clicked["right"]
        pointer_done = self.pointer_exited_circle or self.skip_pointer_button.cget("state") == "disabled"
        
        # Enable done button once minimum requirements met
        if touchpad_done and buttons_done and pointer_done:
            if self.done_button.cget("state") == "disabled":
                self.done_button.configure(state="normal")
                self.progress_var.set("All tests complete!")
                self.log("All pointing device tests complete.")
        else:
            remaining = []
            if not touchpad_done:
                remaining.append("touchpad movement")
            if not buttons_done:
                remaining.append("L/R buttons")
            if not pointer_done:
                remaining.append("pointer stick")
                
            self.progress_var.set(f"Incomplete: {', '.join(remaining)}")
            self.after(200, self.check_progress)
    
    def finish(self):
        """Complete the test and close the window"""
        self.destroy()
    
    def get_results(self) -> Dict[str, Any]:
        """Return streamlined test results"""
        touchpad_complete = all(self.touchpad_squares_hit.values())
        buttons_complete = self.buttons_clicked["left"] and self.buttons_clicked["right"]
        
        # Device has a pointer stick if that test was completed
        has_pointer_stick = self.pointer_exited_circle
        pointer_skipped = not has_pointer_stick and self.skip_pointer_button.cget("state") == "disabled"
        
        # Build result notes
        status = "pass" if (touchpad_complete and buttons_complete) else "fail"
        notes = "Touchpad function verified."
        
        if self.buttons_clicked["middle"]:
            notes += " Middle button detected."
            
        if has_pointer_stick:
            notes += " Pointer stick (TrackPoint) functional."
        elif pointer_skipped:
            notes += " No pointer stick on this device."
        
        return {
            "status": status,
            "touchpad": {
                "movement": touchpad_complete,
                "directions": {d: hit for d, hit in self.touchpad_squares_hit.items()}
            },
            "buttons": {
                "left": self.buttons_clicked["left"],
                "middle": self.buttons_clicked["middle"],
                "right": self.buttons_clicked["right"]
            },
            "pointer_stick": {
                "present": has_pointer_stick,
                "skipped": pointer_skipped
            },
            "notes": notes
        }


def run_pointing_device_test(parent_window, log_callback=None) -> Dict[str, Any]:
    """Run the streamlined pointing device test and return results"""
    start_dt = datetime.datetime.now()
    
    # Create and show the test window
    pointing_test = StreamlinedPointingTest(parent_window, log_callback)
    
    # Wait for the test window to close
    parent_window.wait_window(pointing_test)
    
    # Compile the results
    end_dt = datetime.datetime.now()
    
    try:
        test_details = pointing_test.get_results()
    except Exception as e:
        test_details = {
            "status": "fail",
            "notes": f"Test was closed unexpectedly. Error: {str(e)}"
        }
    
    return {
        "test_details": test_details,
        "started_at": start_dt.isoformat(),
        "finished_at": end_dt.isoformat(),
    }
