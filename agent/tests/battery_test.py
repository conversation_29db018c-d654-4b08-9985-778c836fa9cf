#!/usr/bin/env python3
"""
Arcoa Nexus Battery Test Module

This module provides battery health testing functionality for laptops and portable devices.
It checks battery capacity, wear level, charge cycles, and overall health status.
"""
import datetime
import time
import platform
import subprocess
from typing import Dict, Any, Optional, Callable, List, Tuple

import psutil

from agent.tests.test_framework import (
    TestCategory, TestSeverity, TestStatus, test
)


def get_battery_info() -> Dict[str, Any]:
    """
    Get detailed battery information using platform-specific methods.
    
    Returns:
        Dictionary with battery information
    """
    battery_info = {
        "present": False,
        "percent": 0,
        "status": "unknown",
        "power_plugged": False,
        "time_left": 0,
        "wear_level": 0,
        "design_capacity": 0,
        "current_capacity": 0,
        "cycle_count": 0,
        "technology": "unknown",
        "manufacturer": "unknown",
        "serial_number": "unknown",
    }
    
    # Try using psutil first (cross-platform)
    try:
        battery = psutil.sensors_battery()
        if battery:
            battery_info["present"] = True
            battery_info["percent"] = battery.percent
            battery_info["power_plugged"] = battery.power_plugged
            battery_info["time_left"] = battery.secsleft if battery.secsleft >= 0 else 0
    except (AttributeError, Exception):
        pass
    
    # Platform-specific detailed information
    system = platform.system()
    
    if system == "Linux":
        try:
            # Try to get detailed battery info from /sys/class/power_supply
            import glob
            battery_paths = glob.glob("/sys/class/power_supply/BAT*")
            
            if battery_paths:
                bat_path = battery_paths[0]  # Use first battery if multiple
                
                # Read battery status
                with open(f"{bat_path}/status", "r") as f:
                    battery_info["status"] = f.read().strip()
                
                # Try to read design capacity
                try:
                    with open(f"{bat_path}/energy_full_design", "r") as f:
                        battery_info["design_capacity"] = int(f.read().strip())
                except (FileNotFoundError, ValueError):
                    try:
                        with open(f"{bat_path}/charge_full_design", "r") as f:
                            battery_info["design_capacity"] = int(f.read().strip())
                    except (FileNotFoundError, ValueError):
                        pass
                
                # Try to read current capacity
                try:
                    with open(f"{bat_path}/energy_full", "r") as f:
                        battery_info["current_capacity"] = int(f.read().strip())
                except (FileNotFoundError, ValueError):
                    try:
                        with open(f"{bat_path}/charge_full", "r") as f:
                            battery_info["current_capacity"] = int(f.read().strip())
                    except (FileNotFoundError, ValueError):
                        pass
                
                # Try to read cycle count
                try:
                    with open(f"{bat_path}/cycle_count", "r") as f:
                        battery_info["cycle_count"] = int(f.read().strip())
                except (FileNotFoundError, ValueError):
                    pass
                
                # Try to read technology
                try:
                    with open(f"{bat_path}/technology", "r") as f:
                        battery_info["technology"] = f.read().strip()
                except (FileNotFoundError, ValueError):
                    pass
                
                # Try to read manufacturer
                try:
                    with open(f"{bat_path}/manufacturer", "r") as f:
                        battery_info["manufacturer"] = f.read().strip()
                except (FileNotFoundError, ValueError):
                    pass
                
                # Try to read serial number
                try:
                    with open(f"{bat_path}/serial_number", "r") as f:
                        battery_info["serial_number"] = f.read().strip()
                except (FileNotFoundError, ValueError):
                    pass
                
                # Calculate wear level if we have both capacities
                if battery_info["design_capacity"] > 0 and battery_info["current_capacity"] > 0:
                    battery_info["wear_level"] = 100 - (battery_info["current_capacity"] / battery_info["design_capacity"] * 100)
        except Exception:
            pass
            
    elif system == "Windows":
        try:
            # Use WMIC to get battery information
            wmic_result = subprocess.check_output(
                ["wmic", "path", "Win32_Battery", "get", "BatteryStatus,DesignCapacity,FullChargeCapacity,Name,DeviceID"],
                universal_newlines=True
            )
            
            lines = wmic_result.strip().split("\n")
            if len(lines) > 1:  # Header + at least one battery
                battery_info["present"] = True
                
                # Try to get more detailed info with PowerShell
                try:
                    ps_cmd = "Get-WmiObject -Class BatteryStaticData -Namespace ROOT\\WMI | Select-Object ManufactureName,SerialNumber,DesignedCapacity | ConvertTo-Json"
                    ps_result = subprocess.check_output(["powershell", "-Command", ps_cmd], universal_newlines=True)
                    import json
                    battery_static = json.loads(ps_result)
                    
                    battery_info["manufacturer"] = battery_static.get("ManufactureName", "unknown")
                    battery_info["serial_number"] = battery_static.get("SerialNumber", "unknown")
                    battery_info["design_capacity"] = battery_static.get("DesignedCapacity", 0)
                    
                    # Get cycle count and current capacity
                    ps_cmd = "Get-WmiObject -Class BatteryFullChargedCapacity -Namespace ROOT\\WMI | Select-Object FullChargedCapacity | ConvertTo-Json"
                    ps_result = subprocess.check_output(["powershell", "-Command", ps_cmd], universal_newlines=True)
                    battery_capacity = json.loads(ps_result)
                    battery_info["current_capacity"] = battery_capacity.get("FullChargedCapacity", 0)
                    
                    # Calculate wear level
                    if battery_info["design_capacity"] > 0 and battery_info["current_capacity"] > 0:
                        battery_info["wear_level"] = 100 - (battery_info["current_capacity"] / battery_info["design_capacity"] * 100)
                    
                    # Try to get cycle count (not always available)
                    try:
                        ps_cmd = "Get-WmiObject -Class BatteryCycleCount -Namespace ROOT\\WMI | Select-Object CycleCount | ConvertTo-Json"
                        ps_result = subprocess.check_output(["powershell", "-Command", ps_cmd], universal_newlines=True)
                        battery_cycles = json.loads(ps_result)
                        battery_info["cycle_count"] = battery_cycles.get("CycleCount", 0)
                    except Exception:
                        pass
                except Exception:
                    pass
        except Exception:
            pass
            
    elif system == "Darwin":  # macOS
        try:
            # Use system_profiler to get battery information
            cmd = ["system_profiler", "SPPowerDataType", "-json"]
            result = subprocess.check_output(cmd, universal_newlines=True)
            import json
            power_data = json.loads(result)
            
            if "SPPowerDataType" in power_data and len(power_data["SPPowerDataType"]) > 0:
                power_info = power_data["SPPowerDataType"][0]
                
                if "sppower_battery_model_info" in power_info:
                    battery_info["present"] = True
                    bat_info = power_info["sppower_battery_model_info"]
                    
                    # Extract available information
                    if "sppower_battery_health_info" in power_info:
                        health_info = power_info["sppower_battery_health_info"]
                        battery_info["cycle_count"] = health_info.get("sppower_battery_cycle_count", 0)
                        battery_info["status"] = health_info.get("sppower_battery_health", "unknown")
                    
                    battery_info["manufacturer"] = "Apple"
                    battery_info["technology"] = bat_info.get("sppower_battery_technology", "unknown")
                    battery_info["serial_number"] = bat_info.get("sppower_battery_serial_number", "unknown")
                    
                    # Current capacity and design capacity
                    if "sppower_battery_design_capacity" in bat_info:
                        battery_info["design_capacity"] = bat_info["sppower_battery_design_capacity"]
                    if "sppower_battery_max_capacity" in bat_info:
                        battery_info["current_capacity"] = bat_info["sppower_battery_max_capacity"]
                    
                    # Calculate wear level
                    if battery_info["design_capacity"] > 0 and battery_info["current_capacity"] > 0:
                        battery_info["wear_level"] = 100 - (battery_info["current_capacity"] / battery_info["design_capacity"] * 100)
        except Exception:
            pass
    
    return battery_info


def _evaluate_battery_health(battery_info: Dict[str, Any]) -> Tuple[str, str, List[str]]:
    """
    Evaluate battery health based on collected information.
    
    Args:
        battery_info: Dictionary with battery information
        
    Returns:
        Tuple of (status, grade, notes)
    """
    if not battery_info.get("present", False):
        return TestStatus.SKIPPED.value, "N/A", ["No battery detected"]
    
    notes = []
    status = TestStatus.PASS.value
    grade = "A"
    
    # Check wear level
    wear_level = battery_info.get("wear_level", 0)
    
    # Company standard: <60% health (>40% wear) is a fail
    if wear_level > 40:
        status = TestStatus.FAIL.value
        grade = "F"
        notes.append(f"Battery health below minimum standard: {100-wear_level:.1f}% health ({wear_level:.1f}% wear)")
    else:
        # Still grade the battery even if it passes
        if wear_level > 30:
            grade = "C"
            notes.append(f"Battery health acceptable: {100-wear_level:.1f}% health ({wear_level:.1f}% wear)")
        elif wear_level > 20:
            grade = "B"
            notes.append(f"Battery health good: {100-wear_level:.1f}% health ({wear_level:.1f}% wear)")
        else:
            notes.append(f"Battery health excellent: {100-wear_level:.1f}% health ({wear_level:.1f}% wear)")
    
    # Check cycle count
    cycle_count = battery_info.get("cycle_count", 0)
    if cycle_count > 1000:
        notes.append(f"High cycle count: {cycle_count} cycles")
    elif cycle_count > 500:
        notes.append(f"Moderate cycle count: {cycle_count} cycles")
    elif cycle_count > 0:
        notes.append(f"Low cycle count: {cycle_count} cycles")
    
    # Check charge percentage
    percent = battery_info.get("percent", 0)
    if percent < 20:
        notes.append(f"Low charge: {percent}%")
    
    return status, grade, notes


@test(
    category=TestCategory.BATTERY,
    severity=TestSeverity.CRITICAL,
    description="Tests battery health, capacity, and wear level"
)
def run_battery_test(
    log_callback: Optional[Callable[[str, str], None]] = None
) -> Dict[str, Any]:
    """
    Run a battery health test.
    
    Args:
        log_callback: Optional callback for logging progress
        
    Returns:
        Dictionary with test results
    """
    if log_callback:
        log_callback("Starting battery health test...", "info")
    
    start_dt = datetime.datetime.now()
    
    # Get battery information
    battery_info = get_battery_info()
    
    if not battery_info.get("present", False):
        if log_callback:
            log_callback("No battery detected", "warning")
        
        end_dt = datetime.datetime.now()
        return {
            "status": TestStatus.SKIPPED.value,
            "started_at": start_dt.isoformat(),
            "finished_at": end_dt.isoformat(),
            "metrics": {},
            "notes": "No battery detected",
            "grade": "N/A"
        }
    
    if log_callback:
        log_callback(f"Battery detected: {battery_info.get('percent', 0)}% charged", "info")
        
        if battery_info.get("power_plugged", False):
            log_callback("AC power connected", "info")
        else:
            log_callback("Running on battery power", "info")
    
    # Evaluate battery health
    status, grade, notes = _evaluate_battery_health(battery_info)
    
    # Log results
    if log_callback:
        for note in notes:
            log_callback(note, "info" if status == TestStatus.PASS.value else "warning")
        
        if status == TestStatus.PASS.value:
            log_callback("Battery test passed", "success")
        elif status == TestStatus.WARNING.value:
            log_callback("Battery test passed with warnings", "warning")
        else:
            log_callback("Battery test failed", "error")
    
    end_dt = datetime.datetime.now()
    
    # Format metrics for reporting
    metrics = {
        "percent_charged": battery_info.get("percent", 0),
        "wear_level_percent": round(battery_info.get("wear_level", 0), 1),
        "cycle_count": battery_info.get("cycle_count", 0),
        "design_capacity": battery_info.get("design_capacity", 0),
        "current_capacity": battery_info.get("current_capacity", 0),
        "power_plugged": battery_info.get("power_plugged", False),
        "technology": battery_info.get("technology", "unknown"),
        "manufacturer": battery_info.get("manufacturer", "unknown"),
        "serial_number": battery_info.get("serial_number", "unknown"),
    }
    
    return {
        "status": status,
        "started_at": start_dt.isoformat(),
        "finished_at": end_dt.isoformat(),
        "metrics": metrics,
        "notes": "\n".join(notes),
        "grade": grade
    }


@test(
    name="Battery Discharge Test",
    category=TestCategory.BATTERY,
    severity=TestSeverity.MEDIUM,
    description="Tests battery discharge rate under load"
)
def run_battery_discharge_test(
    duration_seconds: int = 60,
    log_callback: Optional[Callable[[str, str], None]] = None
) -> Dict[str, Any]:
    """
    Run a battery discharge test under load.
    
    Args:
        duration_seconds: Test duration in seconds
        log_callback: Optional callback for logging progress
        
    Returns:
        Dictionary with test results
    """
    if log_callback:
        log_callback("Starting battery discharge test...", "info")
    
    start_dt = datetime.datetime.now()
    
    # Get initial battery information
    initial_battery = get_battery_info()
    
    if not initial_battery.get("present", False):
        if log_callback:
            log_callback("No battery detected", "warning")
        
        end_dt = datetime.datetime.now()
        return {
            "status": TestStatus.SKIPPED.value,
            "started_at": start_dt.isoformat(),
            "finished_at": end_dt.isoformat(),
            "metrics": {},
            "notes": "No battery detected",
            "grade": "N/A"
        }
    
    if initial_battery.get("power_plugged", False):
        if log_callback:
            log_callback("AC power connected. Please unplug to run discharge test.", "warning")
        
        end_dt = datetime.datetime.now()
        return {
            "status": TestStatus.SKIPPED.value,
            "started_at": start_dt.isoformat(),
            "finished_at": end_dt.isoformat(),
            "metrics": {},
            "notes": "AC power connected. Cannot run discharge test.",
            "grade": "N/A"
        }
    
    initial_percent = initial_battery.get("percent", 0)
    
    if log_callback:
        log_callback(f"Initial battery level: {initial_percent}%", "info")
        log_callback(f"Running discharge test for {duration_seconds} seconds...", "info")
    
    # Create some CPU load to accelerate discharge
    from multiprocessing import Pool, cpu_count
    
    def cpu_load():
        end_time = time.time() + duration_seconds
        while time.time() < end_time:
            x = 1.0
            for _ in range(1000000):
                x = (x * 1.000001 + 0.000001) % 1000000
    
    # Start CPU load in separate processes
    with Pool(processes=max(1, cpu_count() - 1)) as pool:
        pool.apply_async(cpu_load)
        
        # Monitor battery during test
        readings = []
        start_time = time.time()
        end_time = start_time + duration_seconds
        
        while time.time() < end_time:
            current_battery = get_battery_info()
            current_percent = current_battery.get("percent", 0)
            readings.append((time.time() - start_time, current_percent))
            
            if log_callback and len(readings) % 5 == 0:
                log_callback(f"Current battery level: {current_percent}%", "info")
            
            # Check if AC was plugged in
            if current_battery.get("power_plugged", False):
                if log_callback:
                    log_callback("AC power connected during test. Aborting.", "warning")
                
                end_dt = datetime.datetime.now()
                return {
                    "status": TestStatus.SKIPPED.value,
                    "started_at": start_dt.isoformat(),
                    "finished_at": end_dt.isoformat(),
                    "metrics": {},
                    "notes": "AC power connected during test. Test aborted.",
                    "grade": "N/A"
                }
            
            time.sleep(max(1, duration_seconds / 20))  # Take ~20 readings during the test
    
    # Get final battery information
    final_battery = get_battery_info()
    final_percent = final_battery.get("percent", 0)
    
    if log_callback:
        log_callback(f"Final battery level: {final_percent}%", "info")
    
    # Calculate discharge rate
    percent_drop = initial_percent - final_percent
    discharge_rate_per_hour = (percent_drop / (duration_seconds / 3600))
    estimated_runtime_hours = final_percent / discharge_rate_per_hour if discharge_rate_per_hour > 0 else 0
    
    if log_callback:
        log_callback(f"Battery discharged {percent_drop}% in {duration_seconds/60:.1f} minutes", "info")
        log_callback(f"Discharge rate: {discharge_rate_per_hour:.2f}% per hour", "info")
        log_callback(f"Estimated runtime remaining: {estimated_runtime_hours:.1f} hours", "info")
    
    # Evaluate results
    notes = []
    status = TestStatus.PASS.value
    grade = "A"
    
    notes.append(f"Discharge rate: {discharge_rate_per_hour:.2f}% per hour")
    notes.append(f"Estimated runtime: {estimated_runtime_hours:.1f} hours")
    
    # Grade based on estimated runtime
    if estimated_runtime_hours < 0.5:
        status = TestStatus.FAIL.value
        grade = "F"
        notes.append("Battery runtime critically low (< 30 minutes)")
    elif estimated_runtime_hours < 1:
        status = TestStatus.FAIL.value
        grade = "D"
        notes.append("Battery runtime very poor (< 1 hour)")
    elif estimated_runtime_hours < 2:
        status = TestStatus.WARNING.value
        grade = "C"
        notes.append("Battery runtime poor (< 2 hours)")
    elif estimated_runtime_hours < 3:
        grade = "B"
        notes.append("Battery runtime acceptable (2-3 hours)")
    else:
        notes.append("Battery runtime good (> 3 hours)")
    
    # Grade based on discharge rate
    if discharge_rate_per_hour > 50:
        if status != TestStatus.FAIL.value:
            status = TestStatus.WARNING.value
        notes.append("High discharge rate (> 50% per hour)")
    
    # Check if battery dropped at all
    if percent_drop <= 0:
        status = TestStatus.FAIL.value
        grade = "F"
        notes.append("Battery level did not decrease during test - possible battery reporting issue")
    
    end_dt = datetime.datetime.now()
    
    # Format metrics for reporting
    metrics = {
        "initial_percent": initial_percent,
        "final_percent": final_percent,
        "percent_drop": percent_drop,
        "discharge_rate_per_hour": round(discharge_rate_per_hour, 2),
        "estimated_runtime_hours": round(estimated_runtime_hours, 1),
        "test_duration_seconds": duration_seconds,
        "readings": readings  # Time series data for graphing
    }
    
    return {
        "status": status,
        "started_at": start_dt.isoformat(),
        "finished_at": end_dt.isoformat(),
        "metrics": metrics,
        "notes": "\n".join(notes),
        "grade": grade
    }


@test(
    name="Battery Charge Test",
    category=TestCategory.BATTERY,
    severity=TestSeverity.MEDIUM,
    description="Tests battery charging capability and rate"
)
def run_battery_charge_test(
    duration_seconds: int = 120,
    log_callback: Optional[Callable[[str, str], None]] = None
) -> Dict[str, Any]:
    """
    Run a battery charge test.
    
    Args:
        duration_seconds: Test duration in seconds
        log_callback: Optional callback for logging progress
        
    Returns:
        Dictionary with test results
    """
    if log_callback:
        log_callback("Starting battery charge test...", "info")
    
    start_dt = datetime.datetime.now()
    
    # Get initial battery information
    initial_battery = get_battery_info()
    
    if not initial_battery.get("present", False):
        if log_callback:
            log_callback("No battery detected", "warning")
        
        end_dt = datetime.datetime.now()
        return {
            "status": TestStatus.SKIPPED.value,
            "started_at": start_dt.isoformat(),
            "finished_at": end_dt.isoformat(),
            "metrics": {},
            "notes": "No battery detected",
            "grade": "N/A"
        }
    
    if not initial_battery.get("power_plugged", False):
        if log_callback:
            log_callback("AC power not connected. Please plug in to run charge test.", "warning")
        
        end_dt = datetime.datetime.now()
        return {
            "status": TestStatus.SKIPPED.value,
            "started_at": start_dt.isoformat(),
            "finished_at": end_dt.isoformat(),
            "metrics": {},
            "notes": "AC power not connected. Cannot run charge test.",
            "grade": "N/A"
        }
    
    initial_percent = initial_battery.get("percent", 0)
    
    # Skip test if battery is already fully charged
    if initial_percent >= 99:
        if log_callback:
            log_callback("Battery already fully charged. Cannot test charging rate.", "warning")
        
        end_dt = datetime.datetime.now()
        return {
            "status": TestStatus.SKIPPED.value,
            "started_at": start_dt.isoformat(),
            "finished_at": end_dt.isoformat(),
            "metrics": {"initial_percent": initial_percent},
            "notes": "Battery already fully charged. Cannot test charging rate.",
            "grade": "N/A"
        }
    
    if log_callback:
        log_callback(f"Initial battery level: {initial_percent}%", "info")
        log_callback(f"Running charge test for {duration_seconds} seconds...", "info")
    
    # Monitor battery during test
    readings = []
    start_time = time.time()
    end_time = start_time + duration_seconds
    
    while time.time() < end_time:
        current_battery = get_battery_info()
        current_percent = current_battery.get("percent", 0)
        readings.append((time.time() - start_time, current_percent))
        
        if log_callback and len(readings) % 5 == 0:
            log_callback(f"Current battery level: {current_percent}%", "info")
        
        # Check if AC was unplugged
        if not current_battery.get("power_plugged", True):
            if log_callback:
                log_callback("AC power disconnected during test. Aborting.", "warning")
            
            end_dt = datetime.datetime.now()
            return {
                "status": TestStatus.SKIPPED.value,
                "started_at": start_dt.isoformat(),
                "finished_at": end_dt.isoformat(),
                "metrics": {},
                "notes": "AC power disconnected during test. Test aborted.",
                "grade": "N/A"
            }
        
        # Check if battery reached 100%
        if current_percent >= 100:
            if log_callback:
                log_callback("Battery reached 100% charge. Ending test early.", "info")
            break
        
        time.sleep(max(1, duration_seconds / 20))  # Take ~20 readings during the test
    
    # Get final battery information
    final_battery = get_battery_info()
    final_percent = final_battery.get("percent", 0)
    
    if log_callback:
        log_callback(f"Final battery level: {final_percent}%", "info")
    
    # Calculate charge rate
    percent_increase = final_percent - initial_percent
    actual_duration = time.time() - start_time
    charge_rate_per_hour = (percent_increase / (actual_duration / 3600))
    time_to_full_hours = (100 - final_percent) / charge_rate_per_hour if charge_rate_per_hour > 0 else 0
    
    if log_callback:
        log_callback(f"Battery charged {percent_increase}% in {actual_duration/60:.1f} minutes", "info")
        log_callback(f"Charge rate: {charge_rate_per_hour:.2f}% per hour", "info")
        log_callback(f"Estimated time to full: {time_to_full_hours:.1f} hours", "info")
    
    # Evaluate results
    notes = []
    status = TestStatus.PASS.value
    grade = "A"
    
    notes.append(f"Charge rate: {charge_rate_per_hour:.2f}% per hour")
    notes.append(f"Estimated time to full: {time_to_full_hours:.1f} hours")
    
    # Grade based on charge rate
    if charge_rate_per_hour < 10:
        status = TestStatus.FAIL.value
        grade = "F"
        notes.append("Extremely slow charging (< 10% per hour)")
    elif charge_rate_per_hour < 20:
        status = TestStatus.WARNING.value
        grade = "D"
        notes.append("Very slow charging (< 20% per hour)")
    elif charge_rate_per_hour < 30:
        status = TestStatus.WARNING.value
        grade = "C"
        notes.append("Slow charging (< 30% per hour)")
    elif charge_rate_per_hour < 40:
        grade = "B"
        notes.append("Moderate charging speed (30-40% per hour)")
    else:
        notes.append("Good charging speed (> 40% per hour)")
    
    # Check if battery increased at all
    if percent_increase <= 0:
        status = TestStatus.FAIL.value
        grade = "F"
        notes.append("Battery level did not increase during test - possible charging issue")
    
    end_dt = datetime.datetime.now()
    
    # Format metrics for reporting
    metrics = {
        "initial_percent": initial_percent,
        "final_percent": final_percent,
        "percent_increase": percent_increase,
        "charge_rate_per_hour": round(charge_rate_per_hour, 2),
        "time_to_full_hours": round(time_to_full_hours, 1),
        "test_duration_seconds": round(actual_duration, 1),
        "readings": readings  # Time series data for graphing
    }
    
    return {
        "status": status,
        "started_at": start_dt.isoformat(),
        "finished_at": end_dt.isoformat(),
        "metrics": metrics,
        "notes": "\n".join(notes),
        "grade": grade
    }


@test(
    name="Battery Full Assessment",
    category=TestCategory.BATTERY,
    severity=TestSeverity.HIGH,
    description="Comprehensive battery assessment including health, capacity, and charging"
)
def run_battery_full_assessment(
    discharge_duration: int = 60,
    charge_duration: int = 120,
    log_callback: Optional[Callable[[str, str], None]] = None
) -> Dict[str, Any]:
    """
    Run a comprehensive battery assessment including health, discharge, and charge tests.
    
    Args:
        discharge_duration: Duration of discharge test in seconds
        charge_duration: Duration of charge test in seconds
        log_callback: Optional callback for logging progress
        
    Returns:
        Dictionary with test results
    """
    if log_callback:
        log_callback("Starting comprehensive battery assessment...", "info")
    
    start_dt = datetime.datetime.now()
    
    # Run battery health test
    if log_callback:
        log_callback("Step 1: Battery health test", "info")
    
    health_result = run_battery_test(log_callback)
    
    if health_result["status"] == TestStatus.SKIPPED.value:
        if log_callback:
            log_callback("No battery detected. Skipping assessment.", "warning")
        return health_result
    
    # Determine if we should run discharge test
    battery_info = get_battery_info()
    can_run_discharge = not battery_info.get("power_plugged", True)
    
    discharge_result = None
    if can_run_discharge:
        if log_callback:
            log_callback("Step 2: Battery discharge test", "info")
        discharge_result = run_battery_discharge_test(discharge_duration, log_callback)
    else:
        if log_callback:
            log_callback("AC power connected. Skipping discharge test.", "warning")
    
    # Determine if we should run charge test
    battery_info = get_battery_info()
    can_run_charge = battery_info.get("power_plugged", False) and battery_info.get("percent", 100) < 99
    
    charge_result = None
    if can_run_charge:
        if log_callback:
            log_callback("Step 3: Battery charge test", "info")
        charge_result = run_battery_charge_test(charge_duration, log_callback)
    else:
        if log_callback:
            log_callback("Cannot run charge test. Either AC power not connected or battery already full.", "warning")
    
    # Compile results
    combined_metrics = {
        "health": health_result.get("metrics", {}),
    }
    
    if discharge_result:
        combined_metrics["discharge"] = discharge_result.get("metrics", {})
    
    if charge_result:
        combined_metrics["charge"] = charge_result.get("metrics", {})
    
    # Determine overall status and grade
    statuses = [
        health_result["status"],
        discharge_result["status"] if discharge_result else None,
        charge_result["status"] if charge_result else None
    ]
    
    grades = [
        health_result["grade"],
        discharge_result["grade"] if discharge_result else None,
        charge_result["grade"] if charge_result else None
    ]
    
    # Filter out None and SKIPPED values
    statuses = [s for s in statuses if s and s != TestStatus.SKIPPED.value]
    grades = [g for g in grades if g and g != "N/A"]
    
    # Determine worst status
    if TestStatus.FAIL.value in statuses:
        overall_status = TestStatus.FAIL.value
    elif TestStatus.WARNING.value in statuses:
        overall_status = TestStatus.WARNING.value
    elif len(statuses) > 0:
        overall_status = TestStatus.PASS.value
    else:
        overall_status = TestStatus.SKIPPED.value
    
    # Determine worst grade
    grade_values = {"A": 4, "B": 3, "C": 2, "D": 1, "F": 0}
    if grades:
        worst_grade_value = min(grade_values.get(g, 4) for g in grades)
        overall_grade = {v: k for k, v in grade_values.items()}.get(worst_grade_value, "A")
    else:
        overall_grade = "N/A"
    
    # Compile notes
    notes = []
    if health_result.get("notes"):
        notes.append("Health: " + health_result["notes"])
    
    if discharge_result and discharge_result.get("notes"):
        notes.append("Discharge: " + discharge_result["notes"])
    
    if charge_result and charge_result.get("notes"):
        notes.append("Charge: " + charge_result["notes"])
    
    end_dt = datetime.datetime.now()
    
    return {
        "status": overall_status,
        "started_at": start_dt.isoformat(),
        "finished_at": end_dt.isoformat(),
        "metrics": combined_metrics,
        "notes": "\n".join(notes),
        "grade": overall_grade,
        "tests_run": {
            "health": True,
            "discharge": discharge_result is not None,
            "charge": charge_result is not None
        }
    }










