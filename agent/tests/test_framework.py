#!/usr/bin/env python3
"""
Arcoa Nexus Test Framework

This module provides a standardized framework for implementing and running hardware tests.
It ensures consistent test execution, logging, and result formatting across all test types.
"""
import datetime
import functools
import inspect
import logging
import os
import time
import traceback
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Tuple, Union

# Configure logging
logger = logging.getLogger("arcoa.tests")


class TestStatus(str, Enum):
    """Standardized test status values."""
    PASS = "pass"
    FAIL = "fail"
    SKIPPED = "skipped"
    ERROR = "error"  # For unexpected errors during test execution
    NOT_APPLICABLE = "not_applicable" # For elements not present or not tested


class TestSeverity(str, Enum):
    """Test severity levels to categorize tests."""
    CRITICAL = "critical"  # Test failure means device is unusable
    HIGH = "high"          # Test failure indicates major functionality issue
    MEDIUM = "medium"      # Test failure affects some functionality
    LOW = "low"            # Test failure is minor or cosmetic


class TestCategory(str, Enum):
    """Categories of tests for organization."""
    SYSTEM = "system"
    CPU = "cpu"
    MEMORY = "memory"
    STORAGE = "storage"
    DISPLAY = "display"
    INPUT = "input"
    NETWORK = "network"
    BATTERY = "battery"
    SECURITY = "security"
    WIPE = "wipe"


class TestResult:
    """Standardized test result container."""

    def __init__(
        self,
        test_name: str,
        status: TestStatus,
        notes: str = "",
        details: Optional[Dict[str, Any]] = None,
        started_at: Optional[datetime.datetime] = None,
        finished_at: Optional[datetime.datetime] = None
    ):
        self.test_name = test_name
        self.status = status
        self.notes = notes
        self.details = details or {}
        self.started_at = started_at or datetime.datetime.now()
        self.finished_at = finished_at or datetime.datetime.now()

    def to_dict(self) -> Dict[str, Any]:
        """Convert test result to a dictionary for serialization."""
        return {
            "test_details": {
                "status": self.status.value if isinstance(self.status, TestStatus) else self.status,
                "notes": self.notes,
                **self.details
            },
            "started_at": self.started_at.isoformat(),
            "finished_at": self.finished_at.isoformat(),
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any], test_name: str) -> 'TestResult':
        """Create a TestResult instance from a dictionary."""
        test_details = data.get("test_details", {})
        status_value = test_details.get("status", "error")

        # Convert string status to enum if needed
        try:
            status = TestStatus(status_value)
        except ValueError:
            status = TestStatus.ERROR

        # Extract notes and other details
        notes = test_details.get("notes", "")
        details = {k: v for k, v in test_details.items() if k not in ["status", "notes"]}

        # Parse timestamps
        started_at = datetime.datetime.fromisoformat(data.get("started_at", datetime.datetime.now().isoformat()))
        finished_at = datetime.datetime.fromisoformat(data.get("finished_at", datetime.datetime.now().isoformat()))

        return cls(
            test_name=test_name,
            status=status,
            notes=notes,
            details=details,
            started_at=started_at,
            finished_at=finished_at
        )


def test(
    category: TestCategory,
    severity: TestSeverity = TestSeverity.MEDIUM,
    timeout: Optional[int] = None,
    description: str = "",
    name: Optional[str] = None
) -> Callable:
    """
    Decorator for test functions that standardizes execution and result formatting.

    Args:
        category: The test category
        severity: The test severity level
        timeout: Optional timeout in seconds
        description: Human-readable description of the test
        name: Optional custom name for the test (defaults to function name)

    Returns:
        Decorated test function
    """
    def decorator(func: Callable) -> Callable:
        # Store metadata on the function
        func.test_metadata = {
            "category": category,
            "severity": severity,
            "timeout": timeout,
            "description": description or func.__doc__ or "",
            "name": name or func.__name__
        }

        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Dict[str, Any]:
            start_dt = datetime.datetime.now()
            test_name = name or func.__name__
            log_callback = kwargs.get('log_callback', lambda msg, lvl="info": logger.info(msg))

            # Log test start
            log_callback(f"Starting test: {test_name}", "info")

            try:
                # Apply timeout if specified
                if timeout:
                    # TODO: Implement timeout mechanism
                    pass

                # Run the actual test
                result = func(*args, **kwargs)

                # If result is already in the expected format, return it
                if isinstance(result, dict) and "test_details" in result:
                    # Add metadata if not present
                    if "metadata" not in result:
                        result["metadata"] = func.test_metadata
                    return result

                # Otherwise, wrap the result
                if isinstance(result, tuple) and len(result) == 2:
                    # Assume (status, notes) format
                    status, notes = result
                    details = {}
                elif isinstance(result, dict):
                    # Assume dictionary with status and notes
                    status = result.get("status", TestStatus.ERROR)
                    notes = result.get("notes", "")
                    details = {k: v for k, v in result.items() if k not in ["status", "notes"]}
                else:
                    # Assume boolean result
                    status = TestStatus.PASS if result else TestStatus.FAIL
                    notes = "Test completed successfully" if result else "Test failed"
                    details = {}

            except Exception as e:
                # Handle exceptions
                status = TestStatus.ERROR
                notes = f"Test error: {str(e)}"
                details = {"traceback": traceback.format_exc()}
                log_callback(f"Test error in {test_name}: {str(e)}", "error")

            # Create standardized result
            end_dt = datetime.datetime.now()
            test_result = TestResult(
                test_name=test_name,
                status=status,
                notes=notes,
                details=details,
                started_at=start_dt,
                finished_at=end_dt
            ).to_dict()

            # Add metadata
            test_result["metadata"] = func.test_metadata

            # Log completion
            duration = (end_dt - start_dt).total_seconds()
            log_callback(f"Completed test: {test_name} ({status}) in {duration:.2f}s", "info")

            return test_result

        return wrapper

    return decorator


def get_available_tests() -> List[Dict[str, Any]]:
    """
    Discover all available tests in the tests directory.

    Returns:
        List of test metadata dictionaries
    """
    import importlib
    import pkgutil
    # Assuming agent.tests is the package where tests reside.
    # If this script is agent/tests/test_framework.py, then agent.tests refers to the parent package.
    # This import might need adjustment based on actual project structure if agent.tests is not directly importable.
    try:
        import agent.tests
    except ImportError:
        logger.error("Could not import 'agent.tests' package. Ensure it's in PYTHONPATH.")
        # Fallback: try to infer path relative to this file if it's part of the agent structure
        # This is a bit fragile and depends on file location.
        current_dir = os.path.dirname(os.path.abspath(__file__)) # .../agent/tests
        agent_dir = os.path.dirname(current_dir) # .../agent
        if os.path.basename(agent_dir) == "agent" and os.path.basename(current_dir) == "tests":
            import sys
            if agent_dir not in sys.path:
                 sys.path.insert(0, os.path.dirname(agent_dir)) # Add parent of agent to path
            try:
                import agent.tests
            except ImportError as e_fallback:
                logger.error(f"Fallback import of 'agent.tests' also failed: {e_fallback}")
                return []
        else:
            return []


    tests = []

    # Get the directory containing the tests
    # This should point to the 'agent/tests' directory
    tests_package_path = os.path.dirname(inspect.getfile(agent.tests))


    # Modules that are part of the framework or utilities, not actual tests
    NON_TEST_MODULES = ["test_framework", "profiles", "test_runner"]

    # Iterate through all modules in the tests package path
    for _, module_name, _ in pkgutil.iter_modules([tests_package_path]):
        if module_name in NON_TEST_MODULES:
            continue

        try:
            # Import the module relative to the agent.tests package
            module = importlib.import_module(f"agent.tests.{module_name}")

            # Find all functions with test_metadata
            for name, obj in inspect.getmembers(module):
                if inspect.isfunction(obj) and hasattr(obj, "test_metadata"):
                    # Add the module name and full path
                    metadata = obj.test_metadata.copy()
                    metadata["module"] = module_name
                    metadata["full_path"] = f"agent.tests.{module_name}.{name}"
                    tests.append(metadata)
        except ImportError as e:
            logger.error(f"Error importing test module {module_name}: {e}")
        except Exception as e_gen: # Catch other potential errors during inspection
            logger.error(f"Generic error processing module {module_name}: {e_gen}")


    return tests


def run_test_by_name(test_name: str, **kwargs) -> Dict[str, Any]:
    """
    Run a test by its full path name.

    Args:
        test_name: Full path to the test function (e.g., "agent.tests.cpu_test.run_basic_cpu_test")
        **kwargs: Arguments to pass to the test function

    Returns:
        Test result dictionary
    """
    import importlib

    # Split the path into module and function
    try:
        module_path, func_name = test_name.rsplit(".", 1)
    except ValueError:
        logger.error(f"Invalid test name format: {test_name}. Expected 'module.function'.")
        return TestResult(
            test_name=test_name,
            status=TestStatus.ERROR,
            notes=f"Invalid test name format: {test_name}"
        ).to_dict()

    try:
        # Import the module
        module = importlib.import_module(module_path)

        # Get the function
        func = getattr(module, func_name)

        # Run the test
        return func(**kwargs)
    except (ImportError, AttributeError) as e:
        # Handle errors
        logger.error(f"Error running test {test_name}: {e}")
        return TestResult(
            test_name=test_name,
            status=TestStatus.ERROR,
            notes=f"Failed to run test: {str(e)}"
        ).to_dict()
    except Exception as e_exec: # Catch errors during test execution if not caught by decorator
        logger.error(f"Unexpected error executing test {test_name}: {e_exec}")
        return TestResult(
            test_name=test_name,
            status=TestStatus.ERROR,
            notes=f"Unexpected error during test execution: {str(e_exec)}",
            details={"traceback": traceback.format_exc()}
        ).to_dict()