#!/usr/bin/env python3
"""
Unit Tests for the Arcoa Nexus RAM Test Module

This module contains unit tests for the RAM test module.
"""
import os
import sys
import unittest
from unittest.mock import MagicMock, patch

# Make sure we can import our own modules regardless of how we're called
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from agent.tests.ram_test import run_ram_test, run_advanced_ram_test
from agent.tests.test_framework import TestStatus


class RAMTestTests(unittest.TestCase):
    """Tests for the RAM test module."""
    
    @patch("agent.tests.ram_test.psutil")
    @patch("agent.tests.ram_test.time")
    def test_run_ram_test_success(self, mock_time, mock_psutil):
        """Test the RAM test with successful memory allocation and verification."""
        # Mock psutil.virtual_memory
        mock_mem = MagicMock()
        mock_mem.total = 16 * 1024 * 1024 * 1024  # 16 GB
        mock_mem.available = 8 * 1024 * 1024 * 1024  # 8 GB
        mock_psutil.virtual_memory.return_value = mock_mem
        
        # Mock time.time for performance measurements
        mock_time.time.side_effect = [0, 1, 2, 3]
        
        # Mock bytearray to avoid actual memory allocation
        with patch("agent.tests.ram_test.bytearray") as mock_bytearray:
            # Configure the mock bytearray to pass verification
            mock_buffer = MagicMock()
            mock_buffer.__getitem__.return_value = b'\xAA' * 4096
            mock_bytearray.return_value = mock_buffer
            
            # Run the test
            result = run_ram_test(test_size_mb=1024)
        
        # Check that the result has the expected structure
        self.assertIn("test_details", result)
        test_details = result["test_details"]
        
        # Check status
        self.assertEqual(test_details["status"], TestStatus.PASS.value)
        
        # Check memory metrics
        self.assertEqual(test_details["test_size_mb"], 1024)
        self.assertEqual(test_details["total_memory_mb"], 16 * 1024)
        self.assertEqual(test_details["available_memory_mb"], 8 * 1024)
        self.assertEqual(test_details["errors"], 0)
        
        # Check patterns tested
        self.assertIn("patterns_tested", test_details)
        self.assertGreaterEqual(len(test_details["patterns_tested"]), 1)
    
    @patch("agent.tests.ram_test.psutil")
    def test_run_ram_test_memory_error(self, mock_psutil):
        """Test the RAM test with a memory allocation error."""
        # Mock psutil.virtual_memory
        mock_mem = MagicMock()
        mock_mem.total = 4 * 1024 * 1024 * 1024  # 4 GB
        mock_mem.available = 1 * 1024 * 1024 * 1024  # 1 GB
        mock_psutil.virtual_memory.return_value = mock_mem
        
        # Mock bytearray to raise MemoryError
        with patch("agent.tests.ram_test.bytearray") as mock_bytearray:
            mock_bytearray.side_effect = MemoryError("Not enough memory")
            
            # Run the test
            result = run_ram_test(test_size_mb=2048)
        
        # Check that the result has the expected structure
        self.assertIn("test_details", result)
        test_details = result["test_details"]
        
        # Check status
        self.assertEqual(test_details["status"], TestStatus.FAIL.value)
        
        # Check error information
        self.assertIn("error_type", test_details)
        self.assertEqual(test_details["error_type"], "MemoryError")
    
    @patch("agent.tests.ram_test.psutil")
    def test_run_ram_test_verification_error(self, mock_psutil):
        """Test the RAM test with memory verification errors."""
        # Mock psutil.virtual_memory
        mock_mem = MagicMock()
        mock_mem.total = 8 * 1024 * 1024 * 1024  # 8 GB
        mock_mem.available = 4 * 1024 * 1024 * 1024  # 4 GB
        mock_psutil.virtual_memory.return_value = mock_mem
        
        # Mock bytearray to simulate memory corruption
        with patch("agent.tests.ram_test.bytearray") as mock_bytearray:
            # Configure the mock buffer to fail verification
            mock_buffer = MagicMock()
            # First chunk is correct, second chunk is corrupted
            mock_buffer.__getitem__.side_effect = [
                b'\xAA' * 4096,  # First chunk is correct
                b'\x00' * 4096,  # Second chunk is corrupted
                b'\xAA' * 4096,  # Third chunk is correct
                b'\x55' * 4096,  # Fourth chunk is correct for second pattern
                b'\x55' * 4096,  # Fifth chunk is correct for second pattern
                b'\x00' * 4096,  # Sixth chunk is corrupted for second pattern
            ]
            mock_bytearray.return_value = mock_buffer
            
            # Run the test
            result = run_ram_test(test_size_mb=16)
        
        # Check that the result has the expected structure
        self.assertIn("test_details", result)
        test_details = result["test_details"]
        
        # Check status
        self.assertEqual(test_details["status"], TestStatus.FAIL.value)
        
        # Check error count
        self.assertGreater(test_details["errors"], 0)
    
    @patch("agent.tests.ram_test.psutil")
    @patch("agent.tests.ram_test.time")
    def test_run_advanced_ram_test(self, mock_time, mock_psutil):
        """Test the advanced RAM test."""
        # Mock psutil.virtual_memory
        mock_mem = MagicMock()
        mock_mem.total = 16 * 1024 * 1024 * 1024  # 16 GB
        mock_mem.available = 8 * 1024 * 1024 * 1024  # 8 GB
        mock_psutil.virtual_memory.return_value = mock_mem
        
        # Mock time.time to control the test duration
        # First call is start time, second is for checking if time expired,
        # third is for final duration calculation
        mock_time.time.side_effect = [0, 5, 10, 15, 20, 25, 30, 35]
        
        # Mock bytearray to avoid actual memory allocation
        with patch("agent.tests.ram_test.bytearray") as mock_bytearray:
            # Configure the mock buffer to pass verification
            mock_buffer = MagicMock()
            mock_buffer.__getitem__.return_value = b'\xAA' * 4096
            mock_bytearray.return_value = mock_buffer
            
            # Run the test
            result = run_advanced_ram_test(duration_seconds=30)
        
        # Check that the result has the expected structure
        self.assertIn("test_details", result)
        test_details = result["test_details"]
        
        # Check status
        self.assertEqual(test_details["status"], TestStatus.PASS.value)
        
        # Check memory metrics
        self.assertEqual(test_details["total_memory_mb"], 16 * 1024)
        self.assertEqual(test_details["available_memory_mb"], 8 * 1024)
        self.assertGreater(test_details["cycle_size_mb"], 0)
        
        # Check cycles completed
        self.assertIn("cycles_completed", test_details)
        self.assertGreater(test_details["cycles_completed"], 0)
        
        # Check patterns tested
        self.assertIn("patterns_tested", test_details)
        self.assertGreaterEqual(len(test_details["patterns_tested"]), 1)


if __name__ == "__main__":
    unittest.main()
