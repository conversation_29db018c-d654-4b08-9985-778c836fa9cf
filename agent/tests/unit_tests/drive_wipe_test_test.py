#!/usr/bin/env python3
"""
Unit Tests for the Arcoa Nexus Drive Wipe Test Module

This module contains unit tests for the drive wipe test module.
"""
import os
import sys
import unittest
from unittest.mock import MagicMock, patch

# Make sure we can import our own modules regardless of how we're called
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from agent.tests.drive_wipe_test import (
    _list_drives, _simulate_wipe, _perform_wipe,
    run_secure_wipe_test, run_wipe_verification_test
)
from agent.tests.test_framework import TestStatus


class DriveWipeTestTests(unittest.TestCase):
    """Tests for the drive wipe test module."""
    
    @patch("agent.tests.drive_wipe_test.platform")
    @patch("agent.tests.drive_wipe_test.psutil")
    def test_list_drives_windows(self, mock_psutil, mock_platform):
        """Test listing drives on Windows."""
        # Mock platform.system
        mock_platform.system.return_value = "Windows"
        
        # Mock WMI for Windows
        with patch.dict("sys.modules", {"wmi": MagicMock()}):
            # Create a mock WMI instance
            mock_wmi = MagicMock()
            sys.modules["wmi"].WMI.return_value = mock_wmi
            
            # Create mock disk drives
            mock_disk1 = MagicMock()
            mock_disk1.DeviceID = r"\\.\PHYSICALDRIVE0"
            mock_disk2 = MagicMock()
            mock_disk2.DeviceID = r"\\.\PHYSICALDRIVE1"
            mock_wmi.Win32_DiskDrive.return_value = [mock_disk1, mock_disk2]
            
            # Call the function
            drives = _list_drives()
        
        # Check the result
        self.assertEqual(len(drives), 2)
        self.assertIn(r"\\.\PHYSICALDRIVE0", drives)
        self.assertIn(r"\\.\PHYSICALDRIVE1", drives)
    
    @patch("agent.tests.drive_wipe_test.platform")
    @patch("agent.tests.drive_wipe_test.psutil")
    def test_list_drives_linux(self, mock_psutil, mock_platform):
        """Test listing drives on Linux."""
        # Mock platform.system
        mock_platform.system.return_value = "Linux"
        
        # Mock psutil.disk_partitions
        mock_part1 = MagicMock()
        mock_part1.device = "/dev/sda1"
        mock_part2 = MagicMock()
        mock_part2.device = "/dev/sda2"
        mock_part3 = MagicMock()
        mock_part3.device = "/dev/sdb1"
        mock_psutil.disk_partitions.return_value = [mock_part1, mock_part2, mock_part3]
        
        # Call the function
        drives = _list_drives()
        
        # Check the result
        self.assertEqual(len(drives), 2)
        self.assertIn("/dev/sda", drives)
        self.assertIn("/dev/sdb", drives)
    
    def test_simulate_wipe(self):
        """Test simulating a drive wipe."""
        # Create a mock log callback
        log_callback = MagicMock()
        
        # Call the function
        _simulate_wipe(["/dev/sda", "/dev/sdb"], log_callback)
        
        # Check that the log callback was called for each drive
        self.assertEqual(log_callback.call_count, 2)
        log_callback.assert_any_call("[SIMULATION] Would wipe drive: /dev/sda")
        log_callback.assert_any_call("[SIMULATION] Would wipe drive: /dev/sdb")
    
    @patch("agent.tests.drive_wipe_test.platform")
    @patch("agent.tests.drive_wipe_test.subprocess")
    def test_perform_wipe_windows(self, mock_subprocess, mock_platform):
        """Test performing a drive wipe on Windows."""
        # Mock platform.system
        mock_platform.system.return_value = "Windows"
        
        # Mock subprocess.run
        mock_subprocess.run.return_value = MagicMock()
        
        # Create a mock log callback
        log_callback = MagicMock()
        
        # Call the function
        status, notes = _perform_wipe([r"\\.\PHYSICALDRIVE0", r"\\.\PHYSICALDRIVE1"], log_callback)
        
        # Check the result
        self.assertEqual(status, "pass")
        self.assertIn(r"\\.\PHYSICALDRIVE0: wiped", notes)
        self.assertIn(r"\\.\PHYSICALDRIVE1: wiped", notes)
        
        # Check that subprocess.run was called for each drive
        self.assertEqual(mock_subprocess.run.call_count, 2)
    
    @patch("agent.tests.drive_wipe_test.platform")
    @patch("agent.tests.drive_wipe_test.subprocess")
    def test_perform_wipe_linux_shred(self, mock_subprocess, mock_platform):
        """Test performing a drive wipe on Linux using shred."""
        # Mock platform.system
        mock_platform.system.return_value = "Linux"
        
        # Mock subprocess.call to indicate shred is available
        mock_subprocess.call.return_value = 0
        
        # Mock subprocess.run
        mock_subprocess.run.return_value = MagicMock()
        
        # Create a mock log callback
        log_callback = MagicMock()
        
        # Call the function
        status, notes = _perform_wipe(["/dev/sda", "/dev/sdb"], log_callback)
        
        # Check the result
        self.assertEqual(status, "pass")
        self.assertIn("/dev/sda: wiped", notes)
        self.assertIn("/dev/sdb: wiped", notes)
        
        # Check that subprocess.run was called for each drive with shred
        self.assertEqual(mock_subprocess.run.call_count, 2)
        mock_subprocess.run.assert_any_call(["shred", "-vz", "-n", "1", "/dev/sda"], check=True)
    
    @patch("agent.tests.drive_wipe_test._list_drives")
    @patch("agent.tests.drive_wipe_test._simulate_wipe")
    def test_run_secure_wipe_test_simulation(self, mock_simulate_wipe, mock_list_drives):
        """Test running a secure wipe test in simulation mode."""
        # Mock _list_drives
        mock_list_drives.return_value = ["/dev/sda", "/dev/sdb"]
        
        # Create a mock log callback
        log_callback = MagicMock()
        
        # Call the function
        result = run_secure_wipe_test(log_callback)
        
        # Check that the result has the expected structure
        self.assertIn("test_details", result)
        test_details = result["test_details"]
        
        # Check status
        self.assertEqual(test_details["status"], TestStatus.SKIPPED.value)
        
        # Check that _simulate_wipe was called
        mock_simulate_wipe.assert_called_once_with(["/dev/sda", "/dev/sdb"], log_callback)
    
    @patch("agent.tests.drive_wipe_test._list_drives")
    @patch("agent.tests.drive_wipe_test._perform_wipe")
    @patch.dict(os.environ, {"ARCOA_ENABLE_WIPE": "1"})
    @patch("agent.tests.drive_wipe_test.time")
    def test_run_secure_wipe_test_real(self, mock_time, mock_perform_wipe, mock_list_drives):
        """Test running a secure wipe test in real mode."""
        # Mock _list_drives
        mock_list_drives.return_value = ["/dev/sda", "/dev/sdb"]
        
        # Mock _perform_wipe
        mock_perform_wipe.return_value = ("pass", "All drives wiped successfully")
        
        # Create a mock log callback
        log_callback = MagicMock()
        
        # Call the function
        result = run_secure_wipe_test(log_callback)
        
        # Check that the result has the expected structure
        self.assertIn("test_details", result)
        test_details = result["test_details"]
        
        # Check status
        self.assertEqual(test_details["status"], "pass")
        
        # Check that _perform_wipe was called
        mock_perform_wipe.assert_called_once_with(["/dev/sda", "/dev/sdb"], log_callback)
    
    @patch("agent.tests.drive_wipe_test._list_drives")
    def test_run_secure_wipe_test_no_drives(self, mock_list_drives):
        """Test running a secure wipe test with no drives."""
        # Mock _list_drives to return an empty list
        mock_list_drives.return_value = []
        
        # Create a mock log callback
        log_callback = MagicMock()
        
        # Call the function
        result = run_secure_wipe_test(log_callback)
        
        # Check that the result has the expected structure
        self.assertIn("test_details", result)
        test_details = result["test_details"]
        
        # Check status
        self.assertEqual(test_details["status"], TestStatus.FAIL.value)
        
        # Check notes
        self.assertIn("No drives found", test_details["notes"])
    
    @patch("agent.tests.drive_wipe_test._list_drives")
    @patch("agent.tests.drive_wipe_test.platform")
    @patch("agent.tests.drive_wipe_test.subprocess")
    def test_run_wipe_verification_test(self, mock_subprocess, mock_platform, mock_list_drives):
        """Test running a wipe verification test."""
        # Mock _list_drives
        mock_list_drives.return_value = ["/dev/sda", "/dev/sdb"]
        
        # Mock platform.system
        mock_platform.system.return_value = "Linux"
        
        # Mock subprocess.run for dd command
        mock_result = MagicMock()
        mock_result.returncode = 0
        mock_result.stdout = bytes(1024)  # All zeros
        mock_subprocess.run.return_value = mock_result
        
        # Create a mock log callback
        log_callback = MagicMock()
        
        # Call the function
        result = run_wipe_verification_test(log_callback)
        
        # Check that the result has the expected structure
        self.assertIn("test_details", result)
        test_details = result["test_details"]
        
        # Check status
        self.assertEqual(test_details["status"], TestStatus.PASS.value)
        
        # Check drive results
        self.assertIn("drive_results", test_details)
        self.assertEqual(len(test_details["drive_results"]), 2)
        
        # Check that each drive was verified
        for drive_result in test_details["drive_results"]:
            self.assertEqual(drive_result["status"], TestStatus.PASS.value)


if __name__ == "__main__":
    unittest.main()
