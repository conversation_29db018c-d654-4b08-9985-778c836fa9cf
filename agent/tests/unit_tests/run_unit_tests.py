#!/usr/bin/env python3
"""
Unit Test Runner for Arcoa Nexus Test Framework

This script runs all unit tests for the test framework and generates a coverage report.
"""
import os
import sys
import unittest
import argparse
import importlib
import pkgutil
from typing import List, Optional

# Make sure we can import our own modules regardless of how we're called
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)


def discover_test_modules() -> List[str]:
    """
    Discover all test modules in the unit_tests directory.
    
    Returns:
        List of test module names
    """
    test_modules = []
    
    # Get the directory containing the unit tests
    tests_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Iterate through all modules in the unit_tests directory
    for _, module_name, is_pkg in pkgutil.iter_modules([tests_dir]):
        if not is_pkg and module_name.endswith("_test"):
            test_modules.append(f"agent.tests.unit_tests.{module_name}")
    
    return test_modules


def run_tests(test_modules: Optional[List[str]] = None, verbose: bool = False) -> bool:
    """
    Run the specified test modules.
    
    Args:
        test_modules: List of test module names to run
        verbose: Whether to show verbose output
        
    Returns:
        True if all tests passed, False otherwise
    """
    # If no test modules are specified, discover all test modules
    if test_modules is None:
        test_modules = discover_test_modules()
    
    # Create a test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add tests from each module
    for module_name in test_modules:
        try:
            # Import the module
            module = importlib.import_module(module_name)
            
            # Add tests from the module
            module_tests = loader.loadTestsFromModule(module)
            suite.addTests(module_tests)
            
            print(f"Added tests from {module_name}")
        except ImportError as e:
            print(f"Error importing {module_name}: {e}")
    
    # Run the tests
    runner = unittest.TextTestRunner(verbosity=2 if verbose else 1)
    result = runner.run(suite)
    
    # Return True if all tests passed
    return result.wasSuccessful()


def run_coverage(test_modules: Optional[List[str]] = None) -> bool:
    """
    Run the specified test modules with coverage.
    
    Args:
        test_modules: List of test module names to run
        
    Returns:
        True if all tests passed, False otherwise
    """
    try:
        import coverage
    except ImportError:
        print("Coverage package not found. Install it with 'pip install coverage'.")
        return False
    
    # If no test modules are specified, discover all test modules
    if test_modules is None:
        test_modules = discover_test_modules()
    
    # Start coverage
    cov = coverage.Coverage(
        source=["agent.tests"],
        omit=["agent/tests/unit_tests/*"]
    )
    cov.start()
    
    # Run the tests
    result = run_tests(test_modules, verbose=False)
    
    # Stop coverage
    cov.stop()
    cov.save()
    
    # Generate report
    print("\nCoverage Report:")
    cov.report()
    
    # Generate HTML report
    html_dir = os.path.join(project_root, "coverage_html")
    cov.html_report(directory=html_dir)
    print(f"\nHTML coverage report saved to {html_dir}")
    
    return result


def parse_args() -> argparse.Namespace:
    """
    Parse command-line arguments.
    
    Returns:
        Parsed arguments
    """
    parser = argparse.ArgumentParser(description="Arcoa Nexus Unit Test Runner")
    
    parser.add_argument(
        "--modules",
        nargs="*",
        help="Specific test modules to run (if not specified, runs all discovered modules)"
    )
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Show verbose output"
    )
    parser.add_argument(
        "--coverage",
        action="store_true",
        help="Generate coverage report"
    )
    parser.add_argument(
        "--list",
        action="store_true",
        help="List available test modules and exit"
    )
    
    return parser.parse_args()


def main() -> int:
    """
    Main entry point for the unit test runner.
    
    Returns:
        Exit code (0 for success, non-zero for failure)
    """
    # Parse command-line arguments
    args = parse_args()
    
    # List test modules if requested
    if args.list:
        test_modules = discover_test_modules()
        print(f"Available test modules ({len(test_modules)}):")
        for module in test_modules:
            print(f"  {module}")
        return 0
    
    # Run tests with coverage if requested
    if args.coverage:
        success = run_coverage(args.modules)
    else:
        success = run_tests(args.modules, args.verbose)
    
    # Return exit code
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
