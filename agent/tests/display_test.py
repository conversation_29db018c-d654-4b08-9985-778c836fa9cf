#!/usr/bin/env python3
"""
Arcoa Nexus Display Test Module

This module implements LCD/display testing functionality for the Arcoa Nexus diagnostics tool.
"""
import datetime
import tkinter as tk
from typing import Dict, Any, Callable, List, Tuple

from agent.tests.test_framework import test, TestCategory, TestSeverity

@test(category=TestCategory.DISPLAY, severity=TestSeverity.MEDIUM, description="Interactive LCD color cycle test.")
def run_lcd_test_gui(parent_window, log_callback=None, **kwargs) -> Dict[str, Any]:
    """
    Runs the LCD test by cycling through colors in a fullscreen window.
    
    Args:
        parent_window: Tkinter parent window
        log_callback: Optional callback function for logging messages
        
    Returns:
        Dictionary with test results and timing information
    """
    def log(message, level="info"):
        if log_callback:
            log_callback(message, level)
    
    log("Starting LCD Test...")
    
    test_colors = [("Black", "#000000"), ("White", "#FFFFFF"), 
                   ("Red", "#FF0000"), ("<PERSON>", "#00FF00"), ("Blue", "#0000FF")]
    results = {"status": "pass", "failed_colors": [], "notes": ""}
    current_color_index = 0
    
    start_dt = datetime.datetime.now()

    lcd_window = tk.Toplevel(parent_window)
    lcd_window.title("LCD Test")
    lcd_window.attributes('-fullscreen', True)
    lcd_window.configure(bg=test_colors[current_color_index][1])
    
    # Make it on top and ensure it gets focus
    lcd_window.attributes('-topmost', True)
    lcd_window.focus_force()  # Initial focus grab
    
    # Multiple focus attempts with delays to ensure window gets focus
    # This is crucial when running multiple tests in sequence
    def ensure_focus(attempt=0):
        if attempt < 5:  # Try up to 5 times
            lcd_window.focus_force()
            lcd_window.lift()
            # Schedule another attempt in 100ms
            lcd_window.after(100, lambda: ensure_focus(attempt + 1))
    
    # Start focus-ensuring process
    ensure_focus()

    info_label = tk.Label(lcd_window, 
                        text=f"Displaying: {test_colors[current_color_index][0]}\nPress ENTER if OK, ESC to mark as FAILED", 
                        font=("Arial", 20), fg="grey", bg=test_colors[current_color_index][1])
    info_label.place(relx=0.5, rely=0.5, anchor=tk.CENTER)

    def next_color(event=None, failed=False):
        nonlocal current_color_index  # Allow modification of outer scope variable
        
        if failed:
            results["status"] = "fail"
            results["failed_colors"].append(test_colors[current_color_index][0])
            log(f"LCD Color {test_colors[current_color_index][0]} reported as FAILED.", "error")
        else:
            log(f"LCD Color {test_colors[current_color_index][0]} reported as OK.", "success")
        
        current_color_index += 1
        if current_color_index < len(test_colors):
            color_name, color_hex = test_colors[current_color_index]
            lcd_window.configure(bg=color_hex)
            info_label.configure(text=f"Displaying: {color_name}\nPress ENTER if OK, ESC to mark as FAILED", bg=color_hex)
            # Adjust text color for visibility against background
            # Simple heuristic: if background is dark, use light text, and vice-versa.
            if color_hex in ["#000000"]:
                info_label.configure(fg="#FFFFFF")
            else:
                info_label.configure(fg="#333333")  # Dark grey for light backgrounds
        else:
            lcd_window.destroy()
            log("LCD Test Finished.", "success" if results["status"] == "pass" else "error")
            if results["status"] == "fail":
                results["notes"] = f"Failed on colors: {', '.join(results['failed_colors'])}"
            
    lcd_window.bind('<Return>', lambda e: next_color(failed=False))
    lcd_window.bind('<Escape>', lambda e: next_color(failed=True))
    
    # This makes the function wait until the window is closed
    parent_window.wait_window(lcd_window) 

    end_dt = datetime.datetime.now()
    
    return {
        "test_details": results,
        "started_at": start_dt.isoformat(),
        "finished_at": end_dt.isoformat(),
    }
