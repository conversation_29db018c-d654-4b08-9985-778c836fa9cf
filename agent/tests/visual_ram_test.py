#!/usr/bin/env python3
"""
Arcoa Nexus Visual RAM Test Module

This module implements a visual, full-screen RAM testing functionality
for the Arcoa Nexus diagnostics tool with progress bars and threading.
"""
import datetime
import threading
import time
import tkinter as tk
from tkinter import ttk
from typing import Dict, Any, Optional, List, Tuple

import psutil

from agent.tests.test_framework import (
    TestCategory, TestSeverity, TestStatus, test
)


class RAMTestWindow(tk.Toplevel):
    """Full-screen window for visual RAM testing."""
    
    def __init__(self, parent, test_size_mb=1024, duration_seconds=30, log_callback=None):
        """
        Initialize the RAM test window.
        
        Args:
            parent: Parent window
            test_size_mb: Memory size to test in MB
            duration_seconds: Test duration in seconds
            log_callback: Callback for logging
        """
        super().__init__(parent)
        self.parent = parent
        self.test_size_mb = test_size_mb
        self.duration_seconds = duration_seconds
        self.log_callback = log_callback or (lambda msg, lvl="info": None)
        
        # Test state variables
        self.test_running = False
        self.test_complete = False
        self.test_thread = None
        self.stop_event = threading.Event()
        self.test_result = {
            "status": TestStatus.SKIPPED.value,
            "notes": "Test not completed",
            "errors": 0,
            "patterns_tested": [],
            "cycles_completed": 0
        }
        
        # Set up the window
        self.title("RAM Test")
        self.attributes('-fullscreen', True)
        self.protocol("WM_DELETE_WINDOW", self.on_close)
        self.configure(bg="#1E1E1E")  # Dark background
        
        # Ensure window gets focus
        self.attributes('-topmost', True)
        self.focus_force()
        
        # Create UI elements
        self.create_ui()
        
        # Bind keyboard shortcuts
        self.bind('<Escape>', lambda e: self.on_close())
        
        # Start the test automatically
        self.after(500, self.start_test)
    
    def create_ui(self):
        """Create the user interface elements."""
        # Main frame
        main_frame = tk.Frame(self, bg="#1E1E1E", padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = tk.Label(
            main_frame, 
            text="RAM Test", 
            font=("Arial", 24, "bold"),
            fg="#FFFFFF",
            bg="#1E1E1E"
        )
        title_label.pack(pady=(0, 20))
        
        # System memory info
        mem_info = psutil.virtual_memory()
        total_mb = mem_info.total / (1024 * 1024)
        available_mb = mem_info.available / (1024 * 1024)
        
        self.system_info_var = tk.StringVar(
            value=f"System Memory: {total_mb:.0f} MB total, {available_mb:.0f} MB available"
        )
        system_info_label = tk.Label(
            main_frame,
            textvariable=self.system_info_var,
            font=("Arial", 12),
            fg="#FFFFFF",
            bg="#1E1E1E"
        )
        system_info_label.pack(pady=(0, 10))
        
        # Test info
        self.test_info_var = tk.StringVar(
            value=f"Test Size: {self.test_size_mb} MB | Duration: {self.duration_seconds} seconds"
        )
        test_info_label = tk.Label(
            main_frame,
            textvariable=self.test_info_var,
            font=("Arial", 12),
            fg="#FFFFFF",
            bg="#1E1E1E"
        )
        test_info_label.pack(pady=(0, 20))
        
        # Progress frame
        progress_frame = tk.Frame(main_frame, bg="#1E1E1E", padx=10, pady=10)
        progress_frame.pack(fill=tk.X, pady=10)
        
        # Overall progress
        tk.Label(
            progress_frame,
            text="Overall Progress:",
            font=("Arial", 12),
            fg="#FFFFFF",
            bg="#1E1E1E",
            anchor=tk.W
        ).pack(fill=tk.X)
        
        self.overall_progress_var = tk.DoubleVar(value=0)
        self.overall_progress = ttk.Progressbar(
            progress_frame,
            variable=self.overall_progress_var,
            length=800,
            mode='determinate'
        )
        self.overall_progress.pack(fill=tk.X, pady=(5, 15))
        
        # Current operation progress
        tk.Label(
            progress_frame,
            text="Current Operation:",
            font=("Arial", 12),
            fg="#FFFFFF",
            bg="#1E1E1E",
            anchor=tk.W
        ).pack(fill=tk.X)
        
        self.operation_var = tk.StringVar(value="Initializing...")
        operation_label = tk.Label(
            progress_frame,
            textvariable=self.operation_var,
            font=("Arial", 10),
            fg="#AAAAAA",
            bg="#1E1E1E",
            anchor=tk.W
        )
        operation_label.pack(fill=tk.X, pady=(0, 5))
        
        self.operation_progress_var = tk.DoubleVar(value=0)
        self.operation_progress = ttk.Progressbar(
            progress_frame,
            variable=self.operation_progress_var,
            length=800,
            mode='determinate'
        )
        self.operation_progress.pack(fill=tk.X, pady=(0, 15))
        
        # Status and statistics
        stats_frame = tk.Frame(main_frame, bg="#1E1E1E")
        stats_frame.pack(fill=tk.X, pady=10)
        
        # Create a 2x3 grid for statistics
        for i in range(2):
            stats_frame.columnconfigure(i, weight=1)
        
        # Statistics variables
        self.cycles_var = tk.StringVar(value="Cycles: 0")
        self.patterns_var = tk.StringVar(value="Patterns: 0")
        self.errors_var = tk.StringVar(value="Errors: 0")
        self.speed_var = tk.StringVar(value="Speed: 0 MB/s")
        self.time_left_var = tk.StringVar(value="Time Left: 0s")
        self.status_var = tk.StringVar(value="Status: Initializing")
        
        # Add statistics labels
        stats = [
            (self.cycles_var, 0, 0),
            (self.patterns_var, 0, 1),
            (self.errors_var, 0, 2),
            (self.speed_var, 1, 0),
            (self.time_left_var, 1, 1),
            (self.status_var, 1, 2)
        ]
        
        for var, row, col in stats:
            tk.Label(
                stats_frame,
                textvariable=var,
                font=("Arial", 12),
                fg="#FFFFFF",
                bg="#1E1E1E",
                width=20,
                anchor=tk.W
            ).grid(row=row, column=col, padx=10, pady=5, sticky=tk.W)
        
        # Memory visualization
        self.canvas = tk.Canvas(
            main_frame,
            width=800,
            height=200,
            bg="#2D2D2D",
            highlightthickness=0
        )
        self.canvas.pack(pady=20)
        
        # Control buttons
        button_frame = tk.Frame(main_frame, bg="#1E1E1E")
        button_frame.pack(pady=20)
        
        self.start_button = ttk.Button(
            button_frame,
            text="Start Test",
            command=self.start_test,
            state=tk.DISABLED  # Disabled initially as test starts automatically
        )
        self.start_button.pack(side=tk.LEFT, padx=10)
        
        self.stop_button = ttk.Button(
            button_frame,
            text="Stop Test",
            command=self.stop_test
        )
        self.stop_button.pack(side=tk.LEFT, padx=10)
        
        self.close_button = ttk.Button(
            button_frame,
            text="Close",
            command=self.on_close
        )
        self.close_button.pack(side=tk.LEFT, padx=10)
    
    def log(self, message, level="info"):
        """Log a message using the callback if available."""
        if self.log_callback:
            self.log_callback(message, level)
    
    def start_test(self):
        """Start the RAM test in a separate thread."""
        if self.test_running:
            return
        
        self.test_running = True
        self.test_complete = False
        self.stop_event.clear()
        
        # Update UI
        self.start_button.configure(state=tk.DISABLED)
        self.stop_button.configure(state=tk.NORMAL)
        self.status_var.set("Status: Running")
        
        # Start the test thread
        self.test_thread = threading.Thread(target=self.run_test)
        self.test_thread.daemon = True
        self.test_thread.start()
        
        # Start the UI update timer
        self.update_ui()
    
    def stop_test(self):
        """Stop the running test."""
        if not self.test_running:
            return
        
        self.log("Stopping RAM test...")
        self.stop_event.set()
        self.status_var.set("Status: Stopping...")
        self.stop_button.configure(state=tk.DISABLED)
    
    def on_close(self):
        """Handle window close event."""
        if self.test_running:
            self.stop_test()
            # Wait a bit for the test to clean up
            self.after(500, self.on_close)
            return
        
        self.destroy()
    
    def update_ui(self):
        """Update the UI with current test status."""
        if not self.test_running and self.test_complete:
            # Test is complete, update final status
            status = self.test_result.get("status", TestStatus.ERROR.value)
            if status == TestStatus.PASS.value:
                self.status_var.set("Status: PASSED")
                self.log("RAM Test completed successfully", "success")
            else:
                self.status_var.set(f"Status: FAILED ({self.test_result.get('errors', 0)} errors)")
                self.log(f"RAM Test failed with {self.test_result.get('errors', 0)} errors", "error")
            
            self.start_button.configure(state=tk.NORMAL)
            self.stop_button.configure(state=tk.DISABLED)
            return
        
        if self.test_running:
            # Schedule next update
            self.after(100, self.update_ui)
    
    def run_test(self):
        """Run the RAM test (executed in a separate thread)."""
        try:
            self.log(f"Starting RAM test with size: {self.test_size_mb} MB, duration: {self.duration_seconds}s")
            
            # Get system memory information
            mem_info = psutil.virtual_memory()
            total_mb = mem_info.total / (1024 * 1024)
            available_mb = mem_info.available / (1024 * 1024)
            
            # Update system info in UI thread
            self.parent.after(0, lambda: self.system_info_var.set(
                f"System Memory: {total_mb:.0f} MB total, {available_mb:.0f} MB available"
            ))
            
            # Determine test size based on available memory
            test_size_mb = self.test_size_mb
            try:
                # Use at most 25% of available memory, but no more than requested size
                test_size_mb = min(self.test_size_mb, int(available_mb * 0.25))
                self.parent.after(0, lambda: self.test_info_var.set(
                    f"Test Size: {test_size_mb} MB | Duration: {self.duration_seconds} seconds"
                ))
                self.log(f"Adjusted test size to {test_size_mb} MB based on available memory")
            except Exception as e:
                self.log(f"Error determining test size: {e}", "warning")
            
            test_size_bytes = test_size_mb * 1024 * 1024
            
            # Test patterns (alternating bits, all ones, all zeros)
            patterns = [b'\xAA', b'\x55', b'\xFF', b'\x00']
            pattern_names = [f"0x{p[0]:02X}" for p in patterns]
            
            # Update patterns in UI
            self.parent.after(0, lambda: self.patterns_var.set(
                f"Patterns: {len(patterns)}"
            ))
            
            # Track statistics
            cycles_completed = 0
            total_errors = 0
            start_time = time.time()
            end_time = start_time + self.duration_seconds
            
            # Main test loop
            while time.time() < end_time and not self.stop_event.is_set():
                # Update cycle count
                cycles_completed += 1
                self.parent.after(0, lambda: self.cycles_var.set(
                    f"Cycles: {cycles_completed}"
                ))
                
                # Calculate overall progress
                elapsed = time.time() - start_time
                progress = min(100, (elapsed / self.duration_seconds) * 100)
                self.parent.after(0, lambda: self.overall_progress_var.set(progress))
                
                # Calculate time left
                time_left = max(0, self.duration_seconds - elapsed)
                self.parent.after(0, lambda: self.time_left_var.set(
                    f"Time Left: {time_left:.1f}s"
                ))
                
                # Allocate memory for this cycle
                self.parent.after(0, lambda: self.operation_var.set("Allocating memory..."))
                self.parent.after(0, lambda: self.operation_progress_var.set(0))
                
                try:
                    buf = bytearray(test_size_bytes)
                except MemoryError:
                    self.log("Memory allocation failed - not enough available memory", "error")
                    self.parent.after(0, lambda: self.status_var.set("Status: FAILED (Memory allocation error)"))
                    self.test_result = {
                        "status": TestStatus.FAIL.value,
                        "notes": "Memory allocation failed",
                        "error_type": "MemoryError",
                        "cycles_completed": cycles_completed,
                        "patterns_tested": pattern_names,
                        "errors": 1
                    }
                    break
                
                # Test with each pattern
                for pattern_idx, pattern_byte in enumerate(patterns):
                    if self.stop_event.is_set():
                        break
                    
                    pattern_hex = f"0x{pattern_byte[0]:02X}"
                    
                    # Write pattern
                    self.parent.after(0, lambda: self.operation_var.set(
                        f"Writing pattern {pattern_hex} to memory..."
                    ))
                    
                    start_write = time.time()
                    for i in range(0, test_size_bytes, 4096):
                        if self.stop_event.is_set():
                            break
                        
                        chunk_size = min(4096, test_size_bytes - i)
                        buf[i:i+chunk_size] = pattern_byte * chunk_size
                        
                        # Update operation progress
                        progress = (i / test_size_bytes) * 100
                        self.parent.after(0, lambda p=progress: self.operation_progress_var.set(p))
                    
                    write_time = time.time() - start_write
                    write_speed = (test_size_bytes / write_time) / (1024 * 1024) if write_time > 0 else 0
                    
                    # Update speed in UI
                    self.parent.after(0, lambda s=write_speed: self.speed_var.set(
                        f"Speed: {s:.1f} MB/s"
                    ))
                    
                    # Verify pattern
                    self.parent.after(0, lambda: self.operation_var.set(
                        f"Verifying pattern {pattern_hex}..."
                    ))
                    
                    errors = 0
                    for i in range(0, test_size_bytes, 4096):
                        if self.stop_event.is_set():
                            break
                        
                        chunk_size = min(4096, test_size_bytes - i)
                        chunk = buf[i:i+chunk_size]
                        expected = pattern_byte * chunk_size
                        
                        if chunk != expected:
                            errors += 1
                        
                        # Update operation progress
                        progress = (i / test_size_bytes) * 100
                        self.parent.after(0, lambda p=progress: self.operation_progress_var.set(p))
                    
                    total_errors += errors
                    if errors > 0:
                        self.log(f"Pattern {pattern_hex} test found {errors} errors", "warning")
                    
                    # Update errors in UI
                    self.parent.after(0, lambda: self.errors_var.set(
                        f"Errors: {total_errors}"
                    ))
            
            # Test complete
            end_time = time.time()
            total_time = end_time - start_time
            
            # Determine test status
            status = TestStatus.PASS if total_errors == 0 else TestStatus.FAIL
            
            # Compile test details
            self.test_result = {
                "status": status.value,
                "notes": f"Completed {cycles_completed} cycles with {total_errors} errors in {total_time:.1f} seconds",
                "total_memory_mb": int(total_mb),
                "available_memory_mb": int(available_mb),
                "test_size_mb": test_size_mb,
                "patterns_tested": pattern_names,
                "cycles_completed": cycles_completed,
                "errors": total_errors,
                "duration_seconds": total_time
            }
            
            if total_errors > 0:
                self.test_result["error_details"] = f"Found {total_errors} memory errors"
            
            self.log(f"RAM test completed: {status.value}")
        
        except Exception as e:
            self.log(f"Error during RAM test: {str(e)}", "error")
            self.test_result = {
                "status": TestStatus.ERROR.value,
                "notes": f"Test error: {str(e)}",
                "error_type": type(e).__name__,
                "error_details": str(e)
            }
        
        finally:
            # Mark test as complete
            self.test_running = False
            self.test_complete = True
            
            # Final UI update will be handled by the update_ui method


@test(
    category=TestCategory.MEMORY,
    severity=TestSeverity.HIGH,
    description="Visual RAM test with real-time progress display"
)
def run_visual_ram_test(parent_window, test_size_mb=1024, duration_seconds=30, log_callback=None) -> Dict[str, Any]:
    """
    Run a visual RAM test with real-time progress display.
    
    Args:
        parent_window: Parent window for the test UI
        test_size_mb: Memory size to test in MB
        duration_seconds: Test duration in seconds
        log_callback: Optional callback for logging
        
    Returns:
        Dictionary with test results
    """
    if log_callback:
        log_callback(f"Starting visual RAM test (size: {test_size_mb} MB, duration: {duration_seconds}s)")
    
    start_dt = datetime.datetime.now()
    
    # Create and show the test window
    test_window = RAMTestWindow(
        parent_window,
        test_size_mb=test_size_mb,
        duration_seconds=duration_seconds,
        log_callback=log_callback
    )
    
    # Wait for the window to close
    parent_window.wait_window(test_window)
    
    end_dt = datetime.datetime.now()
    
    # Get test result from the window
    result = getattr(test_window, 'test_result', {
        "status": TestStatus.ERROR.value,
        "notes": "Test window closed unexpectedly"
    })
    
    return {
        "test_details": result,
        "started_at": start_dt.isoformat(),
        "finished_at": end_dt.isoformat()
    }
