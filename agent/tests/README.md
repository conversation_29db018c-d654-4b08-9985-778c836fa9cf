# Arcoa Nexus Test Framework

This directory contains the test framework and test implementations for the Arcoa Nexus diagnostics and secure wipe platform.

## Overview

The Arcoa Nexus Test Framework provides a standardized way to implement, run, and report hardware tests. It ensures consistent test execution, logging, and result formatting across all test types.

## Test Framework Components

- **test_framework.py**: Core framework with test decorator, result handling, and test discovery
- **test_runner.py**: Test runner for executing tests sequentially or in parallel
- **test_config.py**: Configuration settings for tests
- **run_tests.py**: Command-line interface for running tests

## Hardware Tests

- **cpu_test.py**: CPU functionality and stress tests
- **ram_test.py**: RAM allocation and verification tests
- **drive_wipe_test.py**: Secure drive wiping and verification
- **display_test.py**: Display functionality tests
- **keyboard_test.py**: Keyboard input tests
- **pointing_device_test.py**: Touchpad and pointing device tests

## Unit Tests

The `unit_tests/` directory contains unit tests for the test framework and hardware tests:

- **test_framework_test.py**: Tests for the core framework
- **test_runner_test.py**: Tests for the test runner
- **test_config_test.py**: Tests for the configuration module
- **cpu_test_test.py**: Tests for the CPU tests
- **ram_test_test.py**: Tests for the RAM tests
- **drive_wipe_test_test.py**: Tests for the drive wipe tests

## Usage

### Running Tests from the Command Line

```bash
# Run the default test suite
python agent/run_tests.py

# Run a specific test suite
python agent/run_tests.py --suite full

# Run specific tests
python agent/run_tests.py --tests agent.tests.cpu_test.run_basic_cpu_test agent.tests.ram_test.run_ram_test

# Run tests in parallel
python agent/run_tests.py --parallel --workers 4

# List available tests
python agent/run_tests.py --list

# Filter tests by category
python agent/run_tests.py --categories cpu memory

# Save test reports to a directory
python agent/run_tests.py --report-dir reports
```

### Writing a New Test

To create a new test, use the `@test` decorator from the test framework:

```python
from agent.tests.test_framework import TestCategory, TestSeverity, TestStatus, test

@test(
    category=TestCategory.CPU,
    severity=TestSeverity.HIGH,
    description="Example test that checks CPU functionality"
)
def run_example_test(log_callback=None) -> Dict[str, Any]:
    """
    Example test function.
    
    Args:
        log_callback: Optional callback for logging
        
    Returns:
        Dictionary with test results
    """
    if log_callback:
        log_callback("Starting example test...")
    
    # Test implementation
    # ...
    
    # Return results
    return {
        "test_details": {
            "status": TestStatus.PASS.value,
            "notes": "Test passed successfully",
            "additional_data": 42
        }
    }
```

### Test Result Format

All tests return results in a standardized format:

```json
{
  "test_details": {
    "status": "pass",
    "notes": "Test completed successfully",
    "additional_data": "Any test-specific data"
  },
  "metadata": {
    "category": "cpu",
    "severity": "high",
    "description": "Test description",
    "name": "run_example_test",
    "module": "example_test",
    "full_path": "agent.tests.example_test.run_example_test"
  },
  "started_at": "2023-01-01T12:00:00",
  "finished_at": "2023-01-01T12:00:10"
}
```

## Test Categories

- **CPU**: Tests for CPU functionality and performance
- **MEMORY**: Tests for RAM functionality and reliability
- **STORAGE**: Tests for storage devices
- **DISPLAY**: Tests for display functionality
- **INPUT**: Tests for input devices (keyboard, touchpad, etc.)
- **NETWORK**: Tests for network connectivity
- **BATTERY**: Tests for battery health and performance
- **SECURITY**: Tests for security features
- **WIPE**: Tests for secure data wiping

## Test Severities

- **CRITICAL**: Test failure means device is unusable
- **HIGH**: Test failure indicates major functionality issue
- **MEDIUM**: Test failure affects some functionality
- **LOW**: Test failure is minor or cosmetic

## Test Statuses

- **PASS**: Test completed successfully
- **FAIL**: Test failed
- **SKIPPED**: Test was skipped
- **ERROR**: Unexpected error during test execution

## Configuration

Tests can be configured using environment variables or the `test_config.py` file. Environment variables take precedence over default settings.

Environment variables should be prefixed with `ARCOA_` and uppercase, for example:

```bash
# Set CPU stress test duration to 20 seconds
export ARCOA_CPU_STRESS_DURATION=20

# Enable real drive wiping (DANGEROUS!)
export ARCOA_ENABLE_REAL_WIPE=1
```

## Running Unit Tests

To run the unit tests for the test framework:

```bash
# Run all unit tests
python -m unittest discover -s agent/tests/unit_tests

# Run a specific unit test
python -m unittest agent.tests.unit_tests.test_framework_test
```
