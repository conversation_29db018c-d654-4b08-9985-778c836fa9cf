import tkinter as tk # For main_app_ref type hint, though not strictly needed for runtime if only used as parent
from typing import Callable, Dict, Any, Optional

# Import test functions
from agent.tests.cpu_test import run_basic_cpu_test, run_cpu_stress_test
from agent.tests.ram_test import run_ram_test, run_advanced_ram_test
from agent.tests.display_test import run_lcd_test_gui
from agent.tests.keyboard_test import run_keyboard_test
from agent.tests.pointing_device_test import run_pointing_device_test
from agent.tests.drive_wipe_test import run_secure_wipe_test
from agent.tests.visual_cpu_test import run_visual_cpu_test
from agent.tests.visual_ram_test import run_visual_ram_test
from agent.tests.touch_screen_test import run_touch_screen_test
from agent.tests.battery_test import (
    run_battery_test,
    run_battery_charge_test,
    run_battery_discharge_test,
    run_battery_full_assessment
)

from agent.tests.profiles import Profile
from agent.core.result_manager import ResultManager # For type hint

class TestOrchestrator:
    def __init__(self, 
                 log_callback: Callable[[str, str], None], 
                 result_manager_instance: ResultManager,
                 main_app_ref: tk.Tk, # Typically NexusApp instance
                 get_current_profile_callback: Callable[[], Optional[Profile]]):
        self.log_callback = log_callback
        self.result_manager_instance = result_manager_instance
        self.main_app_ref = main_app_ref
        self.get_current_profile_callback = get_current_profile_callback
        self.end_screen_summary_data: Dict[str, Any] = {}

    def _get_status_and_notes_from_result(self, result_data: Any) -> tuple[str, str]:
        """Helper function to extract status and notes from various result formats."""
        status = "unknown"
        notes = ""
        if isinstance(result_data, dict):
            if "test_details" in result_data: # Standard test framework
                raw_status = result_data.get("test_details", {}).get("status", "unknown")
                status = str(raw_status).lower() if raw_status is not None else "unknown"
                notes = result_data.get("test_details", {}).get("notes", "")
            elif "status" in result_data: # Battery test or other simple dict formats
                raw_status = result_data.get("status", "unknown")
                status = str(raw_status).lower() if raw_status is not None else "unknown"
                raw_notes = result_data.get("notes", "")
                if isinstance(raw_notes, list):
                    notes = "; ".join(raw_notes)
                else:
                    notes = str(raw_notes) # Ensure notes is a string
            else: # Unknown dict structure
                notes = str(result_data) 
                if len(notes) > 200: notes = notes[:200] + "..." 
        elif isinstance(result_data, str): 
            status = result_data.lower()
        else: 
            status = "error"
            notes = f"Unexpected result format: {type(result_data)}"
        return status, notes

    def execute_tests(self):
        current_profile = self.get_current_profile_callback()

        if not current_profile or not current_profile.tests:
            self.log_callback("No profile loaded or profile has no tests.", "warning")
            # Initialize summary data to indicate no tests ran or error
            self.end_screen_summary_data = {
                "overall_status": "ERROR",
                "test_results": [],
                "drive_wipe_results": [],
                "errors": ["No profile loaded or profile has no tests."]
            }
            return

        self.log_callback(f"Starting tests for profile: {current_profile.name}...", "info")
        
        self.end_screen_summary_data = {
            "overall_status": "PASS",  # Assume PASS initially
            "test_results": [],
            "drive_wipe_results": [], # For secure wipe, which has a different result structure
            "errors": []
        }

        tests_to_run = current_profile.tests
        
        # Mapping test paths (as strings) to callable functions
        # This makes the execution loop more data-driven
        test_function_map = {
            "agent.tests.cpu_test.run_basic_cpu_test": (run_basic_cpu_test, {"log_callback": self.log_callback}),
            "agent.tests.ram_test.run_ram_test": (run_ram_test, {"log_callback": self.log_callback}),
            "agent.tests.ram_test.run_advanced_ram_test": (run_advanced_ram_test, {"log_callback": self.log_callback}),
            "agent.tests.display_test.run_lcd_test_gui": (run_lcd_test_gui, {"parent_window": self.main_app_ref, "log_callback": self.log_callback}),
            "agent.tests.keyboard_test.run_keyboard_test": (run_keyboard_test, {"parent_window": self.main_app_ref, "log_callback": self.log_callback}),
            "agent.tests.pointing_device_test.run_pointing_device_test": (run_pointing_device_test, {"parent_window": self.main_app_ref, "log_callback": self.log_callback}),
            "agent.tests.visual_cpu_test.run_visual_cpu_test": (run_visual_cpu_test, {"parent_window": self.main_app_ref, "log_callback": self.log_callback}),
            "agent.tests.visual_ram_test.run_visual_ram_test": (run_visual_ram_test, {"parent_window": self.main_app_ref, "log_callback": self.log_callback}),
            "agent.tests.drive_wipe_test.run_secure_wipe_test": (run_secure_wipe_test, {"parent_window": self.main_app_ref, "log_callback": self.log_callback}),
            "agent.tests.battery_test.run_battery_test": (run_battery_test, {"log_callback": self.log_callback}),
            "agent.tests.battery_test.run_battery_discharge_test": (run_battery_discharge_test, {"log_callback": self.log_callback}),
            "agent.tests.battery_test.run_battery_charge_test": (run_battery_charge_test, {"log_callback": self.log_callback}),
            "agent.tests.battery_test.run_battery_full_assessment": (run_battery_full_assessment, {"log_callback": self.log_callback}),
            "agent.tests.cpu_test.run_cpu_stress_test": (run_cpu_stress_test, {"log_callback": self.log_callback}),
            "agent.tests.touch_screen_test.run_touch_screen_test": (run_touch_screen_test, {"parent_window": self.main_app_ref, "log_callback": self.log_callback}),
        }

        for test_path_in_profile in tests_to_run:
            test_name_simple = test_path_in_profile.split('.')[-1].replace("run_", "").replace("_test", " Test").title()
            
            if test_path_in_profile not in test_function_map:
                error_msg = f"Test function for '{test_path_in_profile}' not found in orchestrator map."
                self.log_callback(error_msg, "error")
                self.result_manager_instance.add_result(test_name_simple, {"test_details": {"status": "fail", "notes": error_msg}})
                self.end_screen_summary_data["test_results"].append({"name": test_name_simple, "status": "FAIL"})
                self.end_screen_summary_data["errors"].append(error_msg)
                self.end_screen_summary_data["overall_status"] = "FAIL"
                continue

            test_func, test_kwargs = test_function_map[test_path_in_profile]
            self.log_callback(f"Running {test_name_simple}...", "info")
            
            try:
                result = test_func(**test_kwargs)
                self.result_manager_instance.add_result(test_name_simple, result)
                
                status, notes = self._get_status_and_notes_from_result(result)

                if test_path_in_profile == "agent.tests.drive_wipe_test.run_secure_wipe_test":
                    if isinstance(result, list): # Wipe test returns a list of dicts per drive
                        for drive_res in result:
                            drive_path = drive_res.get('drive', 'Unknown Drive')
                            drive_status = str(drive_res.get('status', 'unknown')).lower()
                            drive_details = drive_res.get('details', '')
                            self.end_screen_summary_data["drive_wipe_results"].append({
                                "drive": drive_path, "status": drive_status.upper(), "details": drive_details
                            })
                            if drive_status not in ["success", "pass"]: # Note: wipe uses 'success'
                                self.end_screen_summary_data["overall_status"] = "FAIL"
                                if drive_details: self.end_screen_summary_data["errors"].append(f"Wipe Error ({drive_path}): {drive_details}")
                    elif isinstance(result, dict) and 'status' in result: # Fallback for single dict result
                         wipe_status_overall = str(result.get('status', 'unknown')).lower()
                         wipe_details_overall = result.get('notes', result.get('details', ''))
                         self.end_screen_summary_data["drive_wipe_results"].append({
                             "drive": "Overall Wipe Status", "status": wipe_status_overall.upper(), "details": wipe_details_overall
                         })
                         if wipe_status_overall not in ["success", "pass"]:
                             self.end_screen_summary_data["overall_status"] = "FAIL"
                             if wipe_details_overall: self.end_screen_summary_data["errors"].append(f"{test_name_simple}: {wipe_details_overall}")
                    else: # Unexpected format for wipe result
                        self.log_callback(f"{test_name_simple} result in unexpected format: {result}", "warning")
                        self.end_screen_summary_data["drive_wipe_results"].append({"drive": "Unknown", "status": "FAIL", "details": "Result in unexpected format"})
                        self.end_screen_summary_data["overall_status"] = "FAIL"
                        self.end_screen_summary_data["errors"].append(f"{test_name_simple}: Result in unexpected format.")

                else: # For all other tests
                    self.end_screen_summary_data["test_results"].append({"name": test_name_simple, "status": status.upper()})
                    if status not in ["pass", "success"]: # 'success' for general compatibility
                        self.end_screen_summary_data["overall_status"] = "FAIL"
                        if notes: self.end_screen_summary_data["errors"].append(f"{test_name_simple} ({status.upper()}): {notes}")
            
            except Exception as e:
                error_msg = f"Error running {test_name_simple}: {str(e)}"
                self.log_callback(error_msg, "error")
                self.result_manager_instance.add_result(test_name_simple, {"test_details": {"status": "fail", "notes": error_msg}})
                
                # Add to appropriate summary list
                if test_path_in_profile == "agent.tests.drive_wipe_test.run_secure_wipe_test":
                     self.end_screen_summary_data["drive_wipe_results"].append({"drive": "Execution Error", "status": "FAIL", "details": error_msg})
                else:
                    self.end_screen_summary_data["test_results"].append({"name": test_name_simple, "status": "FAIL"})
                
                self.end_screen_summary_data["errors"].append(error_msg)
                self.end_screen_summary_data["overall_status"] = "FAIL"

        final_log_level = "success" if self.end_screen_summary_data["overall_status"] == "PASS" else "error"
        self.log_callback(f"All tests completed. Overall Status: {self.end_screen_summary_data['overall_status']}", final_log_level)
        if self.end_screen_summary_data["errors"]:
            self.log_callback(f"Encountered {len(self.end_screen_summary_data['errors'])} error(s)/issue(s) during testing:", "warning")
            for err_entry in self.end_screen_summary_data["errors"]:
                self.log_callback(f" - {err_entry}", "warning")

    def get_summary_data(self) -> Dict[str, Any]:
        return self.end_screen_summary_data

if __name__ == '__main__':
    # Example Usage (conceptual, needs mocks for ResultManager, main_app_ref, Profile)
    class MockResultManager:
        def add_result(self, test_name, result): print(f"MOCK_RESULT_ADD: {test_name} - {result.get('status', result.get('test_details', {}).get('status'))}")
    
    class MockApp(tk.Tk):
        def __init__(self): super().__init__(); self.title("Mock App")

    mock_app = MockApp()
    mock_logger = lambda msg, lvl: print(f"LOG [{lvl.upper()}]: {msg}")
    mock_res_manager = MockResultManager()
    
    # Sample Profile
    sample_profile = Profile(name="Full Test", device_type="laptop", description="Full suite")
    sample_profile.tests = [
        "agent.tests.cpu_test.run_basic_cpu_test",
        "agent.tests.ram_test.run_ram_test",
        "agent.tests.drive_wipe_test.run_secure_wipe_test",
        "non.existent.test.run_fake_test" # To test error handling
    ]
    
    get_profile_cb = lambda: sample_profile

    orchestrator = TestOrchestrator(
        log_callback=mock_logger,
        result_manager_instance=mock_res_manager, # type: ignore
        main_app_ref=mock_app,
        get_current_profile_callback=get_profile_cb
    )

    print("--- Executing Tests ---")
    orchestrator.execute_tests()
    summary = orchestrator.get_summary_data()
    print("\n--- Test Summary ---")
    import json
    print(json.dumps(summary, indent=2))

    # mock_app.mainloop() # If GUI tests were actually run and needed a window
    mock_app.destroy()
