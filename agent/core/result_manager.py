import os
import time
import json
import datetime
from typing import List, Dict, Any, Callable

class ResultManager:
    def __init__(self, 
                 log_callback: Callable[[str, str], None],
                 get_asset_number_callback: Callable[[], str],
                 get_operator_id_callback: Callable[[], str]):
        self.log_callback = log_callback
        self.get_asset_number_callback = get_asset_number_callback
        self.get_operator_id_callback = get_operator_id_callback
        self.test_results_available: bool = False

    def clear_results(self):
        """Clear previous test results status."""
        self.log_callback("ResultManager: Clearing previous test results status.", "info")
        self.test_results_available = False
        # In a more complex scenario, might clear actual files or a more detailed in-memory store.

    def save_result_to_file(self, test_name: str, result: Dict[str, Any]) -> bool:
        """Save test result to a JSON file."""
        try:
            timestamp = int(time.time())
            results_dir = "results"
            if not os.path.exists(results_dir):
                os.makedirs(results_dir)

            asset_number = self.get_asset_number_callback() or "unknown"
            operator_id = self.get_operator_id_callback() or "unknown"
            
            # Sanitize test_name for filename
            safe_test_name = test_name.replace(' ', '_').replace('/', '_').replace('\\', '_')
            filename = f"{results_dir}/nexus_result_{asset_number}_{safe_test_name}_{timestamp}.json"

            save_data = {
                "asset_number": asset_number,
                "operator_id": operator_id,
                "test_name": test_name,
                "timestamp": timestamp,
                "result": result 
            }

            with open(filename, "w") as f:
                json.dump(save_data, f, indent=2)

            self.log_callback(f"Test result saved to {filename}", "success")
            return True
        except Exception as e:
            self.log_callback(f"Error saving test result to file: {str(e)}", "error")
            return False

    def add_result(self, test_name: str, result: Dict[str, Any]):
        """Add a test result, log it, and save it to a file."""
        # Log the result summary (similar to what was in NexusApp.add_result)
        if "test_details" in result: # Standard test framework format
            status = result.get("test_details", {}).get("status", "unknown")
            notes = result.get("test_details", {}).get("notes", "")
            self.log_callback(f"Test result for {test_name}: {status}", "info")
            if notes:
                self.log_callback(f"Notes: {notes}", "info")
        elif "status" in result: # Battery test or other simple dict formats
            status = result.get("status", "unknown")
            grade = result.get("grade", "N/A")
            notes = result.get("notes", "")
            log_level = "info"
            if status == "pass": log_level = "success"
            elif status == "fail": log_level = "error"
            elif status == "warning": log_level = "warning"
            self.log_callback(f"Test result for {test_name}: {status.upper()} (Grade: {grade})", log_level)
            if notes:
                details_msg = f"Details for {test_name}: " + ("; ".join(notes) if isinstance(notes, list) else str(notes))
                self.log_callback(details_msg, "info")
            metrics = result.get("metrics", {})
            if metrics:
                self.log_callback(f"Metrics for {test_name}:", "info")
                for key, value in metrics.items():
                    if isinstance(value, (list, dict)): continue # Skip complex metrics for general log
                    self.log_callback(f"  - {key}: {value}", "info")
        else: # Unknown format
             self.log_callback(f"Test result for {test_name} (unknown format): {str(result)[:200]}", "info")


        if self.save_result_to_file(test_name, result):
            self.test_results_available = True # Set to true if at least one result is successfully saved
        else:
            self.log_callback(f"Failed to save result for {test_name}. Results availability not changed.", "warning")


    def _load_and_parse_results(self, file_paths: List[str]) -> List[Dict[str, Any]]:
        """Loads and parses result JSON files."""
        parsed_results = []
        for file_path in file_paths:
            try:
                with open(file_path, 'r') as f:
                    content = f.read()
                    data = json.loads(content)

                asset_num = data.get("asset_number", "Unknown")
                op_id = data.get("operator_id", "Unknown")
                test_name_saved = data.get("test_name", "Unknown Test")
                timestamp_saved = data.get("timestamp", 0)
                result_data = data.get('result', {})
                
                test_details = result_data.get('test_details') if isinstance(result_data, dict) else {}
                if test_details is None: test_details = {}

                status = test_details.get('status', 'unknown')
                notes = test_details.get('notes', '')
                other_details = {k: v for k, v in test_details.items() if k not in ['status', 'notes']}
                started_at = result_data.get('started_at') if isinstance(result_data, dict) else None
                finished_at = result_data.get('finished_at') if isinstance(result_data, dict) else None

                parsed_results.append({
                    "file_path": file_path, "asset_number": asset_num, "operator_id": op_id,
                    "test_name": test_name_saved, "timestamp": timestamp_saved, "status": status,
                    "notes": notes, "details": other_details, "started_at": started_at, "finished_at": finished_at
                })
                self.log_callback(f"Successfully parsed result file: {file_path}", "debug") # Changed to debug for less noise
            except FileNotFoundError:
                self.log_callback(f"Result file not found: {file_path}", "error")
            except IOError as e:
                self.log_callback(f"IOError reading result file {file_path}: {e}", "error")
            except json.JSONDecodeError as e:
                self.log_callback(f"Error decoding JSON from result file {file_path}: {e}", "error")
            except Exception as e:
                self.log_callback(f"Unexpected error parsing result file {file_path}: {e}", "error")
        
        return parsed_results

if __name__ == '__main__':
    # Example Usage (for testing ResultManager independently)
    
    def mock_log(message, level="info"):
        print(f"LOG [{level.upper()}]: {message}")

    def mock_get_asset():
        return "TEST_ASSET_001"

    def mock_get_operator():
        return "OPERATOR_007"

    # Create results directory if it doesn't exist for the test
    if not os.path.exists("results"):
        os.makedirs("results")

    result_manager = ResultManager(
        log_callback=mock_log,
        get_asset_number_callback=mock_get_asset,
        get_operator_id_callback=mock_get_operator
    )

    print(f"Initial test_results_available: {result_manager.test_results_available}")

    # Simulate adding a result
    sample_test_result_framework = {
        "test_details": {
            "status": "pass",
            "notes": "CPU test completed successfully.",
            "score": 12500,
            "cores": 8
        },
        "started_at": datetime.datetime.now().isoformat(),
        "finished_at": (datetime.datetime.now() + datetime.timedelta(seconds=30)).isoformat()
    }
    result_manager.add_result("CPU Performance Test", sample_test_result_framework)
    print(f"After add_result, test_results_available: {result_manager.test_results_available}")

    sample_battery_result = {
        "status": "warning",
        "grade": "B",
        "notes": ["Cycle count high", "Reduced capacity"],
        "metrics": {"cycle_count": 750, "current_capacity_mah": 3000, "design_capacity_mah": 5000}
    }
    result_manager.add_result("Battery Health Check", sample_battery_result)

    # Find saved files (simplified for example)
    asset_files = []
    if os.path.exists("results"):
        for f_name in os.listdir("results"):
            if mock_get_asset() in f_name and f_name.endswith(".json"):
                asset_files.append(os.path.join("results", f_name))
    
    if asset_files:
        print(f"\nFound result files for {mock_get_asset()}: {asset_files}")
        parsed = result_manager._load_and_parse_results(asset_files)
        print(f"\nParsed {len(parsed)} results:")
        for p_res in parsed:
            print(f"  - Test: {p_res['test_name']}, Status: {p_res['status']}, Notes: {p_res['notes'][:30]}...")
    else:
        print(f"\nNo result files found for {mock_get_asset()} to test parsing.")

    result_manager.clear_results()
    print(f"After clear_results, test_results_available: {result_manager.test_results_available}")
