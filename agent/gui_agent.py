#!/usr/bin/env python3
"""Arcoa Nexus GUI Agent - Client side component with graphical interface.

This GUI version of the agent provides a user-friendly interface for technicians
to enter asset information and run diagnostic tests.
"""
import asyncio
import datetime
import json
import platform
import subprocess
import threading
import time
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
from typing import Dict, Any, Callable, List

import httpx
import psutil
import cpuinfo # For detailed CPU information

# Styled colors
COLORS = {
    "primary": "#0066cc",
    "success": "#4CAF50",
    "warning": "#FF9800",
    "error": "#F44336",
    "bg_light": "#f5f5f5",
    "bg_dark": "#333333",
    "text_light": "#ffffff",
    "text_dark": "#333333",
}


def get_serial_number() -> str:
    """Get system serial number using various platform-specific methods."""
    try:
        if platform.system() == "Windows":
            # Windows-specific method using wmic
            result = subprocess.run(
                ["wmic", "bios", "get", "serialnumber"], 
                capture_output=True, 
                text=True,
                check=True
            )
            lines = result.stdout.strip().split('\n')
            if len(lines) >= 2:
                return lines[1].strip()
        else:
            # Linux method using dmidecode (requires root)
            result = subprocess.run(
                ["dmidecode", "-s", "system-serial-number"],
                capture_output=True,
                text=True,
                check=True
            )
            return result.stdout.strip()
    except (subprocess.SubprocessError, FileNotFoundError):
        pass
        
    # Fallback if commands fail
    return f"UNKNOWN-{platform.node()}"


def get_screen_resolution(root_tk_instance=None) -> str:
    """Get screen resolution."""
    try:
        # If a Tk instance is available, use it for better accuracy
        if root_tk_instance:
            width = root_tk_instance.winfo_screenwidth()
            height = root_tk_instance.winfo_screenheight()
            return f"{width}x{height}"
        
        # Fallback for environments where Tk might not be fully initialized or available
        # This might require platform-specific libraries for headless environments
        # For now, we'll rely on Tkinter being available for GUI mode
        temp_root = tk.Tk()
        temp_root.withdraw() # Hide the window
        width = temp_root.winfo_screenwidth()
        height = temp_root.winfo_screenheight()
        temp_root.destroy()
        return f"{width}x{height}"
    except Exception:
        return "Unknown"


def get_cpu_info_detailed() -> str:
    parts = []
    try:
        # Using py-cpuinfo for a more brand-friendly name
        cpu_brand = cpuinfo.get_cpu_info().get('brand_raw', '')
        if cpu_brand:
            parts.append(cpu_brand)
        else: # Fallback to platform.processor if py-cpuinfo fails or brand_raw is not found
            processor = platform.processor()
            if processor:
                parts.append(processor)
    except Exception as e:
        # Fallback if py-cpuinfo is not installed or fails
        # self.log(f"Could not get CPU brand using py-cpuinfo: {e}", "warning") # Assuming log is not available here
        try:
            processor = platform.processor()
            if processor:
                parts.append(processor)
        except Exception:
            pass # Can't get processor name

    try:
        cpu_cores = psutil.cpu_count(logical=False)
        cpu_logical = psutil.cpu_count(logical=True)
        if cpu_cores is not None and cpu_logical is not None:
            parts.append(f"{cpu_cores} cores / {cpu_logical} threads")
        elif cpu_logical is not None:
            parts.append(f"{cpu_logical} logical processors")
    except Exception:
        pass # Failed to get core/thread count

    try:
        freq = psutil.cpu_freq()
        if freq and hasattr(freq, 'max') and freq.max > 0: # freq.max might be 0 or not exist on some platforms
            parts.append(f"Max Speed: {freq.max / 1000:.2f} GHz")
        elif freq and hasattr(freq, 'current') and freq.current > 0: # Fallback to current if max is not available
            parts.append(f"Current Speed: {freq.current / 1000:.2f} GHz")
    except Exception: # psutil.cpu_freq() can fail on some systems or VMs
        pass

    return ", ".join(filter(None, parts)) if parts else "N/A"


def get_gpu_info() -> List[str]:
    """Attempts to detect GPU(s) using OS-specific commands."""
    gpus = []
    try:
        if platform.system() == "Windows":
            # Try PowerShell Get-CimInstance first (works on all modern Windows)
            ps_command = [
                "powershell",
                "-Command",
                "Get-CimInstance Win32_VideoController | Select-Object -ExpandProperty Name"
            ]
            try:
                process = subprocess.run(ps_command, capture_output=True, text=True, check=True)
                output = process.stdout.strip()
                if output:
                    gpus.extend([line.strip() for line in output.splitlines() if line.strip()])
            except Exception as e:
                # If PowerShell fails, fallback to WMIC if available
                wmic_path = r"C:\Windows\System32\wbem\WMIC.exe"
                command_str = f'{wmic_path} path Win32_VideoController get Caption'
                process = subprocess.run(command_str, capture_output=True, text=True, shell=True, check=False)
                if process.returncode == 0:
                    output = process.stdout.strip()
                    if output and "Caption" in output.splitlines()[0]:
                        detected_gpus = [line.strip() for line in output.splitlines()[1:] if line.strip()]
                        if detected_gpus:
                            gpus.extend(detected_gpus)
                        else:
                            gpus.append("No GPUs listed by 'wmic path Win32_VideoController get Caption'.")
                    elif output:
                        gpus.append(f"WMIC command for GPU ran, but output format unexpected: {output.splitlines()[0] if output else '[empty output]'}")
                    else:
                        gpus.append("WMIC command for GPU ran successfully but produced no output.")
                else:
                    error_message = f"Error detecting GPUs (WMIC): Command '{command_str}' failed with exit code {process.returncode}."
                    if process.stderr:
                        error_message += f" Stderr: {process.stderr.strip()}"
                    else:
                        error_message += " No stderr output."
                    if process.stdout:
                        error_message += f" Stdout: {process.stdout.strip()}"
                    gpus.append(error_message)
            if not gpus:
                gpus.append("No GPUs detected or PowerShell/WMIC unavailable.")

        elif platform.system() == "Linux":
            try:
                process = subprocess.run(["lspci"], capture_output=True, text=True, check=True)
                for line in process.stdout.splitlines():
                    if "VGA compatible controller" in line or "3D controller" in line:
                        gpus.append(line.split(":", 2)[-1].strip())
            except (subprocess.CalledProcessError, FileNotFoundError):
                try:
                    process = subprocess.run(["lshw", "-C", "display"], capture_output=True, text=True, check=True)
                    for line in process.stdout.splitlines():
                        if "product:" in line.lower():
                            gpus.append(line.split(":", 1)[-1].strip())
                except (subprocess.CalledProcessError, FileNotFoundError):
                    gpus.append("Error detecting GPUs: lspci and lshw commands failed or not found on Linux.")
        
        if not gpus:
            gpus.append("No GPUs detected or OS not supported for GPU detection.")
            
    except Exception as e:
        gpus.append(f"An unexpected error occurred during GPU detection: {str(e)}")
    return gpus


def get_battery_info() -> dict:
    """Get battery status and condition information."""
    import platform
    import psutil
    import os
    import subprocess
    battery_info = {
        "present": False,
        "percent": None,
        "charging": None,
        "secsleft": None,
        "cycle_count": None,
        "health": None,
        "status": "Not detected"
    }
    try:
        batt = psutil.sensors_battery()
        if batt is not None:
            battery_info["present"] = True
            battery_info["percent"] = batt.percent
            battery_info["charging"] = batt.power_plugged
            battery_info["secsleft"] = batt.secsleft
            battery_info["status"] = (
                "Charging" if batt.power_plugged else "Discharging"
            )
        # Try to get more info (cycle count, health) platform-specific
        if platform.system() == "Windows":
            try:
                # Cycle count and health via PowerShell
                ps = subprocess.run([
                    "powershell", "-Command",
                    "Get-WmiObject -Class BatteryStatus -Namespace root\\wmi | Select-Object -Property CycleCount,DesignCapacity,FullChargedCapacity"
                ], capture_output=True, text=True, check=False)
                for line in ps.stdout.splitlines():
                    if "CycleCount" in line:
                        try:
                            battery_info["cycle_count"] = int(line.split(":")[-1].strip())
                        except Exception:
                            pass
                    if "DesignCapacity" in line:
                        try:
                            battery_info["design_capacity"] = int(line.split(":")[-1].strip())
                        except Exception:
                            pass
                    if "FullChargedCapacity" in line:
                        try:
                            battery_info["full_charged_capacity"] = int(line.split(":")[-1].strip())
                        except Exception:
                            pass
                if (
                    battery_info.get("design_capacity")
                    and battery_info.get("full_charged_capacity")
                ):
                    health = (
                        battery_info["full_charged_capacity"] / battery_info["design_capacity"] * 100
                    )
                    battery_info["health"] = round(health, 1)
            except Exception:
                pass
        elif platform.system() == "Linux":
            # Try sysfs for extra details
            try:
                for bat in os.listdir("/sys/class/power_supply"):
                    if bat.startswith("BAT"):
                        base = f"/sys/class/power_supply/{bat}/"
                        def read_file(fname):
                            try:
                                with open(base + fname) as f:
                                    return f.read().strip()
                            except Exception:
                                return None
                        battery_info["cycle_count"] = read_file("cycle_count")
                        battery_info["health"] = read_file("health") or read_file("capacity")
                        break
            except Exception:
                pass
    except Exception:
        pass
    return battery_info

def get_system_info(root_tk_instance=None) -> Dict[str, Any]:
    """Gathers detailed system information, including make, model, serial, computrace, battery."""
    info = {
        "hostname": platform.node(),
        "os_version": f"{platform.system()} {platform.release()} ({platform.version()})",
        "architecture": platform.machine(),
        "cpu": get_cpu_info_detailed(),
        "memory": "N/A",
        "disks": [],
        "gpus": get_gpu_info(),
        "screen_resolution": get_screen_resolution(root_tk_instance),
        "network_interfaces": [],
        "make": "Unknown",
        "model": "Unknown",
        "serial_number": "Unknown",
        "computrace": "Unknown"
    }

    # --- Make, Model, Serial, Computrace ---
    try:
        if platform.system() == "Linux":
            import subprocess
            def run_dmidecode(field):
                try:
                    result = subprocess.run([
                        "dmidecode", "-s", field
                    ], capture_output=True, text=True, check=True)
                    return result.stdout.strip() or "Unknown"
                except Exception:
                    return "Unknown"
            info["make"] = run_dmidecode("system-manufacturer")
            info["model"] = run_dmidecode("system-product-name")
            info["serial_number"] = run_dmidecode("system-serial-number")
            # Computrace status
            try:
                comp = subprocess.run(["dmidecode", "-t", "8"], capture_output=True, text=True, check=True)
                if "computrace" in comp.stdout.lower() or "absolute" in comp.stdout.lower():
                    lines = [l for l in comp.stdout.splitlines() if "computrace" in l.lower() or "absolute" in l.lower()]
                    info["computrace"] = ", ".join(lines) if lines else "Present (details in BIOS)"
                else:
                    info["computrace"] = "Not detected"
            except Exception:
                info["computrace"] = "Unknown (dmidecode not available or not root)"
        elif platform.system() == "Windows":
            import subprocess
            # Make, Model, Serial
            try:
                make = subprocess.run([
                    "powershell", "-Command",
                    "(Get-WmiObject Win32_ComputerSystem).Manufacturer"
                ], capture_output=True, text=True, check=True)
                info["make"] = make.stdout.strip() or "Unknown"
            except Exception:
                pass
            try:
                model = subprocess.run([
                    "powershell", "-Command",
                    "(Get-WmiObject Win32_ComputerSystem).Model"
                ], capture_output=True, text=True, check=True)
                info["model"] = model.stdout.strip() or "Unknown"
            except Exception:
                pass
            try:
                serial = subprocess.run([
                    "powershell", "-Command",
                    "(Get-WmiObject Win32_BIOS).SerialNumber"
                ], capture_output=True, text=True, check=True)
                info["serial_number"] = serial.stdout.strip() or "Unknown"
            except Exception:
                pass
            # Computrace
            try:
                bios = subprocess.run([
                    "powershell", "-Command",
                    "Get-WmiObject -Class Win32_BIOS | Format-List *"
                ], capture_output=True, text=True, check=True)
                bios_out = bios.stdout.lower()
                if "computrace" in bios_out or "absolute" in bios_out:
                    lines = [l for l in bios.stdout.splitlines() if "computrace" in l.lower() or "absolute" in l.lower()]
                    info["computrace"] = ", ".join(lines) if lines else "Present (see BIOS details)"
                else:
                    info["computrace"] = "Not detected"
            except Exception:
                info["computrace"] = "Unknown (no access to BIOS info)"
    except Exception:
        pass

    # --- RAM ---
    try:
        mem = psutil.virtual_memory()
        info["memory"] = f"{mem.total / (1024**3):.2f} GB (Available: {mem.available / (1024**3):.2f} GB)"
    except Exception as e:
        info["memory"] = f"Error detecting memory: {e}"

    # --- Disks ---
    disk_details = []
    try:
        partitions = psutil.disk_partitions()
        if not partitions:
            disk_details.append("No disk partitions found.")
        else:
            for partition in partitions:
                try:
                    usage = psutil.disk_usage(partition.mountpoint)
                    disk_details.append(
                        f"{partition.device} ({partition.fstype}) at {partition.mountpoint}: "
                        f"{usage.total / (1024**3):.2f} GB total, "
                        f"{usage.free / (1024**3):.2f} GB free"
                    )
                except Exception as e:
                    disk_details.append(f"Could not get usage for {partition.device}: {e}")
        info["disks"] = disk_details if disk_details else ["N/A"]
    except Exception as e:
        info["disks"] = [f"Error detecting disks: {e}"]

    # --- Network Interfaces ---
    net_details = []
    try:
        net_if_addrs = psutil.net_if_addrs()
        for interface_name, interface_addresses in net_if_addrs.items():
            for addr in interface_addresses:
                if str(addr.family) == 'AddressFamily.AF_INET':
                    net_details.append(f"{interface_name}: IPv4={addr.address} (Mask={addr.netmask})")
        info["network_interfaces"] = net_details if net_details else ["No network interfaces with IPv4 found."]
    except Exception as e:
        info["network_interfaces"] = [f"Error detecting network interfaces: {e}"]

    return info


def run_basic_cpu_test() -> Dict[str, Any]:
    """Run a simplified CPU test."""
    # Start time for the test
    start_time = time.time()
    start_dt = datetime.datetime.now()
    
    # Just measure current CPU usage without stress
    cpu_percent = psutil.cpu_percent(interval=1.0)
    
    # Ending time
    end_dt = datetime.datetime.now()
    
    # Mocked result - in a real app this would be an actual test
    result = {
        "status": "pass",
        "fail_reason": None,
        "duration_seconds": 1,
        "avg_cpu_load": cpu_percent,
        "max_cpu_load": cpu_percent,
    }
    
    return {
        "test_details": result,
        "started_at": start_dt.isoformat(),
        "finished_at": end_dt.isoformat(),
    }


async def send_result(server_url: str, result: Dict[str, Any]) -> bool:
    """Send test result to server."""
    url = f"{server_url.rstrip('/')}/result"
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                url,
                json=result,
                timeout=10.0,
            )
        
        if response.status_code == 200:
            return True, "Result sent successfully!"
        else:
            return False, f"Failed to send result: {response.status_code} {response.text}"
    except Exception as e:
        return False, f"Error sending result: {e}"


class NexusAgentGUI(tk.Tk):
    """Main GUI application class."""
    
    def __init__(self):
        super().__init__()
        
        self.title("Arcoa Nexus Agent")
        self.geometry("800x700")
        self.configure(bg=COLORS["bg_light"])
        
        self.serial_number = get_serial_number()
        # Pass self (tk.Tk instance) to get_system_info for screen resolution
        self.system_info = get_system_info(root_tk_instance=self) 
        
        self.create_widgets()
        
    def create_widgets(self):
        """Build the GUI layout."""
        # Header
        header_frame = tk.Frame(self, bg=COLORS["primary"], padx=10, pady=10)
        header_frame.pack(fill=tk.X)
        
        title_label = tk.Label(
            header_frame, 
            text="ARCOA NEXUS", 
            font=("Arial", 24, "bold"),
            fg=COLORS["text_light"],
            bg=COLORS["primary"]
        )
        title_label.pack()
        
        subtitle_label = tk.Label(
            header_frame,
            text="Hardware Diagnostics & Secure Wipe",
            font=("Arial", 12),
            fg=COLORS["text_light"],
            bg=COLORS["primary"]
        )
        subtitle_label.pack()
        
        # Main content area
        content_frame = ttk.Frame(self, padding=20)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # Connection settings
        server_frame = ttk.LabelFrame(content_frame, text="Server Connection", padding=10)
        server_frame.pack(fill=tk.X, pady=10)
        
        ttk.Label(server_frame, text="Server URL:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.server_url = tk.StringVar(value="http://localhost:8000")
        server_entry = ttk.Entry(server_frame, textvariable=self.server_url, width=40)
        server_entry.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Operator and asset info
        info_frame = ttk.LabelFrame(content_frame, text="Test Information", padding=10)
        info_frame.pack(fill=tk.X, pady=10)
        
        ttk.Label(info_frame, text="Operator ID:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.operator_id = tk.StringVar()
        ttk.Entry(info_frame, textvariable=self.operator_id, width=20).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(info_frame, text="Asset Number:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.asset_number = tk.StringVar()
        ttk.Entry(info_frame, textvariable=self.asset_number, width=20).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # System info
        system_frame = ttk.LabelFrame(content_frame, text="System Information", padding=10)
        system_frame.pack(fill=tk.X, pady=10)
        
        # Create a grid of system info
        ttk.Label(system_frame, text="Serial Number:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Label(system_frame, text=self.serial_number).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(system_frame, text="CPU:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        cpu_info = self.system_info['cpu']
        ttk.Label(system_frame, text=cpu_info).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(system_frame, text="Memory:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Label(system_frame, text=self.system_info['memory']).grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(system_frame, text="Disks:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Label(system_frame, text=f"{len(self.system_info['disks'])} device(s) detected").grid(row=3, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(system_frame, text="Graphics:").grid(row=4, column=0, sticky=tk.W, padx=5, pady=5)
        gpus = self.system_info['gpus']
        if isinstance(gpus, list):
            gpu_text = ", ".join(gpus)
        else:
            gpu_text = str(gpus)
        ttk.Label(system_frame, text=gpu_text).grid(row=4, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(system_frame, text="Resolution:").grid(row=5, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Label(system_frame, text=self.system_info['screen_resolution']).grid(row=5, column=1, sticky=tk.W, padx=5, pady=5)

        # Battery Condition
        ttk.Label(system_frame, text="Battery:").grid(row=6, column=0, sticky=tk.W, padx=5, pady=5)
        batt = self.system_info.get('battery', {})
        if batt.get('present'):
            batt_lines = [
                f"{batt.get('percent', 'N/A')}% ({batt.get('status', 'N/A')})",
            ]
            if batt.get('health'):
                batt_lines.append(f"Health: {batt['health']}%")
            if batt.get('cycle_count'):
                batt_lines.append(f"Cycles: {batt['cycle_count']}")
            ttk.Label(system_frame, text="; ".join(batt_lines)).grid(row=6, column=1, sticky=tk.W, padx=5, pady=5)
        else:
            ttk.Label(system_frame, text="No battery detected").grid(row=6, column=1, sticky=tk.W, padx=5, pady=5)

        
        # Test controls
        test_frame = ttk.LabelFrame(content_frame, text="Tests", padding=10)
        test_frame.pack(fill=tk.X, pady=10)
        
        # Add test options as checkboxes
        self.test_cpu = tk.BooleanVar(value=True)
        ttk.Checkbutton(test_frame, text="CPU Test", variable=self.test_cpu).grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        
        self.test_memory = tk.BooleanVar(value=False)
        ttk.Checkbutton(test_frame, text="RAM Test", variable=self.test_memory).grid(row=0, column=1, padx=5, pady=5, sticky=tk.W)
        
        self.test_disk = tk.BooleanVar(value=False)
        ttk.Checkbutton(test_frame, text="Disk Test (Not Implemented)", variable=self.test_disk, state="disabled").grid(row=1, column=0, padx=5, pady=5, sticky=tk.W)
        
        self.test_lcd = tk.BooleanVar(value=False)
        ttk.Checkbutton(test_frame, text="LCD Test", variable=self.test_lcd).grid(row=1, column=1, padx=5, pady=5, sticky=tk.W)
        
        # Run button
        run_button = ttk.Button(content_frame, text="Run Tests", command=self.run_tests)
        run_button.pack(pady=10)
        
        # Log output
        log_frame = ttk.LabelFrame(content_frame, text="Log", padding=10)
        log_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=10, wrap=tk.WORD)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # Status bar
        self.status_var = tk.StringVar(value="Ready")
        status_bar = ttk.Label(self, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W, padding=5)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def log(self, message: str, level: str = "info"):
        """Add a message to the log widget with timestamp and color."""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        
        # Apply tag based on level
        tags = {}
        if level == "error":
            tag = "error"
            tags[tag] = {"foreground": COLORS["error"]}
        elif level == "success":
            tag = "success"
            tags[tag] = {"foreground": COLORS["success"]}
        elif level == "warning":
            tag = "warning"
            tags[tag] = {"foreground": COLORS["warning"]}
        else:
            tag = "info"
            tags[tag] = {"foreground": COLORS["text_dark"]}
        
        for tag_name, config in tags.items():
            self.log_text.tag_configure(tag_name, **config)
        
        self.log_text.insert(tk.END, f"[{timestamp}] ", "timestamp")
        self.log_text.insert(tk.END, f"{message}\n", tag)
        self.log_text.see(tk.END)  # Scroll to the end
    
    def run_ram_test(self) -> Dict[str, Any]:
        """Quick RAM test: allocate, write, and verify a chunk of memory."""
        self.log("Allocating memory for RAM test...")
        start_dt = datetime.datetime.now()
        test_size_mb = 1024  # 1GB
        try:
            avail_gb = psutil.virtual_memory().available / (1024 ** 3)
            if avail_gb > 2:
                test_size_mb = min(2048, int(avail_gb * 0.25 * 1024))  # Up to 2GB or 25% of available RAM
        except Exception:
            pass
        test_size_bytes = test_size_mb * 1024 * 1024
        try:
            # Allocate memory
            buf = bytearray(test_size_bytes)
            # Write pattern
            for i in range(0, test_size_bytes, 4096):
                buf[i:i+4096] = b'\xAA' * min(4096, test_size_bytes - i)
            # Verify pattern
            errors = 0
            for i in range(0, test_size_bytes, 4096):
                if buf[i:i+4096] != b'\xAA' * min(4096, test_size_bytes - i):
                    errors += 1
            status = "pass" if errors == 0 else "fail"
            notes = f"Tested {test_size_mb} MB. Errors: {errors}."
        except MemoryError:
            status = "fail"
            notes = "Could not allocate test memory."
        except Exception as e:
            status = "fail"
            notes = f"Unexpected error: {e}"
        end_dt = datetime.datetime.now()
        return {
            "test_details": {"status": status, "notes": notes},
            "started_at": start_dt.isoformat(),
            "finished_at": end_dt.isoformat(),
        }

    def run_lcd_test_gui(self) -> Dict[str, Any]:
        """Runs the LCD test by cycling through colors in a fullscreen window."""
        self.log("Starting LCD Test...")
        self.update_status("Running LCD Test...")

        test_colors = [("Black", "#000000"), ("White", "#FFFFFF"), 
                       ("Red", "#FF0000"), ("Green", "#00FF00"), ("Blue", "#0000FF")]
        results = {"status": "pass", "failed_colors": [], "notes": ""}
        current_color_index = 0
        
        start_dt = datetime.datetime.now()

        lcd_window = tk.Toplevel(self)
        lcd_window.title("LCD Test")
        lcd_window.attributes('-fullscreen', True)
        lcd_window.configure(bg=test_colors[current_color_index][1])
        
        # Make it on top
        lcd_window.attributes('-topmost', True)
        lcd_window.focus_force() # Grab focus

        info_label = tk.Label(lcd_window, 
                              text=f"Displaying: {test_colors[current_color_index][0]}\nPress ENTER if OK, ESC to mark as FAILED", 
                              font=("Arial", 20), fg="grey", bg=test_colors[current_color_index][1])
        info_label.place(relx=0.5, rely=0.5, anchor=tk.CENTER)

        def next_color(event=None, failed=False):
            nonlocal current_color_index # Allow modification of outer scope variable
            
            if failed:
                results["status"] = "fail"
                results["failed_colors"].append(test_colors[current_color_index][0])
                self.log(f"LCD Color {test_colors[current_color_index][0]} reported as FAILED.", "error")
            else:
                self.log(f"LCD Color {test_colors[current_color_index][0]} reported as OK.", "success")
            
            current_color_index += 1
            if current_color_index < len(test_colors):
                color_name, color_hex = test_colors[current_color_index]
                lcd_window.configure(bg=color_hex)
                info_label.configure(text=f"Displaying: {color_name}\nPress ENTER if OK, ESC to mark as FAILED", bg=color_hex)
                # Adjust text color for visibility against background
                # Simple heuristic: if background is dark, use light text, and vice-versa.
                # A more robust solution would calculate luminance.
                if color_hex in ["#000000"]:
                    info_label.configure(fg="#FFFFFF")
                else:
                    info_label.configure(fg="#333333") # Dark grey for light backgrounds
            else:
                lcd_window.destroy()
                self.log("LCD Test Finished.", "success" if results["status"] == "pass" else "error")
                if results["status"] == "fail":
                    results["notes"] = f"Failed on colors: {', '.join(results['failed_colors'])}"
                
        lcd_window.bind('<Return>', lambda e: next_color(failed=False))
        lcd_window.bind('<Escape>', lambda e: next_color(failed=True))
        
        # This makes the run_lcd_test_gui function wait until the window is closed.
        # The main GUI will be blocked, which is intended for this modal test.
        self.wait_window(lcd_window) 

        end_dt = datetime.datetime.now()
        
        return {
            "test_details": results,
            "started_at": start_dt.isoformat(),
            "finished_at": end_dt.isoformat(),
        }

    def validate_inputs(self) -> bool:
        """Validate user inputs before running tests."""
        if not self.operator_id.get().strip():
            messagebox.showerror("Error", "Please enter your operator ID/initials")
            return False
        
        if not self.asset_number.get().strip():
            messagebox.showerror("Error", "Please enter an asset number")
            return False
        
        if not self.server_url.get().strip():
            messagebox.showerror("Error", "Please enter the server URL")
            return False
        
        return True
    
    def update_status(self, message: str):
        """Update the status bar text."""
        self.status_var.set(message)
        self.update_idletasks()
    
    def run_tests(self):
        """Run the selected tests in a background thread."""
        if not self.validate_inputs():
            return
        
        # Clear log
        self.log_text.delete(1.0, tk.END)
        
        # Update status
        self.update_status("Running tests...")
        
        # Start test thread
        test_thread = threading.Thread(target=self._run_tests_thread)
        test_thread.daemon = True
        test_thread.start()
    
    def _run_tests_thread(self):
        """Background thread for running tests."""
        try:
            # Log test start
            self.log(f"Starting tests for asset {self.asset_number.get()}")
            self.log(f"Operator: {self.operator_id.get()}")
            
            # Run enabled tests
            test_results = []
            
            if self.test_cpu.get():
                self.log("Running CPU test...")
                cpu_result = run_basic_cpu_test()
                status = cpu_result["test_details"]["status"]
                level = "success" if status == "pass" else "error"
                self.log(f"CPU test completed: {status.upper()}", level)
                
                # Prepare result for server
                result_cpu = {
                    "asset_serial": self.serial_number,
                    "asset_number": self.asset_number.get(),
                    "operator_id": self.operator_id.get(),
                    "test_name": "cpu_test",
                    "status": status,
                    "data": {
                        "system_info": self.system_info,
                        **cpu_result["test_details"]
                    },
                    "started_at": cpu_result["started_at"],
                    "finished_at": cpu_result["finished_at"],
                }
                test_results.append(result_cpu)

            if self.test_memory.get():
                self.log("Running RAM test...")
                ram_result = self.run_ram_test()
                status = ram_result["test_details"]["status"]
                level = "success" if status == "pass" else "error"
                self.log(f"RAM test completed: {status.upper()}", level)

                result_ram = {
                    "asset_serial": self.serial_number,
                    "asset_number": self.asset_number.get(),
                    "operator_id": self.operator_id.get(),
                    "test_name": "ram_test",
                    "status": status,
                    "data": {
                        "system_info": {"memory": self.system_info.get('memory', 'N/A')},
                        **ram_result["test_details"]
                    },
                    "started_at": ram_result["started_at"],
                    "finished_at": ram_result["finished_at"],
                }
                test_results.append(result_ram)
            
            if self.test_lcd.get():
                lcd_result_data = self.run_lcd_test_gui()
                status_lcd = lcd_result_data["test_details"]["status"]
                
                result_lcd = {
                    "asset_serial": self.serial_number,
                    "asset_number": self.asset_number.get(),
                    "operator_id": self.operator_id.get(),
                    "test_name": "lcd_test",
                    "status": status_lcd,
                    "data": {
                        "system_info": {"screen_resolution": self.system_info.get('screen_resolution', 'N/A')},
                        **lcd_result_data["test_details"]
                    },
                    "started_at": lcd_result_data["started_at"],
                    "finished_at": lcd_result_data["finished_at"],
                }
                test_results.append(result_lcd)

            # Send all collected results
            if not test_results:
                self.log("No tests selected or run.", "warning")
            else:
                self.log(f"Sending {len(test_results)} result(s) to {self.server_url.get()}...")
                all_sent_successfully = True
                for res_idx, test_payload in enumerate(test_results):
                    self.log(f"Sending result for {test_payload['test_name']}...")
                    loop = asyncio.new_event_loop()
                    success, message = loop.run_until_complete(
                        send_result(self.server_url.get(), test_payload)
                    )
                    loop.close()
                    
                    if success:
                        self.log(f"{test_payload['test_name']}: {message}", "success")
                    else:
                        all_sent_successfully = False
                        self.log(f"{test_payload['test_name']}: {message}", "error")
                        # Save locally as fallback
                        backup_file = f"nexus_result_{self.asset_number.get()}_{test_payload['test_name']}_{int(time.time())}.json"
                        with open(backup_file, "w") as f:
                            json.dump(test_payload, f, indent=2)
                        self.log(f"Result for {test_payload['test_name']} saved to {backup_file}", "warning")
                
                if all_sent_successfully:
                    self.log("All test results sent successfully.", "success")
                else:
                    self.log("Some test results failed to send. Check logs and local backups.", "error")
            
            self.update_status("Tests completed")
            self.log("All tests completed", "success")
            
        except Exception as e:
            self.log(f"Error running tests: {e}", "error")
            self.update_status("Error")


if __name__ == "__main__":
    app = NexusAgentGUI()
    app.mainloop()
