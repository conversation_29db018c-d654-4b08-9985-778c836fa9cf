import tkinter as tk
from tkinter import ttk, messagebox

from agent.gui.theme import FONTS, COLORS # COLORS might be needed for LabelFrame styling if not using default
from agent.gui.device_condition import DeviceConditionWindow, save_device_conditions, load_device_conditions

class AssetInputFrame(ttk.Frame):
    def __init__(self, parent, log_callback, asset_change_callback, main_app_ref, base_font_size, **kwargs):
        super().__init__(parent, **kwargs)
        self.log_callback = log_callback
        self.asset_change_callback = asset_change_callback # This is NexusApp._check_run_conditions_callback or similar
        self.main_app_ref = main_app_ref
        self.base_font_size = base_font_size

        # Variables for form fields
        self.operator_id = tk.StringVar()
        self.asset_number = tk.StringVar()
        self.server_url = tk.StringVar(value="localhost:8000") # Default value

        # Device conditions
        self.device_conditions = {}

        self._setup_ui()

        # Add traces
        # on_asset_number_change will call asset_change_callback internally
        self.asset_number.trace_add("write", self.on_asset_number_change)
        # For operator_id, we directly call the callback
        self.operator_id.trace_add("write", self.asset_change_callback)
        # Server URL changes might also need to trigger checks, if relevant for button states
        self.server_url.trace_add("write", self.asset_change_callback)


    def _setup_ui(self):
        """Sets up the UI elements within this frame."""
        # This frame will act like the old top_info_frame, containing asset_frame and condition_frame side-by-side

        # Asset information section
        asset_frame = ttk.LabelFrame(self, text="Asset Information", padding="5")
        asset_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 2), pady=0)
        asset_frame.columnconfigure(1, weight=1) # Allow the entry column to expand

        ttk.Label(asset_frame, text="Operator ID:", font=(FONTS["normal_font"], self.base_font_size)).grid(row=0, column=0, sticky=tk.W, padx=5, pady=3)
        operator_entry = ttk.Entry(asset_frame, textvariable=self.operator_id, width=30, font=(FONTS["normal_font"], self.base_font_size))
        operator_entry.grid(row=0, column=1, sticky=tk.EW, padx=5, pady=3)

        ttk.Label(asset_frame, text="Asset Number:", font=(FONTS["normal_font"], self.base_font_size)).grid(row=1, column=0, sticky=tk.W, padx=5, pady=3)
        asset_entry = ttk.Entry(asset_frame, textvariable=self.asset_number, width=30, font=(FONTS["normal_font"], self.base_font_size))
        asset_entry.grid(row=1, column=1, sticky=tk.EW, padx=5, pady=3)

        ttk.Label(asset_frame, text="Server URL:", font=(FONTS["normal_font"], self.base_font_size)).grid(row=2, column=0, sticky=tk.W, padx=5, pady=3)
        server_entry = ttk.Entry(asset_frame, textvariable=self.server_url, width=30, font=(FONTS["normal_font"], self.base_font_size))
        server_entry.grid(row=2, column=1, sticky=tk.EW, padx=5, pady=3)

        # Device condition section
        condition_frame = ttk.LabelFrame(self, text="Device Condition", padding="3")
        condition_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(2, 0), pady=0)
        
        condition_summary_frame = ttk.Frame(condition_frame) # To hold label and button
        condition_summary_frame.pack(fill=tk.X, expand=True, padx=2, pady=2)

        self.condition_summary_label = ttk.Label(
            condition_summary_frame,
            text="No condition assessment yet",
            font=(FONTS["normal_font"], self.base_font_size)
        )
        self.condition_summary_label.pack(side=tk.LEFT, padx=3, pady=3, fill=tk.X, expand=True)

        condition_button = ttk.Button(
            condition_summary_frame,
            text="Set Condition",
            command=self.open_condition_window,
            style="TButton" # Assuming TButton style is defined in main app
        )
        condition_button.pack(side=tk.RIGHT, padx=3, pady=3)

    def on_asset_number_change(self, *args):
        """Handle changes to the asset number field."""
        asset_num = self.asset_number.get().strip()
        if asset_num:
            self.device_conditions = load_device_conditions(asset_num)
            if self.device_conditions:
                self.update_condition_summary()
                if self.log_callback:
                    self.log_callback(f"Loaded condition assessment for asset {asset_num}.", "info")
            else:
                self.device_conditions = {} # Clear if none found
                self.update_condition_summary() # Update label to "No condition assessment"
        else:
            self.device_conditions = {} # Clear if asset number is cleared
            self.update_condition_summary()

        if self.asset_change_callback:
            self.asset_change_callback(*args) # Notify main app (e.g., to update button states)

    def open_condition_window(self):
        """Open the device condition window."""
        asset_num = self.asset_number.get().strip()
        if not asset_num:
            messagebox.showwarning(
                "Asset Number Required",
                "Please enter an asset number before setting device conditions.",
                parent=self.main_app_ref # Correct parent for messagebox
            )
            return

        def on_condition_save(conditions):
            self.device_conditions = conditions
            save_device_conditions(conditions, asset_num) # Pass asset_num here
            self.update_condition_summary()
            if self.log_callback:
                self.log_callback(f"Device condition assessment updated for asset {asset_num}.", "info")

        # Create and show the device condition window
        # Parent is main_app_ref (NexusApp instance)
        window = DeviceConditionWindow(
            self.main_app_ref, 
            callback=on_condition_save,
            current_conditions=self.device_conditions
        )
        self.main_app_ref.wait_window(window)

    def update_condition_summary(self):
        """Update the condition summary label with current conditions."""
        if not self.device_conditions:
            self.condition_summary_label.config(text="No condition assessment yet")
            return

        grade = self.device_conditions.get("grade", "?")
        case = self.device_conditions.get("case_condition", "?")
        screen = self.device_conditions.get("screen_condition", "?")

        summary = f"Grade: {grade} | Case: {case} | Screen: {screen}"
        self.condition_summary_label.config(text=summary)

    # Getter methods for NexusApp to access StringVars if needed, though direct access is also possible.
    def get_operator_id(self) -> str:
        return self.operator_id.get()

    def get_asset_number(self) -> str:
        return self.asset_number.get()

    def get_server_url(self) -> str:
        return self.server_url.get()

if __name__ == '__main__':
    # Example Usage (for testing AssetInputFrame independently)
    root = tk.Tk()
    root.geometry("800x150")
    root.title("Asset Input Frame Test")

    # Dummy callbacks and refs for testing
    def test_log(message, level="info"):
        print(f"LOG [{level.upper()}]: {message}")

    def test_asset_change_callback(*args):
        print(f"Asset change callback triggered. Args: {args}")
        # In real app, this would enable/disable Run Test button, etc.
        op_id = asset_frame.get_operator_id()
        asset_num = asset_frame.get_asset_number()
        print(f"  Operator ID: '{op_id}', Asset Number: '{asset_num}'")
        if op_id and asset_num:
            print("  Conditions met to enable Run button (simulated).")
        else:
            print("  Conditions NOT met to enable Run button (simulated).")


    # Style for testing if not using a full app theme
    style = ttk.Style()
    style.theme_use('clam')
    style.configure("TButton", padding=5)
    style.configure("TLabelFrame", padding=5)
    style.configure("TLabelFrame.Label", font=("Arial", 10, "bold"))


    asset_frame = AssetInputFrame(
        root,
        log_callback=test_log,
        asset_change_callback=test_asset_change_callback,
        main_app_ref=root, # root can act as main_app_ref for DeviceConditionWindow parenting
        base_font_size=10,
        padding="5"
    )
    asset_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    # Simulate NexusApp's _check_run_conditions initially
    test_asset_change_callback()

    root.mainloop()
