import tkinter as tk
from tkinter import ttk
from agent.gui.theme import FONTS # Assuming COLORS are not directly needed for labels here

class SystemInfoFrame(ttk.LabelFrame):
    def __init__(self, parent, text="System Information", base_font_size=10, padding="3", **kwargs):
        super().__init__(parent, text=text, padding=padding, **kwargs)
        self.base_font_size = base_font_size
        self.system_info_data = None
        self.serial_number = "Unknown"

        # Configure grid columns for 4-column layout within this LabelFrame
        self.columnconfigure(0, weight=0)
        self.columnconfigure(1, weight=1)
        self.columnconfigure(2, weight=0)
        self.columnconfigure(3, weight=1)

    def update_display(self, system_info_data, serial_number):
        """Update the system information display with new data."""
        self.system_info_data = system_info_data
        self.serial_number = serial_number
        self._populate_display()

    def _clear_display(self):
        """Clear existing widgets in the frame."""
        for widget in self.winfo_children():
            widget.destroy()
        # Re-apply column configurations after clearing, as they might be lost if all children are removed
        self.columnconfigure(0, weight=0)
        self.columnconfigure(1, weight=1)
        self.columnconfigure(2, weight=0)
        self.columnconfigure(3, weight=1)


    def _populate_display(self):
        """Populate the frame with system information labels."""
        self._clear_display() # Clear previous entries

        if not self.system_info_data:
            ttk.Label(self, text="System information not available.", font=(FONTS["normal_font"], self.base_font_size)).grid(row=0, column=0, columnspan=4, sticky=tk.W, padx=5, pady=5)
            return

        label_font = (FONTS["normal_font"], self.base_font_size)
        pady_val = 1  # Reduced padding
        padx_val = (3, 5)  # Label padx, Value padx

        # Row 0: Serial Number, CPU
        ttk.Label(self, text="Serial#:", font=label_font).grid(row=0, column=0, sticky=tk.W, padx=padx_val[0], pady=pady_val)
        ttk.Label(self, text=self.serial_number, font=label_font).grid(row=0, column=1, sticky=tk.EW, padx=padx_val[1], pady=pady_val)

        ttk.Label(self, text="CPU:", font=label_font).grid(row=0, column=2, sticky=tk.W, padx=padx_val[0], pady=pady_val)
        cpu_info = self.system_info_data.get('cpu', 'N/A')
        ttk.Label(self, text=cpu_info, font=label_font, wraplength=200).grid(row=0, column=3, sticky=tk.EW, padx=padx_val[1], pady=pady_val)

        # Row 1: Memory, Graphics
        ttk.Label(self, text="Memory:", font=label_font).grid(row=1, column=0, sticky=tk.W, padx=padx_val[0], pady=pady_val)
        ttk.Label(self, text=self.system_info_data.get('memory', 'N/A'), font=label_font).grid(row=1, column=1, sticky=tk.EW, padx=padx_val[1], pady=pady_val)

        ttk.Label(self, text="Graphics:", font=label_font).grid(row=1, column=2, sticky=tk.W, padx=padx_val[0], pady=pady_val)
        gpus = self.system_info_data.get('gpus', 'N/A')
        if isinstance(gpus, list):
            gpu_text = ", ".join(gpus) if gpus else "N/A"
        else:
            gpu_text = str(gpus)
        ttk.Label(self, text=gpu_text, font=label_font, wraplength=200).grid(row=1, column=3, sticky=tk.EW, padx=padx_val[1], pady=pady_val)

        # Row 2: Resolution, Battery
        ttk.Label(self, text="Resolution:", font=label_font).grid(row=2, column=0, sticky=tk.W, padx=padx_val[0], pady=pady_val)
        ttk.Label(self, text=self.system_info_data.get('screen_resolution', 'N/A'), font=label_font).grid(row=2, column=1, sticky=tk.EW, padx=padx_val[1], pady=pady_val)

        ttk.Label(self, text="Battery:", font=label_font).grid(row=2, column=2, sticky=tk.W, padx=padx_val[0], pady=pady_val)
        batt = self.system_info_data.get('battery', {})
        if batt.get('present'):
            batt_lines = [
                f"{batt.get('percent', 'N/A')}% ({batt.get('status', 'N/A')})",
            ]
            if batt.get('health'):
                batt_lines.append(f"H:{batt['health']}%")
            if batt.get('cycle_count'):
                batt_lines.append(f"C:{batt['cycle_count']}")
            ttk.Label(self, text="; ".join(batt_lines), font=label_font, wraplength=200).grid(row=2, column=3, sticky=tk.EW, padx=padx_val[1], pady=pady_val)
        else:
            ttk.Label(self, text="No battery", font=label_font).grid(row=2, column=3, sticky=tk.EW, padx=padx_val[1], pady=pady_val)

        # Row 3: Disks
        disks_info = self.system_info_data.get('disks', [])
        disk_summaries = []
        for d in disks_info[:2]: 
            if isinstance(d, dict):
                status = "OK" if d.get('smart_passed') else "BAD" if d.get('smart_passed') is not None else "?"
                size = f"{d.get('size_gb', '?')}G"
                disk_summaries.append(f"{d.get('path', '?').split('/')[-1]}: {d.get('model','?')} {size} [{status}]")
            else:
                disk_summaries.append(str(d))
        if len(disks_info) > 2:
            disk_summaries.append(f"+{len(disks_info)-2} more")
        
        ttk.Label(self, text="Disks:", font=label_font).grid(row=3, column=0, sticky=tk.W, padx=padx_val[0], pady=pady_val)
        disk_display_text = "; ".join(disk_summaries) if disk_summaries else "No disks"
        ttk.Label(self, text=disk_display_text, font=label_font, wraplength=450).grid(row=3, column=1, columnspan=3, sticky=tk.EW, padx=padx_val[1], pady=pady_val)

if __name__ == '__main__':
    # Example usage (for testing purposes)
    root = tk.Tk()
    root.geometry("600x200")
    
    # Sample data
    sample_sys_info = {
        'cpu': 'Intel Core i7-8750H @ 2.20GHz',
        'memory': '16 GB',
        'gpus': ['NVIDIA GeForce GTX 1050 Ti', 'Intel UHD Graphics 630'],
        'screen_resolution': '1920x1080',
        'battery': {'present': True, 'percent': 80, 'status': 'Charging', 'health': 90, 'cycle_count': 150},
        'disks': [
            {'path': '/dev/sda', 'model': 'Samsung SSD 970 EVO', 'size_gb': 500, 'smart_passed': True},
            {'path': '/dev/sdb', 'model': 'Seagate HDD ST2000LM015', 'size_gb': 2000, 'smart_passed': True}
        ]
    }
    sample_serial = "TESTSERIAL123"

    # Create and pack the frame
    sys_frame = SystemInfoFrame(root, text="System Details", base_font_size=10)
    sys_frame.pack(padx=10, pady=10, fill=tk.X, expand=True)
    
    # Update with data
    sys_frame.update_display(sample_sys_info, sample_serial)
    
    # Example of updating again with different data
    def update_again():
        new_sys_info = {
            'cpu': 'AMD Ryzen 9 5900X',
            'memory': '32 GB',
            'gpus': ['NVIDIA GeForce RTX 3080'],
            'screen_resolution': '2560x1440',
            'battery': {'present': False},
            'disks': [
                {'path': '/dev/nvme0n1', 'model': 'WD Black SN850', 'size_gb': 1000, 'smart_passed': True}
            ]
        }
        new_serial = "NEWSERIAL789"
        sys_frame.update_display(new_sys_info, new_serial)
        sys_frame.config(text="Updated System Details") # Update LabelFrame text

    root.after(5000, update_again) # Update after 5 seconds

    root.mainloop()
