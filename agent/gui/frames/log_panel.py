import tkinter as tk
from tkinter import ttk, scrolledtext
import datetime
from agent.gui.theme import COLORS, FONTS

class LogPanel(ttk.Frame):
    def __init__(self, parent):
        super().__init__(parent)

        # Log area
        self.log_text_area = scrolledtext.ScrolledText(
            self,
            wrap=tk.WORD,
            font=(FONTS["log_font"], FONTS["log_font_size"]),
            bg=COLORS["bg_light"],
            fg=COLORS["text_dark"],
            state="disabled",
            height=10,
        )
        self.log_text_area.pack(pady=5, padx=5, fill=tk.BOTH, expand=True)

        # Status label
        self.status_label = ttk.Label(
            self, text="Status: Idle", font=FONTS.get("status", FONTS["normal_font"]), anchor="w"
        )
        self.status_label.pack(pady=(0, 5), padx=5, fill=tk.X)

        self.log("LogPanel initialized.", "debug")

    def update_status(self, message: str):
        """Updates the status label."""
        self.status_label.config(text=f"Status: {message}")
        # self.log(f"Status updated: {message}", "debug") # Avoid self-logging status updates unless specifically needed

    def log(self, message: str, level: str = "info"):
        """Logs a message to the text area with a timestamp and level."""
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        formatted_message = f"[{timestamp}] [{level.upper()}] {message}\n"

        self.log_text_area.config(state="normal")
        if level.lower() == "error":
            self.log_text_area.insert(tk.END, formatted_message, "error")
        elif level.lower() == "warning":
            self.log_text_area.insert(tk.END, formatted_message, "warning")
        elif level.lower() == "debug":
            self.log_text_area.insert(tk.END, formatted_message, "debug")
        else:
            self.log_text_area.insert(tk.END, formatted_message, "info")
        
        self.log_text_area.tag_config("error", foreground=COLORS["error"])
        self.log_text_area.tag_config("warning", foreground=COLORS["warning"])
        self.log_text_area.tag_config("debug", foreground=COLORS["debug_fg"]) # Assuming a debug color
        self.log_text_area.tag_config("info", foreground=COLORS["text_dark"]) # Default color for info

        self.log_text_area.see(tk.END)  # Scroll to the end
        self.log_text_area.config(state="disabled")
