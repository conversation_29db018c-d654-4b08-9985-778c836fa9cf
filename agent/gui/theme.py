#!/usr/bin/env python3
"""
Arcoa Nexus Theme

This module provides theme colors and styling for the Arcoa Nexus application.
"""

# Styled colors
COLORS = {
    # --- Pointing Device Test Theme Additions ---
    "pointing_test_bg": "#121212",  # Main background for pointing device test
    "pointing_test_canvas_bg": "#121212",  # <PERSON>vas background
    "pointing_test_label_fg": "#FFFFFF",  # Label foreground
    "pointing_test_button_bg": "#FF5BFF",  # Button background
    "pointing_test_button_fg": "#FFFFFF",  # Button foreground
    "pointing_test_status_pass_fg": "#00FF00",  # Status label PASS
    "pointing_test_status_fail_fg": "#FF3333",  # Status label FAIL
    "pointing_test_status_pending_fg": "#FFFF00",  # Status label pending/neutral
    "pointing_test_status_bg": "#121212",  # Status label background
    "debug_fg": "#00BFFF",  # Blue for debug log messages

    "primary": "#ad2b2b",  # Red highlight
    "success": "#4CAF50",
    "warning": "#FF0000",
    "error": "#691111",  # Orange-red for errors/highlights
    "bg_light": "#333232",  # Dark gray
    "bg_dark": "#1a1919",  # Even darker for contrast
    "text_light": "#FFFFFF",
    "text_dark": "#CCCCCC",
    "accent": "#3d3d3d",
    "title": "#FFFFFF",
    "button": "#393d47",
    "button_hover": "#5d5d5d",
    "button_active": "#7d7d7d",
    "button_disabled": "#666666",
    "button_text": "#FFFFFF",
    "button_text_hover": "#FFFFFF",
    "button_text_active": "#FFFFFF",
    "button_text_disabled": "#CCCCCC",
    "low_res_font_size": 8,
    "high_res_font_size": 16,
    "small_font_size": 10,
    "large_font_size": 18,
    "minimum_resolution": (1024, 768),
    "font_family": "Terminal"
}

FONTS = {
    # --- Pointing Device Test Theme Additions ---
    "pointing_test_font": ("Terminal", 6),
    "pointing_test_status_font": ("Terminal", 6, "bold"),

    "normal_font": "Terminal",
    "small_font": "Terminal",
    "large_font": "Terminal",
    "log_font": "Terminal",
    "log_font_size": 8,
    "profile_font": "Terminal",
    "profile_font_size": 10,
}

SIZES = {
    "log_window_height": 1,
    "padding_large": 20,
    "padding_small": 5,
    "padding_medium": 10,
}
