#!/usr/bin/env python3
"""
Arcoa Nexus Profile Editor

This module provides a GUI for creating and editing test profiles.
"""
import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from typing import Dict, List, Any, Optional, Callable
import os

from agent.tests.profiles import (
    Profile, save_profile, load_profile, get_all_profiles, delete_profile
)
from agent.tests.test_framework import get_available_tests, TestCategory # TestCategory might be useful for sorting if not already
from agent.gui.theme import COLORS # Added import


class ProfileEditor(tk.Toplevel):
    """Profile editor window for managing test profiles."""

    def __init__(self, parent, callback: Optional[Callable] = None):
        """
        Initialize the profile editor.

        Args:
            parent: Parent window
            callback: Optional callback function to call when profiles are updated
        """
        super().__init__(parent)
        self.parent = parent
        self.callback = callback

        self.title("Test Profile Editor")
        self.geometry("1000x700")
        self.minsize(950, 650) # Ensure this size is adequate for all controls

        self.transient(parent)
        self.grab_set()

        self.current_profile = None
        self.has_unsaved_changes = False
        self.test_vars: Dict[str, tk.BooleanVar] = {} # Initialize test_vars here
        self.available_tests = sorted(get_available_tests(), key=lambda t: (t["category"], t["name"])) # Sort for consistent display

        self.create_ui()
        self.load_profiles()
        self.focus_set()
        self.protocol("WM_DELETE_WINDOW", self.on_close) # Handle window close button

    def create_ui(self):
        """Create the user interface."""
        self.style = ttk.Style() # Moved to the beginning

        main_frame = ttk.Frame(self, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        main_frame.configure(style="Background.TFrame") # Assuming a base style for background

        # Define custom styles using theme COLORS
        self.style.configure("Background.TFrame", background=COLORS["bg_dark"]) # General background for frames
        self.style.configure("TLabel", background=COLORS["bg_dark"], foreground=COLORS["text_light"], font=("Terminal", 10))

        self.style.configure("TLabelframe", 
                             background=COLORS["bg_dark"], 
                             borderwidth=1, 
                             relief="solid",
                             padding=5)
        self.style.configure("TLabelframe.Label", 
                             foreground=COLORS["primary"], 
                             background=COLORS["bg_dark"], 
                             font=("Terminal", 11, "bold"))

        self.style.configure("ProfileEditor.TNotebook", background=COLORS["bg_dark"], borderwidth=0)
        self.style.configure("ProfileEditor.TNotebook.Tab",
                             background=COLORS["bg_light"],
                             foreground=COLORS["text_dark"],
                             padding=[8, 4],
                             font=("Terminal", 10, "bold"))
        self.style.map("ProfileEditor.TNotebook.Tab",
                       background=[("selected", COLORS["primary"]), ("active", COLORS["accent"])],
                       foreground=[("selected", COLORS["text_light"]), ("active", COLORS["text_light"])])
        
        # Style for frames within notebook tabs
        self.style.configure("ProfileEditor.Tab.TFrame", background=COLORS["bg_dark"]) 

        self.style.configure("ProfileEditor.TCheckbutton",
                             background=COLORS["bg_dark"], # Match its parent frame (scrollable_frame)
                             foreground=COLORS["text_light"],
                             font=("Terminal", 10),
                             padding=3)
        self.style.map("ProfileEditor.TCheckbutton",
                       indicatorcolor=[('selected', COLORS["primary"]), ('!selected', COLORS["accent"])],
                       foreground=[('disabled', COLORS["text_dark"])],
                       background=[('active', COLORS["accent"])]) # Added active state for background

        # PanedWindow for resizable left and right panels
        paned_window = ttk.PanedWindow(main_frame, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True)

        left_panel = ttk.Frame(paned_window, width=250) # Initial width for profile list
        paned_window.add(left_panel, weight=1) # Allow some resizing, but less emphasis
        left_panel.configure(style="Background.TFrame")

        right_panel = ttk.Frame(paned_window)
        paned_window.add(right_panel, weight=3) # Details panel takes more space
        right_panel.configure(style="Background.TFrame")

        # --- Left Panel - Profile List ---
        profile_frame = ttk.LabelFrame(left_panel, text="Profiles", padding="5") # Will use TLabelframe style
        profile_frame.pack(fill=tk.BOTH, expand=True, padx=(0,5))

        list_frame = ttk.Frame(profile_frame)
        list_frame.pack(fill=tk.BOTH, expand=True)
        
        profile_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL)
        self.profile_listbox = tk.Listbox(
            list_frame,
            selectmode=tk.SINGLE,
            yscrollcommand=profile_scrollbar.set,
            font=("Terminal", 12),
            exportselection=False, # Important for Toplevels
            background=COLORS["bg_light"],
            foreground=COLORS["text_light"],
            selectbackground=COLORS["primary"],
            selectforeground=COLORS["text_light"],
            highlightthickness=0, # Remove default border
            borderwidth=0
        )
        profile_scrollbar.config(command=self.profile_listbox.yview)
        profile_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.profile_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.profile_listbox.bind('<<ListboxSelect>>', self.on_profile_select)

        button_frame = ttk.Frame(profile_frame)
        button_frame.pack(fill=tk.X, pady=(5, 0))
        self.new_button = ttk.Button(button_frame, text="New", command=self.create_new_profile)
        self.new_button.pack(side=tk.LEFT, padx=2, fill=tk.X, expand=True)
        self.delete_button = ttk.Button(button_frame, text="Delete", command=self.delete_current_profile, state=tk.DISABLED)
        self.delete_button.pack(side=tk.LEFT, padx=2, fill=tk.X, expand=True)
        self.clone_button = ttk.Button(button_frame, text="Clone", command=self.clone_current_profile, state=tk.DISABLED)
        self.clone_button.pack(side=tk.LEFT, padx=2, fill=tk.X, expand=True)

        # --- Right Panel - Profile Details ---
        details_frame = ttk.LabelFrame(right_panel, text="Profile Details", padding="5") # Will use TLabelframe style
        details_frame.pack(fill=tk.BOTH, expand=True, padx=(5,0))

        form_frame = ttk.Frame(details_frame)
        form_frame.pack(fill=tk.X, pady=(0, 10))
        form_frame.columnconfigure(1, weight=1) # Allow entry column to expand
        form_frame.configure(style="Background.TFrame")

        ttk.Label(form_frame, text="Name:", style="TLabel").grid(row=0, column=0, sticky=tk.W, padx=5, pady=3)
        self.name_var = tk.StringVar()
        self.name_var.trace_add("write", self.on_form_change)
        name_entry = ttk.Entry(form_frame, textvariable=self.name_var)
        name_entry.grid(row=0, column=1, sticky=tk.EW, padx=5, pady=3)

        ttk.Label(form_frame, text="Device Type:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=3)
        self.device_type_var = tk.StringVar()
        self.device_type_var.trace_add("write", self.on_form_change)
        device_types = ["Desktop", "Laptop", "Server", "Tablet", "Any"] # Consider making this dynamic or configurable
        device_type_combo = ttk.Combobox(form_frame, textvariable=self.device_type_var, values=device_types, state="readonly")
        device_type_combo.grid(row=1, column=1, sticky=tk.EW, padx=5, pady=3)

        ttk.Label(form_frame, text="Description:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=3)
        self.description_var = tk.StringVar()
        self.description_var.trace_add("write", self.on_form_change)
        description_entry = ttk.Entry(form_frame, textvariable=self.description_var)
        description_entry.grid(row=2, column=1, sticky=tk.EW, padx=5, pady=3)

        tests_outer_frame = ttk.LabelFrame(details_frame, text="Available Tests") # Will use TLabelframe style
        tests_outer_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 5))

        self.test_notebook = ttk.Notebook(tests_outer_frame, style="ProfileEditor.TNotebook")
        self.test_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # self.test_vars: Dict[str, tk.BooleanVar] = {} # Moved to __init__
        
        categories = sorted(list(set(t["category"] for t in self.available_tests)))

        # Create tab for "All Tests" first
        self._create_scrollable_test_tab("All Tests", self.available_tests, show_category=True)

        for category_name in categories:
            category_tests = [t for t in self.available_tests if t["category"] == category_name]
            self._create_scrollable_test_tab(category_name.capitalize(), category_tests)
        
        # --- Action Buttons ---
        ttk.Separator(details_frame, orient="horizontal").pack(fill=tk.X, pady=(5,5))
        
        save_status_frame = ttk.Frame(details_frame)
        save_status_frame.pack(fill=tk.X, pady=(0,5))

        self.status_var = tk.StringVar(value="")
        status_label = ttk.Label(save_status_frame, textvariable=self.status_var, foreground=COLORS["error"], font=("Terminal", 9, "italic"))
        status_label.pack(side=tk.LEFT, padx=5, anchor=tk.W)
        status_label.configure(style="TLabel") # Ensure it picks up themed background
        
        save_reminder_label = ttk.Label(
            save_status_frame,
            text="Remember to save changes!",
            font=("Terminal", 9, "bold"),
            foreground=COLORS["warning"] 
        )
        save_reminder_label.pack(side=tk.LEFT, padx=10, expand=True, anchor=tk.E)
        save_reminder_label.configure(style="TLabel") # Ensure it picks up themed background

        # self.style = ttk.Style() # Already initialized at the top
        self.style.configure("Save.TButton", font=("Terminal", 11, "bold"), padding=(10,5))
        # Consider adding background/foreground if theme doesn't make it prominent enough
        # self.style.configure("Save.TButton", background=COLORS["success"], foreground="white")

        self.save_button = ttk.Button(
            save_status_frame,
            text="Save Profile",
            command=self.save_current_profile,
            style="Save.TButton",
            state=tk.DISABLED
        )
        self.save_button.pack(side=tk.RIGHT, padx=5)
        
        # --- Bottom Close Button ---
        # This is outside the right_panel's details_frame, at the bottom of main_frame
        close_button_frame = ttk.Frame(main_frame, style="Background.TFrame") # Parent is main_frame
        close_button_frame.pack(fill=tk.X, pady=(10,0), side=tk.BOTTOM)
        
        close_button = ttk.Button(close_button_frame, text="Close", command=self.on_close)
        close_button.pack(side=tk.RIGHT, padx=5)

    def _create_scrollable_test_tab(self, tab_name: str, tests_for_tab: List[Dict[str, Any]], show_category: bool = False):
        """Helper to create a tab with scrollable content for tests."""
        tab_container = ttk.Frame(self.test_notebook, style="ProfileEditor.Tab.TFrame")
        self.test_notebook.add(tab_container, text=tab_name)

        canvas = tk.Canvas(tab_container, borderwidth=0, highlightthickness=0, background=COLORS["bg_dark"])
        scrollbar = ttk.Scrollbar(tab_container, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas, style="ProfileEditor.Tab.TFrame")

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        canvas_frame_id = canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        def _configure_canvas_and_frame(event):
            canvas.itemconfig(canvas_frame_id, width=event.width)
            canvas.configure(scrollregion=canvas.bbox("all"))
        canvas.bind("<Configure>", _configure_canvas_and_frame)
        
        scrollbar.pack(side="right", fill="y")
        canvas.pack(side="left", fill="both", expand=True)

        for test_info in tests_for_tab:
            test_path = test_info["full_path"]
            test_name = test_info["name"]
            test_desc = test_info["description"]
            category = test_info["category"]

            if test_path not in self.test_vars:
                var = tk.BooleanVar(value=False)
                var.trace_add("write", self.on_test_change)
                self.test_vars[test_path] = var
            else:
                var = self.test_vars[test_path]
            
            display_text = f"{test_name} - {test_desc}"
            if show_category:
                display_text = f"{test_name} ({category.capitalize()}) - {test_desc}"

            cb = ttk.Checkbutton(
                scrollable_frame,
                text=display_text,
                variable=var,
                command=self.on_form_change,
                style="ProfileEditor.TCheckbutton" # Apply custom checkbutton style
            )
            cb.pack(anchor=tk.W, padx=5, pady=2)
            self.test_vars[test_path] = var

    def load_profiles(self):
        """Load all available profiles into the listbox."""
        self.profile_listbox.delete(0, tk.END)
        profiles = sorted(get_all_profiles(), key=lambda p: p.name.lower()) # Sort profiles by name
        for profile in profiles:
            self.profile_listbox.insert(tk.END, profile.name)
        
        # Reset UI state if no profiles
        if not profiles:
            self._clear_form_and_disable_buttons()


    def on_profile_select(self, event=None): # Allow calling without event
        """Handle profile selection in the listbox."""
        if self.has_unsaved_changes:
            if not messagebox.askyesno("Unsaved Changes",
                                       "You have unsaved changes. Discard them and load selected profile?",
                                       parent=self):
                # User chose not to discard, try to reselect previous if possible
                # This is tricky, as the selection has already changed.
                # For now, we just return and leave the new selection visually.
                # A more complex solution would involve blocking the selection change.
                return 
        
        selection = self.profile_listbox.curselection()
        if not selection:
            self._clear_form_and_disable_buttons()
            return

        profile_name = self.profile_listbox.get(selection[0])
        profile = load_profile(profile_name)

        if profile:
            self.current_profile = profile
            self.update_form(profile)
            self.delete_button.config(state=tk.NORMAL)
            self.clone_button.config(state=tk.NORMAL)
            self.save_button.config(state=tk.NORMAL) # Enable save for modifications
            self.has_unsaved_changes = False # Reset flag after loading
            self.status_var.set("")
        else:
            messagebox.showerror("Error", f"Could not load profile: {profile_name}", parent=self)
            self._clear_form_and_disable_buttons()

    def _clear_form_and_disable_buttons(self):
        """Clears the form and disables profile-specific buttons."""
        self.current_profile = None
        self.name_var.set("")
        self.device_type_var.set("")
        self.description_var.set("")
        for var in self.test_vars.values():
            var.set(False)
        self.delete_button.config(state=tk.DISABLED)
        self.clone_button.config(state=tk.DISABLED)
        self.save_button.config(state=tk.DISABLED)
        self.has_unsaved_changes = False
        self.status_var.set("")

    def update_form(self, profile: Profile):
        """Update the form with profile data."""
        self.name_var.set(profile.name)
        self.device_type_var.set(profile.device_type if profile.device_type else "Any")
        self.description_var.set(profile.description)

        for test_path, var in self.test_vars.items():
            var.set(test_path in profile.tests)
        
        self.has_unsaved_changes = False # Reset flag after loading
        self.status_var.set("")


    def create_new_profile(self):
        """Create a new profile."""
        if self.has_unsaved_changes:
            if not messagebox.askyesno("Unsaved Changes",
                                       "You have unsaved changes. Discard them and create a new profile?",
                                       parent=self):
                return

        name = simpledialog.askstring("New Profile", "Enter name for the new profile:", parent=self)
        if not name or not name.strip():
            return

        name = name.strip()
        existing_profiles = [p.name.lower() for p in get_all_profiles()]
        if name.lower() in existing_profiles:
            messagebox.showerror("Error", f"A profile named '{name}' already exists.", parent=self)
            return

        profile = Profile(name=name, description="", device_type="Any", tests=[])
        if save_profile(profile):
            self.load_profiles()
            for i in range(self.profile_listbox.size()):
                if self.profile_listbox.get(i) == name:
                    self.profile_listbox.selection_clear(0, tk.END)
                    self.profile_listbox.selection_set(i)
                    self.profile_listbox.activate(i)
                    self.profile_listbox.see(i)
                    self.on_profile_select(None) # Manually trigger update
                    break
            if self.callback: self.callback()
        else:
            messagebox.showerror("Error", f"Failed to save new profile '{name}'.", parent=self)


    def clone_current_profile(self):
        """Clone the current profile."""
        if not self.current_profile: return

        initial_clone_name = f"{self.current_profile.name} (Copy)"
        name = simpledialog.askstring("Clone Profile", "Enter name for the cloned profile:",
                                      parent=self, initialvalue=initial_clone_name)
        if not name or not name.strip(): return
        
        name = name.strip()
        existing_profiles = [p.name.lower() for p in get_all_profiles()]
        if name.lower() in existing_profiles:
            messagebox.showerror("Error", f"A profile named '{name}' already exists.", parent=self)
            return

        cloned_profile = Profile(
            name=name,
            description=self.current_profile.description,
            device_type=self.current_profile.device_type,
            tests=list(self.current_profile.tests), # Ensure it's a copy
            test_args=dict(self.current_profile.test_args) if self.current_profile.test_args else {} # Deep copy if mutable
        )
        if save_profile(cloned_profile):
            self.load_profiles()
            for i in range(self.profile_listbox.size()):
                if self.profile_listbox.get(i) == name:
                    self.profile_listbox.selection_clear(0, tk.END)
                    self.profile_listbox.selection_set(i)
                    self.profile_listbox.activate(i)
                    self.profile_listbox.see(i)
                    self.on_profile_select(None)
                    break
            if self.callback: self.callback()
        else:
            messagebox.showerror("Error", f"Failed to save cloned profile '{name}'.", parent=self)


    def delete_current_profile(self):
        """Delete the current profile."""
        if not self.current_profile: return

        if messagebox.askyesno("Confirm Delete",
                               f"Are you sure you want to delete the profile '{self.current_profile.name}'?",
                               parent=self):
            if delete_profile(self.current_profile.name):
                self._clear_form_and_disable_buttons()
                self.load_profiles()
                if self.callback: self.callback()
            else:
                messagebox.showerror("Error", f"Failed to delete profile '{self.current_profile.name}'.", parent=self)

    def save_current_profile(self):
        """Save the current profile with form data."""
        if not self.current_profile and not self.name_var.get().strip():
             messagebox.showerror("Error", "Cannot save. No profile selected or new profile name is empty.", parent=self)
             return

        original_name = self.current_profile.name if self.current_profile else None
        new_name = self.name_var.get().strip()
        device_type = self.device_type_var.get()
        description = self.description_var.get().strip()

        if not new_name:
            messagebox.showerror("Error", "Profile name cannot be empty.", parent=self)
            return

        selected_tests = [path for path, var in self.test_vars.items() if var.get()]
        if not selected_tests:
            messagebox.showerror("Error", "Please select at least one test for the profile.", parent=self)
            return

        # Check for name collision if renaming or creating new
        if new_name.lower() != (original_name.lower() if original_name else None):
            existing_profiles = [p.name.lower() for p in get_all_profiles()]
            if new_name.lower() in existing_profiles:
                messagebox.showerror("Error", f"A profile named '{new_name}' already exists.", parent=self)
                return
        
        # If name changed, old profile needs to be deleted after new one is saved (or before if it's an update)
        profile_to_save = Profile(
            name=new_name,
            description=description,
            device_type=device_type,
            tests=selected_tests,
            test_args=self.current_profile.test_args if self.current_profile else {}
        )

        if save_profile(profile_to_save):
            if original_name and new_name != original_name: # If renamed, delete old one
                delete_profile(original_name)
            
            messagebox.showinfo("Success", f"Profile '{new_name}' saved successfully.", parent=self)
            self.current_profile = profile_to_save # Update current profile reference
            self.has_unsaved_changes = False
            self.status_var.set("")
            
            current_selection_index = self.profile_listbox.curselection()
            self.load_profiles() # Reload list
            
            # Try to reselect the saved profile
            found_idx = -1
            for i in range(self.profile_listbox.size()):
                if self.profile_listbox.get(i) == new_name:
                    found_idx = i
                    break
            
            if found_idx != -1:
                self.profile_listbox.selection_clear(0, tk.END)
                self.profile_listbox.selection_set(found_idx)
                self.profile_listbox.activate(found_idx)
                self.profile_listbox.see(found_idx)
                # self.on_profile_select(None) # No, this would trigger unsaved changes prompt
            else: # Should not happen if save was successful
                 self._clear_form_and_disable_buttons()


            if self.callback: self.callback()
        else:
            messagebox.showerror("Error", f"Failed to save profile '{new_name}'.", parent=self)


    def on_form_change(self, *args):
        """Handle changes to form fields."""
        # Only flag unsaved changes if a profile is loaded or a new name is typed
        if self.current_profile or self.name_var.get().strip():
            self.has_unsaved_changes = True
            self.status_var.set("* Unsaved changes")
            self.save_button.config(state=tk.NORMAL)

    def on_test_change(self, *args):
        """Handle changes to test checkboxes."""
        if self.current_profile or self.name_var.get().strip():
            self.has_unsaved_changes = True
            self.status_var.set("* Unsaved changes")
            self.save_button.config(state=tk.NORMAL)

    def on_close(self):
        """Handle window close."""
        if self.has_unsaved_changes:
            response = messagebox.askyesnocancel(
                "Unsaved Changes",
                "You have unsaved changes. Save before closing?",
                parent=self
            )
            if response is None:  # Cancel
                return
            elif response:  # Yes, save
                self.save_current_profile()
                # If save failed or was cancelled by user (e.g. name conflict), don't close yet
                if self.has_unsaved_changes: # Check if still has unsaved changes
                    return

        self.grab_release()
        self.destroy()


if __name__ == "__main__":
    # For testing
    # Make sure agent.tests and agent.tests.profiles are accessible
    # You might need to adjust PYTHONPATH or run from the project's root directory
    # Example: PYTHONPATH=. python3 agent/gui/profile_editor.py
    
    # Mock get_available_tests if agent.tests isn't fully set up for standalone run
    try:
        from agent.tests.test_framework import get_available_tests
        # print(f"Available tests: {get_available_tests()}")
    except ImportError as e:
        print(f"Could not import agent.tests.test_framework: {e}")
        def get_available_tests_mock():
            return [
                {"full_path": "mock.test.test_A", "name": "Mock Test A", "description": "A mock test", "category": "cpu"},
                {"full_path": "mock.test.test_B", "name": "Mock Test B", "description": "Another mock test", "category": "memory"},
                {"full_path": "mock.test.test_C_long", "name": "Mock Test C with a very very very long name and description to check UI wrapping and scrolling capabilities effectively", "description": "This test is designed specifically to be very long to test how the UI handles it.", "category": "cpu"},
            ] + [{"full_path": f"mock.test.test_cpu_{i}", "name": f"Mock CPU Test {i}", "description": f"CPU test variant {i}", "category": "cpu"} for i in range(20)]
        
        # Monkey patch if necessary for testing
        import agent.tests.test_framework
        agent.tests.test_framework.get_available_tests = get_available_tests_mock
        print("Using MOCKED get_available_tests()")


    root = tk.Tk()
    root.title("Main App (Profile Editor Test)")
    root.geometry("300x200")

    def open_editor():
        editor = ProfileEditor(root, callback=lambda: print("Profile editor callback triggered"))
        # root.wait_window(editor) # Not needed if editor is modal and handles its own lifecycle

    ttk.Button(root, text="Open Profile Editor", command=open_editor).pack(padx=20, pady=20)
    
    # Create dummy profiles directory for testing if it doesn't exist
    profiles_dir_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "tests", "profiles")
    if not os.path.exists(profiles_dir_path):
        try:
            os.makedirs(profiles_dir_path)
            print(f"Created dummy profiles directory: {profiles_dir_path}")
        except OSError as e:
            print(f"Error creating dummy profiles directory {profiles_dir_path}: {e}")


    root.mainloop()
