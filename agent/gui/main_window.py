#!/usr/bin/env python3
"""
Arcoa Nexus Main GUI Window

This module provides the main GUI window for the Arcoa Nexus diagnostics tool.
"""
from agent.gui.results_display_window import ResultsDisplayWindow
from agent.gui.end_screen_window import EndScreenWindow
from agent.core.test_orchestrator import TestOrchestrator
from agent.core.result_manager import ResultManager
from agent.gui.frames.test_control_frame import TestControlFrame
from agent.gui.frames.asset_input_frame import AssetInputFrame
from agent.gui.frames.log_panel import LogPanel
import asyncio
import datetime
import json
from logging import debug
import os
import time
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, simpledialog
from typing import Dict, Any, List

# Import from our modules
from agent.hardware.system_info import get_system_info, get_screen_resolution
from agent.tests.cpu_test import run_basic_cpu_test
from agent.tests.ram_test import run_ram_test
from agent.tests.display_test import run_lcd_test_gui
from agent.tests.keyboard_test import run_keyboard_test
from agent.tests.pointing_device_test import run_pointing_device_test
from agent.tests.drive_wipe_test import run_secure_wipe_test
from agent.tests.visual_cpu_test import run_visual_cpu_test
from agent.tests.visual_ram_test import run_visual_ram_test
from agent.tests.battery_test import run_battery_test, run_battery_discharge_test, run_battery_charge_test, run_battery_full_assessment
from agent.tests.profiles import Profile, get_all_profiles, load_profile
from agent.tests.test_framework import run_test_by_name
from agent.gui.profile_editor import ProfileEditor
from agent.gui.device_condition import DeviceConditionWindow, save_device_conditions, load_device_conditions
from agent.utils.network import send_result
from agent.gui.theme import COLORS, FONTS, SIZES
import os
import json

# Load customizable resolution config
CONFIG_PATH = os.path.join(os.path.dirname(__file__), 'resolution_config.json')
try:
    RESOLUTION_CONFIG = json.load(open(CONFIG_PATH))
except Exception:
    RESOLUTION_CONFIG = {"resolutions": {}}


class NexusApp(tk.Tk):
    def __init__(self):
        super().__init__()

        self.title("")
        self.geometry("1024x768")
        # Set minimum size to ensure all elements are visible
        self.minsize(1024, 700)
        self.attributes('-fullscreen', True)  # Disable fullscreen by default
        # Fullscreen mode can be enabled for Linux PXE environment if needed
        # self.attributes('-fullscreen', True)
        self.system_info = None
        self.serial_number = "Unknown"

        # Determine font size based on resolution
        self.base_font_size = COLORS["low_res_font_size"]  # Default
        try:
            res_str = get_screen_resolution(self)
            if res_str and 'x' in res_str:
                width, height = map(int, res_str.split('x'))
                min_width, min_height = COLORS["minimum_resolution"]
                if width > min_width and height > min_height:
                    self.base_font_size = COLORS["high_res_font_size"]
                elif width == min_width and height == min_height:
                    # Could use a medium font size if defined, or stick to low_res for exact match
                    self.base_font_size = COLORS["low_res_font_size"]
        except Exception as e:
            print(f"Error determining screen resolution for font sizing: {e}")
            # Stick with default low_res_font_size

        # StringVars for operator_id, asset_number, server_url are now in AssetInputFrame
        # self.device_conditions dictionary is now in AssetInputFrame

        # Current profile - now managed by TestControlFrame
        # NexusApp still needs to know the currently loaded profile
        self.current_profile = None
        # self.profile_name = tk.StringVar() # Managed by TestControlFrame

        # Device conditions - managed by AssetInputFrame
        # self.device_conditions = {}
        # self.end_screen_summary_data is now managed by TestOrchestrator

        # self.test_results_available is now managed by ResultManager
        # self.test_results_available = False

        # Instantiate ResultManager
        self.result_manager = ResultManager(
            log_callback=self.log_panel.log if hasattr(self, 'log_panel') else lambda m, l: print(f"Early Log: {m}"),
            get_asset_number_callback=lambda: self.asset_input_frame.get_asset_number() if hasattr(self, 'asset_input_frame') else "",
            get_operator_id_callback=lambda: self.asset_input_frame.get_operator_id() if hasattr(self, 'asset_input_frame') else ""
        )

        # Instantiate TestOrchestrator (log_panel will be None initially)
        self.test_orchestrator = TestOrchestrator(
            log_callback=lambda m, l: print(f"Early Orchestrator Log: {m}"),
            result_manager_instance=self.result_manager,
            main_app_ref=self,
            get_current_profile_callback=lambda: self.current_profile
        )

        # Set up the UI
        self.setup_ui_styles()
        self.setup_ui()  # Creates self.log_panel

        # Update log_callbacks for components that need it after log_panel is created
        self.result_manager.log_callback = self.log_panel.log
        self.test_orchestrator.log_callback = self.log_panel.log

        # Collect system information
        self.log_panel.update_status("Collecting system information...")
        self.system_info = get_system_info(self)
        self.serial_number = self.system_info.get("serial_number", "Unknown")
        self.update_system_info_display()
        self.log_panel.update_status("Ready")

        # Traces for operator_id and asset_number are now managed within AssetInputFrame.
        # AssetInputFrame will call self._check_run_conditions_callback when they change.

        # Load device conditions if available (Done AFTER setup_ui)
        # This logic is now within AssetInputFrame's on_asset_number_change

        # Load the Arcoa logo (Done AFTER setup_ui)
        try:
            # Get the base directory
            base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            logo_path = os.path.join(base_dir, "pictures", "arcoalogo.png")

            if os.path.exists(logo_path):
                self.logo_image = tk.PhotoImage(file=logo_path)
                # Resize the image to an appropriate height (e.g., 100 pixels)
                original_height = self.logo_image.height()
                target_height = 100  # Desired height for the logo
                subsample_factor = max(1, int(original_height / target_height))
                self.logo_image = self.logo_image.subsample(subsample_factor)

                # Update the logo label with the image (logo_label is created in setup_ui)
                if hasattr(self, 'logo_label'):
                    self.logo_label.configure(image=self.logo_image)
                    self.log_panel.log("Logo loaded successfully.", "info")
                else:
                    self.log_panel.log("logo_label not found during logo loading.", "warning")
            else:
                self.log_panel.log(f"Logo image not found at: {logo_path}", "warning")
        except Exception as e:
            self.log_panel.log(f"Error loading logo: {str(e)}", "error")

        # Load default profile if available (Done AFTER setup_ui)
        # This logic will be adapted to use TestControlFrame
        if hasattr(self, 'test_control_frame'):
            try:
                from agent.hardware.system_info import get_device_type
                device_type = get_device_type()
                self.log_panel.log(f"Detected device_type for auto-profile: {device_type}", "debug")

                all_profiles = get_all_profiles()
                profile_names_only = [p.name for p in all_profiles]
                self.log_panel.log(f"Available profiles for auto-selection: {profile_names_only}", "debug")

                preferred_profile_name = None
                if device_type == "laptop" and "Laptop" in profile_names_only:
                    preferred_profile_name = "Laptop"
                elif device_type == "desktop" and "Desktop" in profile_names_only:
                    preferred_profile_name = "Desktop"

                if preferred_profile_name:
                    self.log_panel.log(f"Attempting to auto-select profile: {preferred_profile_name}", "info")
                    self.test_control_frame.set_profile_by_name_and_load(preferred_profile_name)
                elif all_profiles:
                    first_profile_name = all_profiles[0].name
                    self.log_panel.log(f"Auto-profile selection: fallback to first profile: {first_profile_name}", "warning")
                    self.test_control_frame.set_profile_by_name_and_load(first_profile_name)
                else:
                    self.log_panel.log("No profiles available for auto-selection.", "warning")
            except Exception as e:
                self.log_panel.log(f"Auto-profile selection failed: {e}", "error")
        else:
            self.log_panel.log("test_control_frame not found during default profile loading.", "warning")

        # Initial check for button state
        self._check_run_conditions()
        # Add resolution menu for dynamic testing of screen configurations
        self.create_menu()
        # Auto-adjust sash and font for detected resolution
        try:
            detected_res = get_screen_resolution(self)
            self.apply_resolution(detected_res, self.base_font_size)
        except Exception:
            pass

    def setup_ui_styles(self):
        """Configure ttk styles for the application."""
        self.style = ttk.Style(self)
        self.style.theme_use('clam')

        self.style.configure(".", background=COLORS["bg_dark"], foreground=COLORS["text_light"],
                             font=(FONTS["normal_font"], self.base_font_size))
        self.style.configure("TFrame", background=COLORS["bg_dark"])
        self.style.configure("TLabel", background=COLORS["bg_dark"], foreground=COLORS["text_light"],
                             font=(FONTS["normal_font"], self.base_font_size))
        self.style.configure("TNotebook", background=COLORS["bg_dark"], borderwidth=0)
        self.style.configure("TNotebook.Tab", background=COLORS["bg_light"], foreground=COLORS["text_dark"],
                             font=(FONTS["normal_font"], COLORS["small_font_size"]), padding=[5, 2])
        self.style.map("TNotebook.Tab",
                       background=[("selected", COLORS["primary"]), ("active", COLORS["accent"])],
                       foreground=[("selected", COLORS["text_light"]), ("active", COLORS["text_light"])])
        self.style.configure("TButton", background=COLORS["button"], foreground=COLORS["button_text"],
                             font=(FONTS["normal_font"], self.base_font_size), padding=5, relief=tk.FLAT, borderwidth=0)
        self.style.map("TButton",
                       background=[('pressed', COLORS["button_active"]), ('active', COLORS["button_hover"]), ('disabled', COLORS["button_disabled"])],
                       foreground=[('disabled', COLORS["button_text_disabled"])])
        self.style.configure("Header.TLabel", font=(FONTS["normal_font"], COLORS["large_font_size"], "bold"),
                             foreground=COLORS["primary"], background=COLORS["bg_dark"])
        self.style.configure("SectionTitle.TLabel", font=(FONTS["normal_font"], self.base_font_size + 2, "bold"),
                             foreground=COLORS["text_light"], background=COLORS["bg_dark"])
        self.style.configure("TListbox", background=COLORS["bg_light"], foreground=COLORS["text_light"],
                             font=(FONTS["normal_font"], self.base_font_size), selectbackground=COLORS["primary"],
                             selectforeground=COLORS["text_light"], relief=tk.FLAT, borderwidth=1)
        self.style.configure("TEntry", fieldbackground=COLORS["bg_light"], foreground=COLORS["text_light"],
                             font=(FONTS["normal_font"], self.base_font_size), insertcolor=COLORS["text_light"],
                             relief=tk.FLAT, borderwidth=1)
        self.style.map("TEntry",
                       bordercolor=[('focus', COLORS["primary"]), ('!focus', COLORS["bg_light"])],
                       fieldbackground=[('disabled', COLORS["button_disabled"])])
        self.style.configure("Vertical.TScrollbar", background=COLORS["bg_light"], troughcolor=COLORS["bg_dark"],
                             arrowcolor=COLORS["text_dark"], borderwidth=0)
        self.style.map("Vertical.TScrollbar",
                       background=[("active", COLORS["accent"])])

    def setup_ui(self):
        """Set up the user interface components with a dark theme and large fonts."""
        style = ttk.Style()
        self.configure(bg=COLORS["bg_dark"])
        style.theme_use('clam')

        base_font_family = FONTS["normal_font"]

        style.configure("TFrame", background=COLORS["bg_dark"])
        style.configure("TLabel", background=COLORS["bg_dark"], foreground=COLORS["text_light"],
                        font=(base_font_family, self.base_font_size))
        style.configure("TLabelframe", background=COLORS["bg_dark"], foreground=COLORS["accent"],
                        font=(base_font_family, self.base_font_size, "bold"), borderwidth=1, relief="solid")
        style.configure("TLabelframe.Label", background=COLORS["bg_dark"], foreground=COLORS["primary"],
                        font=(base_font_family, self.base_font_size - 2, "bold"))
        style.configure("TButton", background=COLORS["button"], foreground=COLORS["button_text"],
                        font=(base_font_family, self.base_font_size, "bold"), borderwidth=1, relief="raised")
        style.map("TButton",
                  background=[('active', COLORS["button_hover"]), ('pressed', COLORS["button_active"]), ('disabled', COLORS["button_disabled"])],
                  foreground=[('disabled', COLORS["button_text_disabled"])],
                  relief=[('pressed', 'sunken'), ('!pressed', 'raised')])
        style.configure("TCheckbutton", background=COLORS["bg_dark"], foreground=COLORS["text_light"],
                        font=(base_font_family, self.base_font_size))
        style.map("TCheckbutton",
                  indicatorbackground=[('selected', COLORS["primary"]), ('!selected', COLORS["accent"])],
                  indicatorforeground=[('selected', COLORS["text_light"]), ('!selected', COLORS["text_light"])])
        style.configure("TEntry", fieldbackground=COLORS["bg_light"], foreground=COLORS["text_light"],
                        font=(base_font_family, self.base_font_size), insertbackground=COLORS["text_light"])
        style.map("TEntry",
                  bordercolor=[('focus', COLORS["primary"]), ('!focus', COLORS["accent"])],
                  borderwidth=[('focus', 2), ('!focus', 1)])
        style.configure("TCombobox",
                        fieldbackground=COLORS["bg_light"],
                        background=COLORS["accent"],
                        foreground=COLORS["text_light"],
                        arrowcolor=COLORS["text_light"],
                        font=(base_font_family, self.base_font_size - 2),
                        insertbackground=COLORS["text_light"],
                        selectbackground=COLORS["bg_light"],
                        selectforeground=COLORS["text_light"])
        style.map("TCombobox",
                  bordercolor=[('focus', COLORS["primary"]), ('!focus', COLORS["accent"])],
                  borderwidth=[('focus', 2), ('!focus', 1)],
                  fieldbackground=[('readonly', COLORS["bg_light"])],
                  foreground=[('readonly', COLORS["text_light"])])
        self.option_add('*TCombobox*Listbox.background', COLORS["bg_light"])
        self.option_add('*TCombobox*Listbox.foreground', COLORS["text_light"])
        self.option_add('*TCombobox*Listbox.selectBackground', COLORS["primary"])
        self.option_add('*TCombobox*Listbox.selectForeground', COLORS["text_light"])
        self.option_add('*TCombobox*Listbox.font', (base_font_family, self.base_font_size - 4))
        style.configure("Horizontal.TProgressbar", background=COLORS["primary"], troughcolor=COLORS["bg_light"])
        style.configure("Vertical.TScrollbar", background=COLORS["button"], troughcolor=COLORS["bg_light"],
                        arrowcolor=COLORS["text_light"], borderwidth=0, relief='flat')
        style.map("Vertical.TScrollbar", background=[('active', COLORS["button_hover"])])
        style.configure("Horizontal.TScrollbar", background=COLORS["button"], troughcolor=COLORS["bg_light"],
                        arrowcolor=COLORS["text_light"], borderwidth=0, relief='flat')
        style.map("Horizontal.TScrollbar", background=[('active', COLORS["button_hover"])])
        style.configure("TPanedwindow", background=COLORS["bg_dark"])
        style.configure("Sash", background=COLORS["accent"], sashthickness=6, gripcount=0,
                        relief='raised', borderwidth=1)
        style.map("Sash", background=[('active', COLORS["primary"])])

        main_frame = ttk.Frame(self, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=0)

        self.logo_label = ttk.Label(title_frame, background=COLORS["bg_dark"])
        self.logo_label.pack(side=tk.LEFT, padx=(0, 10))

        title_label = ttk.Label(title_frame, text="", font=(base_font_family, self.base_font_size + 8, "bold"),
                                 foreground=COLORS["primary"])
        title_label.pack(side=tk.LEFT)

        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True)

        paned_window = ttk.PanedWindow(content_frame, orient=tk.VERTICAL)
        paned_window.pack(fill=tk.BOTH, expand=True)
        self.paned_window = paned_window

        left_panel_container = ttk.Frame(paned_window)
        paned_window.add(left_panel_container, weight=3)

        left_canvas = tk.Canvas(left_panel_container, background=COLORS["bg_dark"], highlightthickness=0)
        left_scrollbar = ttk.Scrollbar(left_panel_container, orient="vertical", command=left_canvas.yview)
        left_panel = ttk.Frame(left_canvas, padding="5")

        left_canvas.configure(yscrollcommand=left_scrollbar.set)
        left_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        left_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        left_canvas_frame = left_canvas.create_window((0, 0), window=left_panel, anchor=tk.NW)

        def configure_left_scroll(event):
            left_canvas.configure(scrollregion=left_canvas.bbox("all"))
            left_canvas.itemconfig(left_canvas_frame, width=left_canvas.winfo_width())

        left_panel.bind("<Configure>", configure_left_scroll)
        left_canvas.bind("<Configure>", lambda e: left_canvas.itemconfig(left_canvas_frame, width=left_canvas.winfo_width()))

        right_panel = ttk.Frame(paned_window, padding="2")
        paned_window.add(right_panel, weight=1)

        self.log_panel = LogPanel(right_panel)
        self.log_panel.pack(fill=tk.BOTH, expand=True)

        self.asset_input_frame = AssetInputFrame(left_panel, log_callback=self.log_panel.log,
                                                  asset_change_callback=self._check_run_conditions_callback,
                                                  main_app_ref=self, base_font_size=self.base_font_size)
        self.asset_input_frame.pack(fill=tk.X, pady=5)

        system_frame = ttk.LabelFrame(left_panel, text="System Information", padding="3")
        system_frame.pack(fill=tk.X, pady=2)
        self.system_frame = system_frame

        self.test_control_frame = TestControlFrame(parent=left_panel, log_callback=self.log_panel.log,
                                                    main_app_ref=self, base_font_size=self.base_font_size,
                                                    on_load_profile_action=self._on_profile_loaded_action,
                                                    on_run_tests_action=self.run_tests,
                                                    on_view_results_action=self.view_results,
                                                    on_edit_profiles_action=self._on_edit_profiles_action)
        self.test_control_frame.pack(fill=tk.X, pady=5)

        self.log_panel.log("Arc Nexus initialized.")
        self.log_panel.log("Select tests to run and enter asset information.")

    def update_system_info_display(self):
        """Update the system information display with collected data."""
        for widget in self.system_frame.winfo_children():
            widget.destroy()

        self.system_frame.columnconfigure(0, weight=0)
        self.system_frame.columnconfigure(1, weight=1)
        self.system_frame.columnconfigure(2, weight=0)
        self.system_frame.columnconfigure(3, weight=1)

        label_font = (FONTS["normal_font"], self.base_font_size)
        pady_val = 1
        padx_val = (3, 5)

        ttk.Label(self.system_frame, text="Serial#:", font=label_font).grid(row=0, column=0, sticky=tk.W, padx=padx_val[0], pady=pady_val)
        ttk.Label(self.system_frame, text=self.serial_number, font=label_font).grid(row=0, column=1, sticky=tk.EW, padx=padx_val[1], pady=pady_val)

        ttk.Label(self.system_frame, text="CPU:", font=label_font).grid(row=0, column=2, sticky=tk.W, padx=padx_val[0], pady=pady_val)
        cpu_info = self.system_info['cpu']
        ttk.Label(self.system_frame, text=cpu_info, font=label_font, wraplength=200).grid(row=0, column=3, sticky=tk.EW, padx=padx_val[1], pady=pady_val)

        ttk.Label(self.system_frame, text="Memory:", font=label_font).grid(row=1, column=0, sticky=tk.W, padx=padx_val[0], pady=pady_val)
        ttk.Label(self.system_frame, text=self.system_info['memory'], font=label_font).grid(row=1, column=1, sticky=tk.EW, padx=padx_val[1], pady=pady_val)

        ttk.Label(self.system_frame, text="Graphics:", font=label_font).grid(row=1, column=2, sticky=tk.W, padx=padx_val[0], pady=pady_val)
        gpus = self.system_info['gpus']
        gpu_text = ", ".join(gpus) if isinstance(gpus, list) else str(gpus)
        ttk.Label(self.system_frame, text=gpu_text, font=label_font, wraplength=200).grid(row=1, column=3, sticky=tk.EW, padx=padx_val[1], pady=pady_val)

        ttk.Label(self.system_frame, text="Resolution:", font=label_font).grid(row=2, column=0, sticky=tk.W, padx=padx_val[0], pady=pady_val)
        ttk.Label(self.system_frame, text=self.system_info['screen_resolution'], font=label_font).grid(row=2, column=1, sticky=tk.EW, padx=padx_val[1], pady=pady_val)

        ttk.Label(self.system_frame, text="Battery:", font=label_font).grid(row=2, column=2, sticky=tk.W, padx=padx_val[0], pady=pady_val)
        batt = self.system_info.get('battery', {})
        if batt.get('present'):
            batt_lines = [f"{batt.get('percent', 'N/A')}% ({batt.get('status', 'N/A')})"]
            if batt.get('health'):
                batt_lines.append(f"H:{batt['health']}%")
            if batt.get('cycle_count'):
                batt_lines.append(f"C:{batt['cycle_count']}")
            ttk.Label(self.system_frame, text="; ".join(batt_lines), font=label_font, wraplength=200).grid(row=2, column=3, sticky=tk.EW, padx=padx_val[1], pady=pady_val)
        else:
            ttk.Label(self.system_frame, text="No battery", font=label_font).grid(row=2, column=3, sticky=tk.EW, padx=padx_val[1], pady=pady_val)

        disks_info = self.system_info.get('disks', [])
        disk_summaries = []
        for d in disks_info[:2]:
            if isinstance(d, dict):
                status = "OK" if d.get('smart_passed') else "BAD" if d.get('smart_passed') is not None else "?"
                size = f"{d.get('size_gb', '?')}G"
                disk_summaries.append(f"{d.get('path', '?').split('/')[-1]}: {d.get('model','?')} {size} [{status}]")
            else:
                disk_summaries.append(str(d))
        if len(disks_info) > 2:
            disk_summaries.append(f"+{len(disks_info)-2} more")
        
        ttk.Label(self.system_frame, text="Disks:", font=label_font).grid(row=3, column=0, sticky=tk.W, padx=padx_val[0], pady=pady_val)
        disk_display_text = "; ".join(disk_summaries) or "No disks"
        ttk.Label(self.system_frame, text=disk_display_text, font=label_font, wraplength=450).grid(row=3, column=1, columnspan=3, sticky=tk.EW, padx=padx_val[1], pady=pady_val)

    def _on_profile_loaded_action(self, profile: Profile | None):
        """Callback for TestControlFrame when a profile is loaded (or selection cleared)."""
        self.current_profile = profile
        if hasattr(self, 'result_manager'):
            self.result_manager.clear_results()
        self._check_run_conditions()

    def _on_edit_profiles_action(self):
        """Callback for TestControlFrame after ProfileEditor is closed."""
        self.log_panel.log("Profile editor closed, refreshing profile list in TestControlFrame.", "info")
        all_profiles = get_all_profiles()
        if hasattr(self, 'test_control_frame'):
            self.test_control_frame.update_profile_list(all_profiles)
        else:
            self.log_panel.log("Error: test_control_frame not found to update profile list.", "error")

    def log(self, message: str, level: str = "info"):
        """Convenience method for logging, usable by child windows."""
        if hasattr(self, 'log_panel') and self.log_panel:
            self.log_panel.log(message, level)
        else:
            print(f"NEXUS_APP_LOG [{level.upper()}]: {message}")

    def validate_inputs(self) -> bool:
        """Validate user inputs before running tests."""
        if not self.asset_input_frame.get_operator_id().strip():
            messagebox.showerror("Validation Error", "Please enter an Operator ID")
            return False
        if not self.asset_input_frame.get_asset_number().strip():
            messagebox.showerror("Validation Error", "Please enter an Asset Number")
            return False
        if not self.asset_input_frame.get_server_url().strip():
            messagebox.showerror("Validation Error", "Please enter a Server URL")
            return False
        if not self.current_profile or not self.current_profile.tests:
            messagebox.showerror("Validation Error", "Please select at least one test to run")
            return False
        return True

    def run_tests(self):
        """Run the selected tests based on the current profile."""
        self.result_manager.clear_results()
        self._check_run_conditions()
        if not self.current_profile or not self.current_profile.tests:
            messagebox.showwarning("No Profile Loaded", "Please load a profile with tests to run, or ensure the profile has tests defined.")
            return
        if hasattr(self, 'test_control_frame'):
            self.test_control_frame.set_run_button_state(False)
        self.test_orchestrator.execute_tests()
        summary_data_to_pass = self.test_orchestrator.get_summary_data()
        overall_status_from_orchestrator = summary_data_to_pass.get("overall_status", "UNKNOWN")
        final_log_level = "success" if overall_status_from_orchestrator == "PASS" else "error"
        self.log_panel.update_status("Tests completed")
        self.log_panel.log(f"NexusApp: All tests completed. Overall Status: {overall_status_from_orchestrator}", final_log_level)
        self._check_run_conditions()
        if hasattr(self, 'test_control_frame'):
            self.test_control_frame.set_run_button_state(True)
        self.log_panel.log("Displaying End Screen Summary.", "info")
        if not summary_data_to_pass:
            summary_data_to_pass = {
                "overall_status": "ERROR",
                "test_results": [],
                "drive_wipe_results": [],
                "errors": ["No test summary data was generated by orchestrator."]
            }
            self.log_panel.log("End screen summary data from orchestrator was missing or empty, using error default.", "warning")
        end_screen = EndScreenWindow(parent=self, summary_data=summary_data_to_pass,
                                     asset_number=self.asset_input_frame.get_asset_number(),
                                     base_font_size=self.base_font_size)
        self.wait_window(end_screen)

    def _check_run_conditions_callback(self, *args):
        """Callback for AssetInputFrame to trigger check."""
        self._check_run_conditions()

    def _check_run_conditions(self):
        """Enable or disable the 'Run Tests' and 'View Results' buttons based on conditions."""
        op_id = self.asset_input_frame.get_operator_id().strip() if hasattr(self, 'asset_input_frame') and self.asset_input_frame else ""
        asset_num = self.asset_input_frame.get_asset_number().strip() if hasattr(self, 'asset_input_frame') and self.asset_input_frame else ""
        run_enabled = bool(op_id and asset_num and self.current_profile)
        view_results_enabled = bool(hasattr(self, 'result_manager') and self.result_manager.test_results_available and asset_num)
        if hasattr(self, 'test_control_frame'):
            self.test_control_frame.set_run_button_state(run_enabled)
            self.test_control_frame.set_view_results_button_state(view_results_enabled)

    def view_results(self):
        self.log_panel.log("View Results button clicked.", "info")
        asset_num_val = self.asset_input_frame.get_asset_number().strip() if hasattr(self, 'asset_input_frame') and self.asset_input_frame else ""
        if not asset_num_val:
            self.log_panel.log("Asset number is missing. Cannot view results.", "warning")
            messagebox.showwarning("Missing Information", "Please enter an Asset Number to view results.")
            return
        results_dir = "results"
        self.log_panel.log(f"Checking for results in directory: {results_dir} for asset: {asset_num_val}", "info")
        if not os.path.exists(results_dir):
            self.log_panel.log(f"Results directory '{results_dir}' not found.", "warning")
            messagebox.showinfo("No Results", f"Results directory '{results_dir}' does not exist. No results to display.")
            return
        try:
            all_files = os.listdir(results_dir)
        except FileNotFoundError:
            self.log_panel.log(f"Results directory '{results_dir}' was not found during listing. It might have been deleted.", "error")
            messagebox.showerror("Error", f"Could not access results directory: {results_dir}. It may have been deleted.")
            return
        except Exception as e:
            self.log_panel.log(f"Error listing files in '{results_dir}': {e}", "error")
            messagebox.showerror("Error", f"An unexpected error occurred while listing files in {results_dir}.")
            return
        asset_file_prefix = f"nexus_result_{asset_num_val}_"
        result_files = [os.path.join(results_dir, filename) for filename in all_files if filename.startswith(asset_file_prefix) and filename.endswith(".json")]
        if not result_files:
            self.log_panel.log(f"No result files found for asset '{asset_num_val}' in '{results_dir}'.", "info")
            messagebox.showinfo("No Results Found", f"No result files found for asset number '{asset_num_val}'.")
            return
        self.log_panel.log(f"Found {len(result_files)} result file(s) for asset '{asset_num_val}':", "info")
        for file_path in result_files:
            self.log_panel.log(f" - {file_path}", "info")
        parsed_data = self.result_manager._load_and_parse_results(result_files)
        if not parsed_data:
            self.log_panel.log(f"No data could be parsed from the found result files for asset '{asset_num_val}'.", "warning")
            messagebox.showinfo("Parsing Incomplete", f"Found {len(result_files)} file(s), but none could be successfully parsed. Check logs.")
            return
        self.log_panel.log(f"Successfully parsed {len(parsed_data)} of {len(result_files)} result file(s) for asset '{asset_num_val}'.", "success")
        if parsed_data:
            first_result_summary = {
                "file": parsed_data[0]["file_path"],
                "test_name": parsed_data[0]["test_name"],
                "status": parsed_data[0]["status"]
            }
            self.log_panel.log(f"First parsed result summary: {json.dumps(first_result_summary, indent=2)}", "debug")
        results_window = ResultsDisplayWindow(self, parsed_data, asset_num_val, base_font_size=self.base_font_size)
        self.wait_window(results_window)

    def create_menu(self):
        menubar = tk.Menu(self)
        resolution_menu = tk.Menu(menubar, tearoff=0)
        configs = {
            "Low (1024x768)": ("1024x768", COLORS["low_res_font_size"]),
            "Medium (1440x900)": ("1440x900", int((COLORS["low_res_font_size"]+COLORS["high_res_font_size"])/2)),
            "High (1920x1080)": ("1920x1080", COLORS["high_res_font_size"]),
            "Fullscreen": ("fullscreen", COLORS["high_res_font_size"]),
        }
        for label, (res_str, font_size) in configs.items():
            resolution_menu.add_command(label=label, command=lambda r=res_str, fs=font_size: self.apply_resolution(r, fs))
        menubar.add_cascade(label="Resolution", menu=resolution_menu)
        self.config(menu=menubar)

    def update_widget_fonts(self, widget, new_font_size):
        """Recursively update the font for a widget and all its descendants."""
        try:
            widget.configure(font=(FONTS["normal_font"], new_font_size))
        except Exception:
            pass
        for child in widget.winfo_children():
            self.update_widget_fonts(child, new_font_size)

    def apply_resolution(self, res_str, _font_size):
        """
        Apply configuration from resolution_config.json:
        - Remain fullscreen
        - Scale fonts (using the provided _font_size if available)
        - Adjust control/log split
        """
        self.attributes('-fullscreen', True)
        if res_str != "fullscreen":
            try:
                width, height = map(int, res_str.split('x'))
                self.geometry(f"{width}x{height}")
                self.minsize(width, height)
                self.maxsize(width, height)
            except Exception:
                pass
        cfg = RESOLUTION_CONFIG.get("resolutions", {}).get(res_str) or {}
        # Use provided _font_size if available, otherwise use config, else current
        if _font_size:
            self.base_font_size = _font_size
        else:
            self.base_font_size = cfg.get("font_size", self.base_font_size)
        ratio = cfg.get("control_log_ratio", 0.5)
        self.setup_ui_styles()
        style = self.style
        style.configure(".", font=(FONTS["normal_font"], self.base_font_size))
        style.configure("TLabel", font=(FONTS["normal_font"], self.base_font_size))
        style.configure("TButton", font=(FONTS["normal_font"], self.base_font_size))
        self.update_idletasks()
        height = self.winfo_height()
        self.paned_window.sashpos(0, int(height * ratio))
        p_large = cfg.get("padding_large", SIZES.get("padding_large", 10))
        try:
            if hasattr(self, 'main_frame'):
                self.main_frame.pack_configure(padding=p_large)
            if hasattr(self, 'system_frame'):
                self.system_frame.pack_configure(pady=p_large // 2)
        except Exception:
            pass
        p_small = cfg.get("padding_small", SIZES.get("padding_small", 5))
        try:
            self.asset_input_frame.pack_configure(pady=p_small)
            self.test_control_frame.pack_configure(pady=p_small)
        except Exception:
            pass
        lp_height = cfg.get("log_panel_height")
        if lp_height is not None:
            try:
                self.log_panel.log_text_area.config(height=lp_height)
            except Exception:
                pass
        self.update_widget_fonts(self, self.base_font_size)


if __name__ == '__main__':
    pass
