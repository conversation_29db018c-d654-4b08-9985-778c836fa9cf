#!/usr/bin/env python3
"""
Arcoa Nexus End Screen Window

This module provides the EndScreenWindow class for displaying test and wipe summaries.
"""
import os
import tkinter as tk
from tkinter import ttk, scrolledtext

# Import from our modules
from agent.gui.theme import COLORS, FONTS, SIZES


class EndScreenWindow(tk.Toplevel):
    def __init__(self, parent, summary_data, asset_number, base_font_size):
        super().__init__(parent)
        self.parent = parent
        self.summary_data = summary_data # Store for potential display later
        self.asset_number = asset_number
        self.base_font_size = base_font_size

        self.title(f"Test & Wipe Summary for Asset: {asset_number}")
        self.geometry("800x600")
        self.configure(bg=COLORS["bg_dark"])

        # Countdown management
        self.countdown_seconds = 10
        self.countdown_job = None
        self.shutdown_button_text = tk.StringVar()
        self.shutdown_button_text.set("Shut Down")

        # Style configuration
        style = ttk.Style(self)
        style.configure("EndScreen.TLabel", background=COLORS["bg_dark"], foreground=COLORS["text_light"], font=(FONTS["normal_font"], self.base_font_size))
        style.configure("EndScreen.TButton", font=(FONTS["normal_font"], self.base_font_size), padding=5) # Ensure EndScreen.TButton is defined
        style.map("EndScreen.TButton",
                  background=[('active', COLORS["button_hover"]), ('!active', COLORS["button"]), ('disabled', COLORS["button_disabled"])],
                  foreground=[('active', COLORS["button_text"]), ('!active', COLORS["button_text"]), ('disabled', COLORS["button_text_disabled"])])
        style.configure("EndScreen.TFrame", background=COLORS["bg_dark"])

        self._setup_ui()

        # Modality - set up window relationships first
        self.protocol("WM_DELETE_WINDOW", self._on_close)
        self.transient(parent)

        # Ensure window is visible before grabbing focus
        self.update_idletasks()  # Process pending events
        self.deiconify()  # Make sure window is visible

        # Delay grab_set to ensure window is fully rendered
        self.after(10, self._delayed_grab_focus)

    def _delayed_grab_focus(self):
        """Safely grab focus after window is fully visible."""
        try:
            # Check if window is still valid and visible
            if self.winfo_exists() and self.winfo_viewable():
                self.grab_set()
                self.focus_set()
                self.lift()  # Bring to front
            else:
                # If window isn't ready, try again after a short delay
                self.after(10, self._delayed_grab_focus)
        except tk.TclError as e:
            print(f"Warning: Could not grab focus for EndScreenWindow: {e}")
            # Continue without modal behavior if grab fails

    def _setup_ui(self):
        # Main frame
        main_frame = ttk.Frame(self, style="EndScreen.TFrame", padding=SIZES["padding_large"])
        main_frame.pack(expand=True, fill=tk.BOTH)
        # Configure main_frame rows and columns for expansion
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1) # Content frame will expand
        main_frame.rowconfigure(2, weight=0) # Button frame will not expand

        # Asset Number Display
        asset_label = ttk.Label(main_frame, text=f"Summary for Asset: {self.asset_number}", style="EndScreen.TLabel", font=(FONTS["normal_font"], self.base_font_size + 4, "bold"))
        asset_label.grid(row=0, column=0, pady=(SIZES["padding_small"], SIZES["padding_medium"]), sticky="ew")

        # Content frame to hold all sections (overall status, tests, wipe, errors)
        content_sections_frame = ttk.Frame(main_frame, style="EndScreen.TFrame")
        content_sections_frame.grid(row=1, column=0, sticky="nsew", pady=(0, SIZES["padding_medium"]))
        content_sections_frame.columnconfigure(0, weight=1)
        # Allow rows within content_sections_frame to have different weights if needed, e.g., errors section to expand more
        content_sections_frame.rowconfigure(0, weight=0) # Overall status
        content_sections_frame.rowconfigure(1, weight=1) # Test Results (scrollable)
        content_sections_frame.rowconfigure(2, weight=1) # Drive Wipe Results (scrollable)
        content_sections_frame.rowconfigure(3, weight=1) # Errors (scrollable)


        # 1. Overall Status
        overall_status = self.summary_data.get("overall_status", "UNKNOWN")
        status_color_key = "success" if overall_status == "PASS" else "error" if overall_status == "FAIL" else "warning"
        status_color = COLORS.get(status_color_key, COLORS.get("text_light", "#FFFFFF"))

        overall_status_label = ttk.Label(
            content_sections_frame, 
            text=f"Overall Status: {overall_status}", 
            style="EndScreen.TLabel", 
            font=(FONTS["normal_font"], self.base_font_size + 6, "bold"), 
            foreground=status_color,
            anchor=tk.CENTER
        )
        overall_status_label.grid(row=0, column=0, pady=(SIZES["padding_small"], SIZES["padding_large"]), sticky="ew")

        # Define a common function to create scrollable frames for lists
        def create_scrollable_list_section(parent, title, items_data, item_formatter_func, no_items_message):
            section_lf = ttk.LabelFrame(parent, text=title, style="TLabelframe", padding=SIZES["padding_small"])
            section_lf.columnconfigure(0, weight=1)
            section_lf.rowconfigure(0, weight=1)

            if not items_data:
                no_data_label = ttk.Label(section_lf, text=no_items_message, style="EndScreen.TLabel", anchor=tk.CENTER)
                no_data_label.grid(row=0, column=0, padx=SIZES["padding_small"], pady=SIZES["padding_small"], sticky="ew")
                return section_lf # Return the labelframe directly

            canvas = tk.Canvas(section_lf, background=COLORS["bg_light"], highlightthickness=0)
            scrollbar = ttk.Scrollbar(section_lf, orient="vertical", command=canvas.yview)
            scrollable_frame = ttk.Frame(canvas, style="EndScreen.TFrame") # Use EndScreen.TFrame for bg

            scrollable_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"), width=e.width -4) # -4 for minor padding
            )
            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)

            canvas.grid(row=0, column=0, sticky="nsew")
            scrollbar.grid(row=0, column=1, sticky="ns")
            
            section_lf.grid_rowconfigure(0, weight=1)
            section_lf.grid_columnconfigure(0, weight=1)

            for item in items_data:
                item_formatter_func(scrollable_frame, item)
            
            return section_lf

        # 2. Test Results Section
        test_results_data = self.summary_data.get("test_results", [])
        def format_test_result_item(parent_frame, test_item):
            item_frame = ttk.Frame(parent_frame, style="EndScreen.TFrame", padding=(0, 2))
            item_frame.pack(fill=tk.X, expand=True)
            
            name = test_item.get("name", "N/A")
            status = str(test_item.get("status", "UNKNOWN")).upper()
            
            color_key = "success" if status == "PASS" else "error" if status == "FAIL" else "warning"
            color = COLORS.get(color_key, COLORS.get("text_light", "#FFFFFF"))

            ttk.Label(item_frame, text=name, style="EndScreen.TLabel", width=30, anchor=tk.W).pack(side=tk.LEFT, padx=(SIZES["padding_small"],0))
            ttk.Label(item_frame, text=status, style="EndScreen.TLabel", foreground=color, width=10, anchor=tk.W).pack(side=tk.LEFT, padx=SIZES["padding_small"])

        test_results_lf = create_scrollable_list_section(content_sections_frame, "Test Results", test_results_data, format_test_result_item, "No test results available.")
        test_results_lf.grid(row=1, column=0, sticky="nsew", pady=SIZES["padding_small"])


        # 3. Drive Wipe Results Section
        drive_wipe_data = self.summary_data.get("drive_wipe_results", [])
        def format_drive_wipe_item(parent_frame, wipe_item):
            item_frame = ttk.Frame(parent_frame, style="EndScreen.TFrame", padding=(0,2))
            item_frame.pack(fill=tk.X, expand=True)

            drive = wipe_item.get("drive", "N/A")
            status = str(wipe_item.get("status", "UNKNOWN")).upper()
            details = wipe_item.get("details", "")

            color_key = "success" if status == "PASS" or status == "SUCCESS" else "error" if status == "FAIL" else "warning"
            color = COLORS.get(color_key, COLORS.get("text_light", "#FFFFFF"))

            ttk.Label(item_frame, text=f"Drive: {drive}", style="EndScreen.TLabel", width=25, anchor=tk.W).pack(side=tk.LEFT, padx=(SIZES["padding_small"],0))
            ttk.Label(item_frame, text=f"Status: {status}", style="EndScreen.TLabel", foreground=color, width=15, anchor=tk.W).pack(side=tk.LEFT, padx=SIZES["padding_small"])
            if details: # Only show details if present
                 ttk.Label(item_frame, text=f"Details: {details}", style="EndScreen.TLabel", wraplength=300, anchor=tk.W).pack(side=tk.LEFT, padx=SIZES["padding_small"], fill=tk.X, expand=True)


        drive_wipe_lf = create_scrollable_list_section(content_sections_frame, "Drive Wipe Results", drive_wipe_data, format_drive_wipe_item, "No drive wipe operations performed.")
        drive_wipe_lf.grid(row=2, column=0, sticky="nsew", pady=SIZES["padding_small"])
        

        # 4. Errors Section
        errors_data = self.summary_data.get("errors", [])
        errors_lf = ttk.LabelFrame(content_sections_frame, text="Errors/Issues", style="TLabelframe", padding=SIZES["padding_small"])
        errors_lf.grid(row=3, column=0, sticky="nsew", pady=SIZES["padding_small"])
        errors_lf.columnconfigure(0, weight=1)
        errors_lf.rowconfigure(0, weight=1)

        error_text_widget = scrolledtext.ScrolledText(
            errors_lf, 
            wrap=tk.WORD, 
            font=(FONTS["log_font"], self.base_font_size - 2), # Slightly smaller for errors log
            background=COLORS["bg_light"], 
            foreground=COLORS["warning"], # Use warning color for error text
            relief="sunken",
            borderwidth=1,
            height=5 # Initial height, will expand if row has weight
        )
        error_text_widget.grid(row=0, column=0, sticky="nsew")

        if errors_data:
            for error_msg in errors_data:
                error_text_widget.insert(tk.END, f"- {error_msg}\n")
        else:
            error_text_widget.insert(tk.END, "No errors or issues reported.")
        error_text_widget.configure(state=tk.DISABLED)


        # Button Frame (already exists, just ensure it's correctly placed in the main_frame grid)
        button_frame = ttk.Frame(main_frame, style="EndScreen.TFrame", padding=SIZES["padding_medium"])
        button_frame.grid(row=2, column=0, sticky="ew", pady=(SIZES["padding_medium"], 0)) # Place below content_sections_frame
        
        center_button_frame = ttk.Frame(button_frame, style="EndScreen.TFrame")
        center_button_frame.pack(anchor=tk.CENTER) # Keep buttons centered

        self.shutdown_button = ttk.Button(center_button_frame, textvariable=self.shutdown_button_text, command=self._initiate_shutdown_sequence, style="EndScreen.TButton", width=22)
        self.shutdown_button.pack(side=tk.LEFT, padx=SIZES["padding_small"], pady=SIZES["padding_small"])
        
        self.close_button = ttk.Button(center_button_frame, text="Close", command=self._on_close, style="EndScreen.TButton", width=15)
        self.close_button.pack(side=tk.LEFT, padx=SIZES["padding_small"], pady=SIZES["padding_small"])


    def _initiate_shutdown_sequence(self):
        if hasattr(self.parent, 'log_panel') and hasattr(self.parent.log_panel, 'log'):
            self.parent.log_panel.log(f"Shutdown sequence initiated for asset {self.asset_number}.", "info")
        
        self.countdown_seconds = 10 # Reset countdown duration
        self.shutdown_button_text.set(f"Cancel Shutdown ({self.countdown_seconds}s)")
        self.shutdown_button.configure(command=self._cancel_shutdown)
        if hasattr(self, 'close_button'): # Ensure close_button exists
            self.close_button.configure(state=tk.DISABLED)
        self._update_countdown()

    def _update_countdown(self):
        self.countdown_seconds -= 1
        if self.countdown_seconds > 0:
            self.shutdown_button_text.set(f"Cancel Shutdown ({self.countdown_seconds}s)")
            self.countdown_job = self.after(1000, self._update_countdown)
        else:
            self.shutdown_button_text.set("Shutting down...")
            self.shutdown_button.configure(state=tk.DISABLED) # Disable button after countdown
            
            actual_command = "sudo shutdown now" 
            placeholder_command = f"echo 'PLACEHOLDER: System would execute: {actual_command}'"
            
            log_message = (f"Placeholder for shutdown: Executing '{placeholder_command}'. "
                           f"The actual command should be '{actual_command}' and requires root/admin privileges. "
                           "This is a simulation for non-production environments.")
            print(log_message) 
            if hasattr(self.parent, 'log_panel') and hasattr(self.parent.log_panel, 'log'):
                self.parent.log_panel.log(log_message, "warning")

            os.system(placeholder_command) 

            # In a real scenario, the system would shut down. For testing, we might close the app.
            # self.parent.destroy() # Closes the main app window
            # self._on_close() # Or just this window if preferred for testing flow

    def _cancel_shutdown(self):
        if self.countdown_job:
            self.after_cancel(self.countdown_job)
            self.countdown_job = None
        
        self.countdown_seconds = 10 
        self.shutdown_button_text.set("Shut Down")
        self.shutdown_button.configure(command=self._initiate_shutdown_sequence, state=tk.NORMAL) # Re-enable if disabled
        if hasattr(self, 'close_button'):
             self.close_button.configure(state=tk.NORMAL)
        if hasattr(self.parent, 'log_panel') and hasattr(self.parent.log_panel, 'log'):
            self.parent.log_panel.log(f"Shutdown sequence cancelled for asset {self.asset_number}.", "info")

    def _on_close(self):
        if self.countdown_job: 
            self.after_cancel(self.countdown_job)
            self.countdown_job = None
            if hasattr(self.parent, 'log_panel') and hasattr(self.parent.log_panel, 'log'):
                self.parent.log_panel.log(f"Shutdown countdown cancelled due to EndScreenWindow closure for asset {self.asset_number}.", "info")

        if hasattr(self.parent, 'log_panel') and hasattr(self.parent.log_panel, 'log'): 
            self.parent.log_panel.log(f"EndScreenWindow for asset {self.asset_number} closed.", "info")
        self.grab_release()
        self.destroy()

    # _on_shut_down is effectively replaced by the countdown sequence, 
    # but can be kept if a direct shutdown (no countdown) path is desired later.
    def _on_shut_down(self): 
        action_msg = f"Direct _on_shut_down called for asset {self.asset_number}. This path is currently bypassed by countdown sequence."
        print(action_msg) 
        if hasattr(self.parent, 'log_panel') and hasattr(self.parent.log_panel, 'log'):
            self.parent.log_panel.log(action_msg, "warning")

if __name__ == '__main__':
    # This block is for testing the EndScreenWindow independently.
    # You would need to mock the parent and summary_data.
    # For example:
    # root = tk.Tk()
    # root.withdraw() # Hide the root window

    # class MockParent:
    #     def __init__(self):
    #         self.log_panel = MockLogPanel()
    # class MockLogPanel:
    #     def log(self, message, level="info"):
    #         print(f"LOG [{level.upper()}]: {message}")

    # mock_summary_data = {
    #     "overall_status": "PASS",
    #     "test_results": [
    #         {"name": "CPU Test", "status": "PASS"},
    #         {"name": "RAM Test", "status": "PASS"},
    #     ],
    #     "drive_wipe_results": [
    #         {"drive": "/dev/sda", "status": "SUCCESS", "details": "Wipe completed."},
    #     ],
    #     "errors": ["Minor warning during boot."]
    # }
    # app = EndScreenWindow(MockParent(), mock_summary_data, "ASSET123", 10)
    # app.mainloop()
    pass
