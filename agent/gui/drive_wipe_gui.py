#!/usr/bin/env python3
"""agent.gui.drive_wipe_gui

Tkinter top-level window that lets the operator select drives and initiate a
secure wipe.  All heavy work is delegated to a *wipe_callback* that should run
in a background thread to keep the UI responsive.
"""
from __future__ import annotations

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
from typing import Callable, Dict, List, Any

from agent.gui.theme import COLORS  # Re-use existing colour scheme

# Type aliases for readability
ProgressCb = Callable[[str, float, float, str], None]
LogCb = Callable[[str, str], None]
ResultCb = Callable[[str, Dict[str, Any]], None]
WipeCallback = Callable[[List[str], str, ProgressCb, LogCb, ResultCb, Callable[[], None]], None]

WIPE_METHODS = {
    "NIST 800-88 Clear (1-pass zeros)": "zero_fill",
    "NIST 800-88 Clear (1-pass random)": "random_fill",
    "DoD 5220.22-M (3-pass)": "dodshort",
    "Gutmann (35-pass)": "gutmann",
    "ATA Secure Erase (SSD Only)": "ata_secure_erase",
    "NVMe Sanitize (NVMe Only)": "nvme_sanitize",
    "Blkdiscard (Quick SSD/NVMe)": "blkdiscard",
}


class DriveWipeWindow(tk.Toplevel):
    """Modal window for selecting drives and starting wipes."""

    def __init__(
        self,
        parent: tk.Tk,
        drives_info: List[Dict[str, Any]],
        wipe_callback: WipeCallback,
        log_callback_main_gui: LogCb,
    ) -> None:
        super().__init__(parent)
        self.title("Secure Drive Wipe")
        self.grab_set()  # Make modal

        self.drives_info = drives_info
        self.wipe_callback = wipe_callback
        self.log_main = log_callback_main_gui

        # Internal state
        self.drive_vars: Dict[str, tk.BooleanVar] = {}
        self._wipe_in_progress: bool = False
        self.wipe_results_collected: List[Dict[str, Any]] = [] # To store detailed results

        self._build_ui()

    # ------------------------------------------------------------------ UI
    def _build_ui(self) -> None:
        self.configure(bg=COLORS["bg_dark"])
        style = ttk.Style(self)
        style.theme_use("clam")
        style.configure("TLabel", background=COLORS["bg_dark"], foreground=COLORS["text_light"])
        style.configure("TCheckbutton", background=COLORS["bg_dark"], foreground=COLORS["text_light"])
        style.configure("TButton", background=COLORS["accent"], foreground=COLORS["text_light"], font=("Arial", 12, "bold"))
        style.map("TButton", background=[("active", COLORS["primary"]), ("disabled", COLORS["bg_dark"])])
        style.configure("Horizontal.TProgressbar", background=COLORS["accent"], troughcolor=COLORS["bg_light"])

        # Main layout frames
        top_frame = ttk.Frame(self, padding=10)
        top_frame.pack(fill=tk.BOTH, expand=True)

        # Drives list ----------------------------------------------------
        drives_labelframe = ttk.LabelFrame(top_frame, text="Detected Drives", padding=5)
        drives_labelframe.pack(fill=tk.X, pady=5)

        drives_canvas = tk.Canvas(drives_labelframe, height=150, bg=COLORS["bg_dark"], highlightthickness=0)
        vsb = ttk.Scrollbar(drives_labelframe, orient="vertical", command=drives_canvas.yview)
        drives_canvas.configure(yscrollcommand=vsb.set)
        vsb.pack(side=tk.RIGHT, fill=tk.Y)
        drives_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        inner = ttk.Frame(drives_canvas)
        drives_canvas.create_window((0, 0), window=inner, anchor="nw")

        def _configure_scroll(_e):
            drives_canvas.configure(scrollregion=drives_canvas.bbox("all"))
        inner.bind("<Configure>", _configure_scroll)

        for idx, d in enumerate(self.drives_info):
            var = tk.BooleanVar(value=False)
            self.drive_vars[d["path"]] = var
            cb = ttk.Checkbutton(inner, variable=var)
            cb.grid(row=idx, column=0, sticky=tk.W)

            # Build description text
            desc_parts = [d.get("path"), d.get("model", "Unknown")]
            size_gb = d.get("size_gb")
            if size_gb:
                desc_parts.append(f"{size_gb} GB")
            desc_parts.append(d.get("type", "?"))
            smart_ok = d.get("smart_passed")
            if smart_ok is not None:
                desc_parts.append("SMART OK" if smart_ok else "SMART BAD")
            label = ttk.Label(inner, text=" | ".join(desc_parts), font=("Arial", 10))
            label.grid(row=idx, column=1, sticky=tk.W, padx=2)

            # Disable if mounted
            if d.get("mountpoints"):
                cb.state(["disabled"])
            # Simple heuristic: likely system disk (first non-USB etc.)
            if idx == 0:
                cb.state(["disabled"])

        # Wipe method ----------------------------------------------------
        method_frame = ttk.LabelFrame(top_frame, text="Wipe Method", padding=5)
        method_frame.pack(fill=tk.X, pady=5)

        ttk.Label(method_frame, text="Method:").grid(row=0, column=0, sticky=tk.W)
        self.method_var = tk.StringVar()
        method_combo = ttk.Combobox(method_frame, textvariable=self.method_var, values=list(WIPE_METHODS.keys()), state="readonly")
        method_combo.grid(row=0, column=1, sticky=tk.W, padx=5)
        method_combo.current(0)

        # Warning --------------------------------------------------------
        warn_label = ttk.Label(top_frame, text="WARNING: This will irreversibly destroy data on the selected drives.", foreground=COLORS["error"], font=("Arial", 12, "bold"))
        warn_label.pack(pady=5)

        # Progress -------------------------------------------------------
        self.progress_label = ttk.Label(top_frame, text="Idle")
        self.progress_label.pack(anchor=tk.W)
        self.progress = ttk.Progressbar(top_frame, mode="determinate", length=400)
        self.progress.pack(fill=tk.X, padx=5, pady=2)

        # Log area -------------------------------------------------------
        log_frame = ttk.LabelFrame(top_frame, text="Wipe Log", padding=5)
        log_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8, wrap=tk.WORD, bg=COLORS["bg_dark"], fg=COLORS["text_light"], insertbackground=COLORS["accent"])
        self.log_text.pack(fill=tk.BOTH, expand=True)

        # Buttons --------------------------------------------------------
        btn_frame = ttk.Frame(top_frame)
        btn_frame.pack(fill=tk.X, pady=5)
        self.start_btn = ttk.Button(btn_frame, text="Start Wipe", command=self.on_start_wipe)
        self.start_btn.pack(side=tk.LEFT, padx=3)
        self.cancel_btn = ttk.Button(btn_frame, text="Cancel", command=self.on_cancel)
        self.cancel_btn.pack(side=tk.RIGHT, padx=3)

    # ------------------------------------------------------------------ Callbacks
    def on_start_wipe(self):
        selected = [path for path, var in self.drive_vars.items() if var.get()]
        if not selected:
            messagebox.showerror("No Drives Selected", "Select at least one drive to wipe.", parent=self)
            return

        method_human = self.method_var.get()
        method_key = WIPE_METHODS.get(method_human)
        if not method_key:
            messagebox.showerror("Invalid Method", "Please choose a wipe method.", parent=self)
            return

        # Basic compatibility checks (heuristic)
        incompatible = []
        if method_key == "ata_secure_erase":
            for d in self.drives_info:
                if d["path"] in selected and d.get("type") not in {"SSD", "HDD"}:
                    incompatible.append(d["path"])
        if method_key == "nvme_sanitize":
            for d in self.drives_info:
                if d["path"] in selected and d.get("type") != "NVMe":
                    incompatible.append(d["path"])
        if incompatible:
            messagebox.showerror("Incompatible Drives", f"Selected method not supported for: {', '.join(incompatible)}", parent=self)
            return

        # Confirmation dialog
        if not messagebox.askyesno("Confirm Wipe", f"You are about to wipe {len(selected)} drive(s). This CANNOT be undone. Continue?", parent=self):
            return

        # Disable UI
        self.start_btn.state(["disabled"])
        for cb in self.drive_vars.values():
            # We stored BooleanVar, not widget, so can't disable; not critical.
            pass
        self._wipe_in_progress = True

        # Kick off backend wipe process
        self.wipe_callback(selected, method_key, self.update_progress, self.log_to_gui, self.finalize_drive_wipe, self.all_wipes_completed)

    def update_progress(self, current_drive: str, overall: float, drive_progress: float, message: str):
        self.progress_label.config(text=f"{current_drive}: {message} ({drive_progress:.0f}% | overall {overall:.0f}%)")
        self.progress.config(value=overall)
        self.update_idletasks()

    def log_to_gui(self, message: str, level: str = "info"):
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)

    def finalize_drive_wipe(self, drive_path: str, result_data: Dict[str, Any]):
        self.log_main(f"Drive wipe done for {drive_path}: {result_data.get('status', '?')}", "info")
        # Store the detailed result for this specific drive
        self.wipe_results_collected.append(result_data)

    def all_wipes_completed(self):
        self._wipe_in_progress = False
        self.progress_label.config(text="All wipes completed")
        self.start_btn.state(["!disabled"])
        self.cancel_btn.config(text="Close")

    def on_cancel(self):
        if self._wipe_in_progress:
            if not messagebox.askyesno("Abort Wipe", "Wipes are still running. Abort?", parent=self):
                return
            # TODO: backend abort mechanism
        self.destroy()
