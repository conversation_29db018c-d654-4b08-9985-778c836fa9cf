#!/usr/bin/env python3
"""
Arcoa Nexus Results Display Window

This module provides the ResultsDisplayWindow class for showing detailed test results.
"""
import tkinter as tk
from tkinter import ttk
import datetime
from typing import List, Dict, Any

# Import from our modules
from agent.gui.theme import COLORS, FONTS


class ResultsDisplayWindow(tk.Toplevel):
    def __init__(self, parent, results_data: List[Dict[str, Any]], asset_number: str, base_font_size: int = 10):
        super().__init__(parent)
        self.parent = parent # Store parent for potential future use (e.g., logging)
        self.results_data = results_data
        self.asset_number = asset_number
        self.base_font_size = base_font_size # For consistent font sizing

        self.title(f"Test Results for Asset: {self.asset_number}")
        self.geometry("900x700") # Increased size slightly
        self.resizable(True, True)

        # Configure style for this Toplevel, if needed, or ensure it inherits parent styles
        # For simplicity, we'll rely on inherited styles or direct widget configuration

        self._setup_ui()

        # Modality
        self.protocol("WM_DELETE_WINDOW", self._on_close) # Handle window close button
        self.transient(parent) # Set to be on top of the parent window
        self.grab_set() # Make modal
        self.focus_set() # Grab focus

    def _on_close(self):
        self.grab_release() # Release grab before destroying
        self.destroy()

    def _format_timestamp(self, ts_value: Any) -> str:
        if not ts_value:
            return "N/A"
        try:
            # Try parsing as ISO 8601 string (common for started_at, finished_at)
            if isinstance(ts_value, str):
                dt_obj = datetime.datetime.fromisoformat(ts_value.replace("Z", "+00:00")) # Handle Z for UTC
                return dt_obj.strftime("%Y-%m-%d %H:%M:%S")
            # Try parsing as epoch timestamp (common for the main 'timestamp')
            elif isinstance(ts_value, (int, float)):
                return datetime.datetime.fromtimestamp(ts_value).strftime("%Y-%m-%d %H:%M:%S")
        except ValueError as e:
            if hasattr(self.parent, 'log_panel') and self.parent.log_panel: # Check if parent (NexusApp) has log_panel
                 self.parent.log_panel.log(f"Error formatting timestamp value {ts_value}: {e}", "warning")
            else: # Fallback if not, or if using the generic log method
                print(f"Warning: Error formatting timestamp value {ts_value}: {e}")
            return str(ts_value) # Return original value if parsing fails
        return str(ts_value)


    def _calculate_summary(self) -> Dict[str, Any]:
        summary = {
            "total_tests": len(self.results_data),
            "passed": 0,
            "failed": 0,
            "unknown": 0
        }
        for result in self.results_data:
            status = result.get("status", "unknown").lower()
            if status == "pass":
                summary["passed"] += 1
            elif status == "fail":
                summary["failed"] += 1
            else:
                summary["unknown"] += 1
        return summary

    def _setup_ui(self):
        self.configure(bg=COLORS["bg_dark"])
        main_frame = ttk.Frame(self, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        main_frame.columnconfigure(0, weight=1) # Make content column expandable
        main_frame.rowconfigure(1, weight=1) # Make results area expandable

        # Style definitions
        style = ttk.Style(self)
        style.configure("Results.TLabel", background=COLORS["bg_dark"], foreground=COLORS["text_light"], font=(FONTS["normal_font"], self.base_font_size))
        style.configure("ResultsTitle.TLabel", background=COLORS["bg_dark"], foreground=COLORS["primary"], font=(FONTS["normal_font"], self.base_font_size + 2, "bold"))
        style.configure("ResultsHeader.TLabel", background=COLORS["bg_dark"], foreground=COLORS["accent"], font=(FONTS["normal_font"], self.base_font_size, "bold"))
        # New style for list items, ensuring their background matches the canvas/inner frame
        style.configure("ResultsListItem.TLabel", background=COLORS.get("bg_light", "#444444"), foreground=COLORS["text_light"], font=(FONTS["normal_font"], self.base_font_size))
        style.configure("ResultsContainer.TFrame", background=COLORS.get("bg_light", "#444444"))

        # Summary Section (remains largely the same)
        summary_frame = ttk.Frame(main_frame, padding="5")
        summary_frame.grid(row=0, column=0, sticky=tk.EW, pady=(0, 10))
        summary_data = self._calculate_summary()
        ttk.Label(summary_frame, text=f"Asset Number: {self.asset_number}", style="ResultsTitle.TLabel").grid(row=0, column=0, sticky=tk.W, columnspan=2)
        summary_text_frame = ttk.Frame(summary_frame)
        summary_text_frame.grid(row=1, column=0, sticky=tk.W, pady=(5,0))
        ttk.Label(summary_text_frame, text="Total Tests:", style="ResultsHeader.TLabel").grid(row=0, column=0, sticky=tk.W, padx=(0,5))
        ttk.Label(summary_text_frame, text=str(summary_data["total_tests"]), style="Results.TLabel").grid(row=0, column=1, sticky=tk.W)
        ttk.Label(summary_text_frame, text="Passed:", style="ResultsHeader.TLabel").grid(row=1, column=0, sticky=tk.W, padx=(0,5))
        ttk.Label(summary_text_frame, text=str(summary_data["passed"]), style="Results.TLabel", foreground=COLORS.get("success", "green")).grid(row=1, column=1, sticky=tk.W)
        ttk.Label(summary_text_frame, text="Failed:", style="ResultsHeader.TLabel").grid(row=2, column=0, sticky=tk.W, padx=(0,5))
        ttk.Label(summary_text_frame, text=str(summary_data["failed"]), style="Results.TLabel", foreground=COLORS.get("error", "red")).grid(row=2, column=1, sticky=tk.W)
        ttk.Label(summary_text_frame, text="Unknown/Other:", style="ResultsHeader.TLabel").grid(row=3, column=0, sticky=tk.W, padx=(0,5))
        ttk.Label(summary_text_frame, text=str(summary_data["unknown"]), style="Results.TLabel", foreground=COLORS.get("warning", "orange")).grid(row=3, column=1, sticky=tk.W)

        # Results Display Area - New Scrollable List
        results_area_frame = ttk.Frame(main_frame) # Container for canvas and scrollbar
        results_area_frame.grid(row=1, column=0, sticky=tk.NSEW, pady=(0, 10))
        results_area_frame.rowconfigure(0, weight=1)
        results_area_frame.columnconfigure(0, weight=1)

        results_canvas = tk.Canvas(results_area_frame, bg=COLORS.get("bg_light", "#444444"), highlightthickness=0)
        scrollbar_y = ttk.Scrollbar(results_area_frame, orient=tk.VERTICAL, command=results_canvas.yview)
        results_canvas.configure(yscrollcommand=scrollbar_y.set)

        scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)
        results_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        scrollable_content_frame = ttk.Frame(results_canvas, style="ResultsContainer.TFrame")
        results_canvas.create_window((0, 0), window=scrollable_content_frame, anchor=tk.NW, tags="scrollable_content_frame")

        def _configure_scroll_region(event):
            results_canvas.configure(scrollregion=results_canvas.bbox(tk.ALL))
        scrollable_content_frame.bind("<Configure>", _configure_scroll_region)
        
        # Populate Results List
        for result_item in self.results_data:
            test_name = result_item.get('test_name', 'N/A')
            status_raw = result_item.get('status', 'unknown')
            status_display = status_raw.upper()

            status_color_key = status_raw.lower()
            if status_color_key == "pass":
                color = COLORS.get("success", "green")
            elif status_color_key == "fail":
                color = COLORS.get("error", "red")
            else:
                color = COLORS.get("warning", "orange")

            item_frame = ttk.Frame(scrollable_content_frame, style="ResultsContainer.TFrame") # Match background
            item_frame.pack(fill=tk.X, expand=True, pady=1, padx=5)

            name_label = ttk.Label(item_frame, text=f"{test_name}:", style="ResultsListItem.TLabel", anchor=tk.W)
            name_label.pack(side=tk.LEFT)

            status_label = ttk.Label(item_frame, text=status_display, style="ResultsListItem.TLabel", foreground=color, anchor=tk.W)
            status_label.pack(side=tk.LEFT, padx=5)

        # Close Button
        close_button_frame = ttk.Frame(main_frame) # Frame for button centering/styling
        close_button_frame.grid(row=2, column=0, sticky=tk.E, pady=(5,0))
        close_button = ttk.Button(
            close_button_frame,
            text="Close",
            command=self._on_close,
            style="TButton"
        )
        close_button.pack()

if __name__ == '__main__':
    # This block is for testing the ResultsDisplayWindow independently.
    # You would need to mock the parent and results_data.
    # For example:
    # root = tk.Tk()
    # root.withdraw() # Hide the root window

    # class MockParent:
    #     def __init__(self):
    #         self.log_panel = self.MockLogPanel() # Nested class for simplicity
    #     class MockLogPanel:
    #         def log(self, message, level="info"):
    #             print(f"LOG [{level.upper()}]: {message}")

    # mock_results = [
    #     {"test_name": "CPU Stability", "status": "PASS", "timestamp": "2023-01-01T12:00:00Z"},
    #     {"test_name": "Memory Check", "status": "FAIL", "details": "Error at 0xDEADBEEF", "timestamp": 1672574400},
    # ]
    # app = ResultsDisplayWindow(MockParent(), mock_results, "ASSETXYZ", 10)
    # app.mainloop()
    pass
