#!/usr/bin/env python3
"""
Arcoa Nexus Device Condition Window

This module provides a GUI for setting and recording the physical condition of devices being tested.
"""
import tkinter as tk
from tkinter import ttk, messagebox
import json
import os
from typing import Dict, Any, Optional, Callable

# Import colors from theme
from agent.gui.theme import COLORS


class DeviceConditionWindow(tk.Toplevel):
    """Window for setting device physical condition parameters."""

    def __init__(self, parent, callback: Optional[Callable] = None,
                 current_conditions: Optional[Dict[str, Any]] = None):
        """
        Initialize the device condition window.

        Args:
            parent: Parent window
            callback: Optional callback function to call when conditions are saved
            current_conditions: Optional dictionary of current condition values
        """
        super().__init__(parent)
        self.parent = parent
        self.callback = callback

        # Set window properties
        self.title("Device Condition Assessment")
        self.geometry("600x550")
        self.minsize(600, 550)

        # Make it modal
        self.transient(parent)
        self.grab_set()

        # Configure style
        self.configure(bg=COLORS["bg_dark"])

        # Initialize condition variables
        self.conditions = {
            "case_condition": tk.StringVar(value="Good"),
            "screen_condition": tk.StringVar(value="Good"),
            "missing_items": tk.StringVar(value="N/A"),
            "keyboard_condition": tk.StringVar(value="Good"),
            "touchpad_condition": tk.StringVar(value="Good"),
            "usb_ports": tk.StringVar(value="Good"),
            "grade": tk.StringVar(value="A")
        }

        # Set current conditions if provided
        if current_conditions:
            for key, value in current_conditions.items():
                if key in self.conditions:
                    self.conditions[key].set(value)

        # Create UI
        self.create_ui()

        # Focus the window
        self.focus_set()

    def create_ui(self):
        """Create the user interface."""
        # Main frame with padding
        main_frame = ttk.Frame(self, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Title
        title_label = ttk.Label(
            main_frame,
            text="Device Condition Assessment",
            font=("Arial", 16, "bold"),
            foreground=COLORS["accent"]
        )
        title_label.pack(pady=(0, 10))

        # Create a frame for all condition fields
        conditions_frame = ttk.Frame(main_frame)
        conditions_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # Define condition options
        condition_options = {
            "case_condition": {
                "label": "Case Condition:",
                "options": ["Good", "Light Scratches", "Heavy Scratches", "Light Dents", "Heavy Dents", "Cracks"]
            },
            "screen_condition": {
                "label": "Screen Condition:",
                "options": ["Good", "Light Scratches", "Heavy Scratches", "Cracked", "Pressure Marks", "Faded Areas", "Dead Pixels"]
            },
            "missing_items": {
                "label": "Missing Items:",
                "options": ["N/A", "Point Stick", "Footpads", "Keyboard Keys", "Bottom Screws"]
            },
            "keyboard_condition": {
                "label": "Keyboard Condition:",
                "options": ["Good", "Light Wear", "Heavy Wear"]
            },
            "touchpad_condition": {
                "label": "Touchpad Condition:",
                "options": ["Good", "Light Wear", "Heavy Wear"]
            },
            "usb_ports": {
                "label": "USB Ports:",
                "options": ["Good", "Damaged"]
            },
            "grade": {
                "label": "Grade:",
                "options": ["A", "B", "C", "Damaged"]
            }
        }

        # Create fields for each condition
        row = 0
        for key, config in condition_options.items():
            # Label
            ttk.Label(
                conditions_frame,
                text=config["label"],
                font=("Arial", 12),
                foreground=COLORS["text_light"]
            ).grid(row=row, column=0, sticky=tk.W, padx=5, pady=10)

            if key == "missing_items":
                self.missing_items_listbox = tk.Listbox(conditions_frame, selectmode=tk.MULTIPLE, height=len(config["options"]), font=("Arial", 12))
                for option in config["options"]:
                    self.missing_items_listbox.insert(tk.END, option)
                # Pre-select previously set values if available (assuming comma separated values)
                current_val = self.conditions[key].get()
                if current_val != "N/A":
                    for idx, option in enumerate(config["options"]):
                        if option in [s.strip() for s in current_val.split(",")]:
                            self.missing_items_listbox.selection_set(idx)
                self.missing_items_listbox.grid(row=row, column=1, sticky=tk.W, padx=5, pady=10)
            else:
                dropdown = ttk.Combobox(
                    conditions_frame,
                    textvariable=self.conditions[key],
                    values=config["options"],
                    width=20,
                    font=("Arial", 12),
                    state="readonly"
                )
                dropdown.grid(row=row, column=1, sticky=tk.W, padx=5, pady=10)

            row += 1

        # Add a separator before buttons
        ttk.Separator(main_frame, orient="horizontal").pack(fill=tk.X, pady=10)

        # Buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=5)

        # Save button
        save_button = ttk.Button(
            buttons_frame,
            text="Save Conditions",
            command=self.save_conditions,
            style="TButton"
        )
        save_button.pack(side=tk.RIGHT, padx=5)

        # Cancel button
        cancel_button = ttk.Button(
            buttons_frame,
            text="Cancel",
            command=self.on_cancel,
            style="TButton"
        )
        cancel_button.pack(side=tk.RIGHT, padx=5)

    def save_conditions(self):
        """Save the device conditions and close the window."""
        # Get all condition values
        conditions_data = {key: var.get() for key, var in self.conditions.items()}
        # Override missing_items with multiple selection from listbox if available
        if hasattr(self, "missing_items_listbox"):
            selections = self.missing_items_listbox.curselection()
            missing = ",".join([self.missing_items_listbox.get(i) for i in selections]) if selections else "N/A"
            conditions_data["missing_items"] = missing

        # Call the callback with the conditions data
        if self.callback:
            self.callback(conditions_data)

        # Close the window
        self.grab_release()
        self.destroy()

    def on_cancel(self):
        """Cancel and close the window."""
        self.grab_release()
        self.destroy()


def get_conditions_file_path(asset_number: str = None) -> str:
    """
    Get the path to the conditions file for the specified asset.

    Args:
        asset_number: The asset number to use in the filename

    Returns:
        Path to the conditions file
    """
    # Create a results directory if it doesn't exist
    results_dir = "results"
    if not os.path.exists(results_dir):
        os.makedirs(results_dir)

    # Use asset number in the filename if provided
    if asset_number and asset_number.strip():
        # Sanitize asset number for use as filename
        safe_asset = "".join(c if c.isalnum() else "_" for c in asset_number)
        return os.path.join(results_dir, f"device_conditions_{safe_asset}.json")
    else:
        # Fallback to generic filename if no asset number
        return os.path.join(results_dir, "device_conditions.json")


def save_device_conditions(conditions: Dict[str, Any], asset_number: str = None) -> bool:
    """
    Save device conditions to a file.

    Args:
        conditions: Dictionary of device conditions
        asset_number: The asset number to use in the filename

    Returns:
        True if successful, False otherwise
    """
    try:
        file_path = get_conditions_file_path(asset_number)
        with open(file_path, 'w') as f:
            json.dump(conditions, f, indent=2)
        return True
    except Exception as e:
        print(f"Error saving device conditions: {e}")
        return False


def load_device_conditions(asset_number: str = None) -> Dict[str, Any]:
    """
    Load device conditions from a file.

    Args:
        asset_number: The asset number to use in the filename

    Returns:
        Dictionary of device conditions or empty dict if not found
    """
    try:
        file_path = get_conditions_file_path(asset_number)
        if os.path.exists(file_path):
            with open(file_path, 'r') as f:
                return json.load(f)
    except Exception as e:
        print(f"Error loading device conditions: {e}")

    # Return default conditions if file doesn't exist or there's an error
    return {}


if __name__ == "__main__":
    # For testing
    root = tk.Tk()
    root.withdraw()

    def on_save(conditions):
        print("Saved conditions:", conditions)
        save_device_conditions(conditions)

    current = load_device_conditions()
    window = DeviceConditionWindow(root, callback=on_save, current_conditions=current)
    root.mainloop()
