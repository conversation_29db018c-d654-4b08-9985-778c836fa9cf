# Arcoa Nexus

A modular diagnostics & secure‐erase platform for computer recycling environments.

* **Project goal:** Provide a PXE/USB/ISO bootable test agent and a REST API server that records hardware diagnostics and drive-wipe events, integrating later with Makor ERP.
* **Status:** Bootstrap skeleton – server + agent.

---

## Quick Start

### 1. Install Python 3.11+

```bash
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
```

### 2. Run the API server

```bash
uvicorn server.main:app --reload --port 8000
```

Browse to `http://localhost:8000/docs` to see the interactive Swagger UI.

### 3. Run the agent (same machine for demo)

```bash
python agent/agent.py --server http://localhost:8000
```

You should see a JSON response confirming the test result was received.

---

## Repository Layout

```
├── agent/          # Client-side test agent that runs in live OS
│   └── agent.py
├── server/         # FastAPI application
│   └── main.py
├── requirements.txt
└── README.md
```

---

## Next Steps

1. Design DB schema (PostgreSQL) for assets, test suites, wipe jobs.
2. Implement persistent storage layer (SQLModel / SQLAlchemy).
3. Build additional test plug-ins (CPU stress, RAM, kb input, etc.).
4. Package minimal Debian Live image embedding `agent/` and auto-start script.
