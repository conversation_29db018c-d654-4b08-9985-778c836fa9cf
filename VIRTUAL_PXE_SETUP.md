# Virtual PXE Development Environment for Arcoa Nexus

This guide outlines how to create an isolated virtual environment for PXE boot development without interfering with your home network.

## Overview

We'll use VirtualBox to create:
1. A Host-Only Network for our PXE infrastructure
2. A PXE Server VM running Alpine Linux
3. A Client VM to test PXE booting

This setup keeps all PXE-related traffic contained in a virtual network, preventing any interference with your home network.

## Step 1: Install VirtualBox

1. Download and install VirtualBox from [virtualbox.org](https://www.virtualbox.org/wiki/Downloads)
2. Install the VirtualBox Extension Pack for additional features

## Step 2: Create a Host-Only Network

1. Open VirtualBox and go to **File > Host Network Manager**
2. Click **Create** to make a new host-only network
3. Configure the network:
   - IPv4 Address: `************`
   - IPv4 Network Mask: `*************`
4. In the DHCP Server tab, **uncheck** "Enable Server" (we'll run our own DHCP server)
5. Click **Apply** and close the manager

## Step 3: Set Up PXE Server VM

1. Download Alpine Linux ISO from [alpinelinux.org](https://alpinelinux.org/downloads/)
2. In VirtualBox, click **New**
3. Create a VM with these settings:
   - Name: `ArcoaPXEServer`
   - Type: Linux
   - Version: Other Linux (64-bit)
   - Memory: 1024 MB
   - Create a virtual hard disk (20 GB)
4. Once created, select the VM and click **Settings**
5. Under **Network**:
   - Adapter 1: Host-only Adapter, Name: the network you created
   - Adapter 2: NAT (for internet access)
6. Under **Storage**, attach the Alpine Linux ISO to the virtual optical drive
7. Start the VM and install Alpine Linux

## Step 4: Install PXE Components on Server VM

1. After Alpine boots, log in and run `setup-alpine`
2. When asked about network interfaces:
   - Configure `eth0` (Host-only adapter) with static IP: `*************`
   - Configure `eth1` (NAT adapter) with DHCP
3. Install required packages:

```bash
# Update package lists
apk update

# Install DHCP, TFTP, and HTTP servers
apk add dnsmasq lighttpd

# Install utilities
apk add syslinux python3 py3-pip curl
```

## Step 5: Configure DHCP and TFTP (using dnsmasq)

```bash
# Create directories
mkdir -p /var/lib/tftpboot/pxelinux.cfg

# Copy needed syslinux files
cp /usr/share/syslinux/pxelinux.0 /var/lib/tftpboot/
cp /usr/share/syslinux/ldlinux.c32 /var/lib/tftpboot/
cp /usr/share/syslinux/libcom32.c32 /var/lib/tftpboot/
cp /usr/share/syslinux/libutil.c32 /var/lib/tftpboot/
cp /usr/share/syslinux/vesamenu.c32 /var/lib/tftpboot/
cp /usr/share/syslinux/menu.c32 /var/lib/tftpboot/
```

## Step 6: Configure dnsmasq for DHCP and TFTP

```bash
# Create a dnsmasq configuration file
cat > /etc/dnsmasq.conf << 'EOF'
# Don't function as a DNS server
port=0

# Listen on the host-only interface
interface=eth0

# Enable the DHCP server
dhcp-range=*************0,**************,12h

# Set gateway and DNS server (optional in this setup)
dhcp-option=3,*************
dhcp-option=6,*************

# Enable the built-in TFTP server
enable-tftp
tftp-root=/var/lib/tftpboot

# Boot BIOS clients
dhcp-boot=pxelinux.0

# Log DHCP messages
log-dhcp
EOF
```

## Step 7: Configure HTTP Server

```bash
# Configure lighttpd
cat > /etc/lighttpd/lighttpd.conf << 'EOF'
server.document-root = "/var/www/localhost/htdocs"
server.port = 80
server.username = "lighttpd"
server.groupname = "lighttpd"
server.bind = "*************"
server.errorlog = "/var/log/lighttpd/error.log"
index-file.names = ( "index.html" )
mimetype.assign = (
  ".html" => "text/html",
  ".txt" => "text/plain",
  ".jpg" => "image/jpeg",
  ".png" => "image/png",
  "" => "application/octet-stream"
)
EOF

# Create web directory
mkdir -p /var/www/localhost/htdocs/arcoanexus
```

## Step 8: Prepare PXE Boot Files

```bash
# Create a basic PXE menu
cat > /var/lib/tftpboot/pxelinux.cfg/default << 'EOF'
DEFAULT menu.c32
PROMPT 0
TIMEOUT 300
ONTIMEOUT arcoanexus

MENU TITLE Arcoa Nexus PXE Boot Menu

LABEL arcoanexus
  MENU LABEL Arcoa Nexus Diagnostics
  KERNEL vmlinuz-alpine
  APPEND initrd=initramfs-alpine alpine_repo=http://dl-cdn.alpinelinux.org/alpine/latest-stable/main modloop=http://*************/arcoanexus/modloop quiet
EOF
```

## Step 9: Get Alpine Boot Files

```bash
# Download the Alpine kernel and initramfs
cd /var/lib/tftpboot
curl -O https://dl-cdn.alpinelinux.org/alpine/latest-stable/releases/x86_64/netboot/vmlinuz-alpine
curl -O https://dl-cdn.alpinelinux.org/alpine/latest-stable/releases/x86_64/netboot/initramfs-alpine

# Download modloop
cd /var/www/localhost/htdocs/arcoanexus
curl -O https://dl-cdn.alpinelinux.org/alpine/latest-stable/releases/x86_64/netboot/modloop-lts
mv modloop-lts modloop
```

## Step 10: Start Services

```bash
# Start and enable services
rc-service dnsmasq start
rc-service lighttpd start
rc-update add dnsmasq default
rc-update add lighttpd default
```

## Step 11: Create Test Client VM

1. In VirtualBox, click **New**
2. Create a VM with these settings:
   - Name: `ArcoaTestClient`
   - Type: Linux
   - Version: Other Linux (64-bit)
   - Memory: 2048 MB
   - Create a virtual hard disk (20 GB)
3. Once created, select the VM and click **Settings**
4. Under **Network**:
   - Adapter 1: Host-only Adapter, Name: the network you created
5. Under **System** > **Boot Order**:
   - Enable Network boot and move it to the top of the list

## Step 12: Test PXE Boot

1. Start the `ArcoaTestClient` VM
2. It should boot via PXE and show the Arcoa Nexus PXE Boot Menu
3. Choose "Arcoa Nexus Diagnostics" and it should boot into Alpine Linux

## Step 13: Customizing for Arcoa Nexus

After testing basic PXE boot, we'll need to:

1. Create a customized Alpine Linux initramfs with Arcoa Nexus pre-installed
2. Copy your Arcoa Nexus application code to the PXE server
3. Configure the Alpine boot parameters to auto-start your application

## Troubleshooting

- **VM won't boot from network**: Make sure Network boot is enabled and first in boot order
- **DHCP not working**: Check `dnsmasq` logs with `cat /var/log/messages | grep dnsmasq`
- **TFTP failures**: Check permissions on `/var/lib/tftpboot` directory
- **HTTP server issues**: Check `/var/log/lighttpd/error.log`

## Next Steps

Once the basic PXE environment is working, we'll:

1. Create a custom Alpine Linux build that includes Python, Tkinter and other dependencies
2. Integrate the Arcoa Nexus application into the boot image
3. Configure automatic login and application startup
4. Test with real hardware once ready
