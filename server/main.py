"""FastAPI entry-point for Arcoa Nexus server."""
from datetime import datetime
from typing import List, Optional

from fastapi import <PERSON><PERSON><PERSON>
from pydantic import BaseModel

app = FastAPI(title="Arcoa Nexus API", version="0.1.0")

# NOTE: In-memory list for demo; replace with <PERSON> later.
TEST_RESULTS: List[dict] = []


class TestResult(BaseModel):
    asset_serial: str
    asset_number: str
    operator_id: str
    test_name: str
    status: str  # "pass" | "fail"
    data: Optional[dict] = None
    started_at: datetime
    finished_at: datetime


@app.get("/")
async def root() -> dict[str, str]:
    """Health check."""
    return {"msg": "Arcoa Nexus API running"}


@app.post("/result")
async def collect_result(result: TestResult):
    """Receive a single test result from an agent."""
    TEST_RESULTS.append(result.model_dump())
    return {"received": True, "total": len(TEST_RESULTS)}


@app.get("/results")
async def list_results():
    """Return all collected results (demo only)."""
    return TEST_RESULTS


@app.get("/result")
async def redirect_to_results():
    """Redirect to the plural endpoint for consistency."""
    return {"msg": "Use /results endpoint instead", "results": TEST_RESULTS}


@app.get("/ui")
async def web_ui():
    """Simple HTML UI for viewing results."""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Arcoa Nexus - Dashboard</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
            .container { max-width: 1200px; margin: 0 auto; }
            h1 { color: #0066cc; }
            .result-card { background: white; border-radius: 5px; padding: 15px; margin-bottom: 15px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
            .pass { border-left: 5px solid #4CAF50; }
            .fail { border-left: 5px solid #F44336; }
            .details { margin-top: 10px; font-family: monospace; white-space: pre-wrap; background: #f8f8f8; padding: 10px; border-radius: 3px; }
            .refresh { padding: 10px 15px; background: #0066cc; color: white; border: none; border-radius: 4px; cursor: pointer; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>Arcoa Nexus Dashboard</h1>
            <button class="refresh" onclick="location.reload()">Refresh</button>
            <p>Total assets tested: <span id="total-count">0</span></p>
            <div id="results"></div>
        </div>
        
        <script>
            // Fetch results from the API
            fetch('/results')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('total-count').textContent = data.length;
                    const resultsContainer = document.getElementById('results');
                    
                    data.forEach(result => {
                        const card = document.createElement('div');
                        card.className = `result-card ${result.status}`;
                        
                        const header = document.createElement('h3');
                        header.textContent = `Asset #${result.asset_number} - ${result.test_name} [${result.status.toUpperCase()}]`;
                        
                        const meta = document.createElement('p');
                        meta.innerHTML = `<strong>Operator:</strong> ${result.operator_id} | <strong>System SN:</strong> ${result.asset_serial} | <strong>Test ran:</strong> ${new Date(result.started_at).toLocaleString()}`;
                        
                        const details = document.createElement('div');
                        details.className = 'details';
                        details.textContent = JSON.stringify(result.data, null, 2);
                        
                        card.appendChild(header);
                        card.appendChild(meta);
                        card.appendChild(details);
                        resultsContainer.appendChild(card);
                    });
                })
                .catch(error => {
                    console.error('Error fetching results:', error);
                    document.getElementById('results').innerHTML = '<p>Error loading results</p>';
                });
        </script>
    </body>
    </html>
    """
    return html_content
