#!/usr/bin/env python3
"""
Run Visual CPU and RAM Tests

This script provides a simple way to run the visual CPU and RAM tests
without going through the main application.
"""
import argparse
import tkinter as tk
from tkinter import ttk

from agent.tests.visual_cpu_test import run_visual_cpu_test
from agent.tests.visual_ram_test import run_visual_ram_test
from agent.tests.test_config import get_setting


class TestLauncher(tk.Tk):
    """Simple launcher for visual tests."""
    
    def __init__(self):
        super().__init__()
        
        self.title("Visual Test Launcher")
        self.geometry("500x300")
        
        # Set up the UI
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the user interface."""
        # Main frame
        main_frame = ttk.Frame(self, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = ttk.Label(
            main_frame, 
            text="Visual Test Launcher", 
            font=("Arial", 16, "bold")
        )
        title_label.pack(pady=(0, 20))
        
        # Test selection
        test_frame = ttk.LabelFrame(main_frame, text="Select Test", padding="10")
        test_frame.pack(fill=tk.X, pady=10)
        
        # Test type
        self.test_type = tk.StringVar(value="cpu")
        ttk.Radiobutton(
            test_frame, 
            text="CPU Test", 
            variable=self.test_type, 
            value="cpu"
        ).pack(anchor=tk.W, pady=5)
        
        ttk.Radiobutton(
            test_frame, 
            text="RAM Test", 
            variable=self.test_type, 
            value="ram"
        ).pack(anchor=tk.W, pady=5)
        
        # Test parameters
        param_frame = ttk.LabelFrame(main_frame, text="Test Parameters", padding="10")
        param_frame.pack(fill=tk.X, pady=10)
        
        # Duration
        ttk.Label(param_frame, text="Duration (seconds):").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        self.duration = tk.IntVar(value=get_setting("visual_cpu_duration"))
        ttk.Spinbox(
            param_frame, 
            from_=10, 
            to=300, 
            increment=10, 
            textvariable=self.duration, 
            width=5
        ).grid(row=0, column=1, padx=5, pady=5, sticky=tk.W)
        
        # RAM size (only relevant for RAM test)
        ttk.Label(param_frame, text="RAM Size (MB):").grid(row=1, column=0, padx=5, pady=5, sticky=tk.W)
        self.ram_size = tk.IntVar(value=get_setting("visual_ram_size_mb"))
        ttk.Spinbox(
            param_frame, 
            from_=256, 
            to=4096, 
            increment=256, 
            textvariable=self.ram_size, 
            width=5
        ).grid(row=1, column=1, padx=5, pady=5, sticky=tk.W)
        
        # Run button
        run_button = ttk.Button(
            main_frame, 
            text="Run Test", 
            command=self.run_test
        )
        run_button.pack(pady=20)
        
        # Log
        self.log_var = tk.StringVar(value="Select a test and click 'Run Test'")
        ttk.Label(
            main_frame, 
            textvariable=self.log_var, 
            font=("Arial", 10)
        ).pack(pady=5)
    
    def log(self, message, level="info"):
        """Log a message."""
        self.log_var.set(message)
        print(f"[{level.upper()}] {message}")
    
    def run_test(self):
        """Run the selected test."""
        test_type = self.test_type.get()
        duration = self.duration.get()
        
        if test_type == "cpu":
            self.log("Starting Visual CPU Test...")
            run_visual_cpu_test(self, duration_seconds=duration, log_callback=self.log)
        else:  # RAM test
            ram_size = self.ram_size.get()
            self.log("Starting Visual RAM Test...")
            run_visual_ram_test(
                self, 
                test_size_mb=ram_size, 
                duration_seconds=duration, 
                log_callback=self.log
            )


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Run visual CPU and RAM tests")
    parser.add_argument("--cpu", action="store_true", help="Run CPU test directly")
    parser.add_argument("--ram", action="store_true", help="Run RAM test directly")
    parser.add_argument("--duration", type=int, help="Test duration in seconds")
    parser.add_argument("--ram-size", type=int, help="RAM test size in MB")
    
    args = parser.parse_args()
    
    # Create a simple root window if running tests directly
    if args.cpu or args.ram:
        root = tk.Tk()
        root.withdraw()  # Hide the root window
        
        if args.cpu:
            duration = args.duration or get_setting("visual_cpu_duration")
            print(f"Running Visual CPU Test (duration: {duration}s)")
            run_visual_cpu_test(root, duration_seconds=duration)
        
        if args.ram:
            duration = args.duration or get_setting("visual_ram_duration")
            ram_size = args.ram_size or get_setting("visual_ram_size_mb")
            print(f"Running Visual RAM Test (size: {ram_size}MB, duration: {duration}s)")
            run_visual_ram_test(root, test_size_mb=ram_size, duration_seconds=duration)
        
        return
    
    # Otherwise, show the launcher
    app = TestLauncher()
    app.mainloop()


if __name__ == "__main__":
    main()
