fastapi==0.110.3
uvicorn[standard]==0.29.0
pydantic==2.7.1
httpx==0.27.0
rich==13.7.1
psutil==5.9.8
numpy==1.26.3
py-cpuinfo==9.0.0

# --- GUI ---
tkinter  # usually provided by python3-tk package on Linux

# --- Hardware/Diagnostics ---
# psutil (already included)
# py-cpuinfo (already included)

# --- Testing ---
pytest>=7.0.0

# --- Optional: For Windows support ---
# wmi;pywin32

# --- CLI tools required on Linux (install via apt/yum, not pip):
# lsblk, smartctl, nwipe, hdparm, nvme-cli, blkdiscard, blockdev, dd, sha256sum
# Example install on Ubuntu/Debian:
# sudo apt-get install lsblk smartmontools nwipe hdparm nvme-cli util-linux coreutils
