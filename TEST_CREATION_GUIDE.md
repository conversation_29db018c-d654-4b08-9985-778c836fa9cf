### Guide: Adding a New Test to Arcoa Nexus

This guide outlines the process for creating a new test and ensuring it's discoverable by the test framework, usable in profiles, and visible in the Profile Editor.

**Core Idea:** The system uses a decorator (`@test`) to identify and gather metadata about test functions. These functions must reside in Python files within the `agent/tests/` directory.

### Step 1: Create the Test File
1.  **Location**: Create a new Python file in the `c:\DEV\ArcoaEPS\agent\tests\` directory.
2.  **Naming**: Choose a descriptive name for your file, typically ending with `_test.py`.

### Step 2: Define the Test Function
1.  Define a Python function for your test logic.
2.  Function name should be descriptive.
3.  Function can accept `**kwargs` (e.g., for `log_callback`, `parent_window`).

### Step 3: Use the `@test` Decorator
1.  **Import**: `from agent.tests.test_framework import test, TestCategory, TestSeverity, TestStatus`.
2.  **Apply**: Place `@test(...)` above your test function.
    *   `category` (Required): `TestCategory` enum member. Add to enum in `test_framework.py` if new category needed.
    *   `severity` (Optional): `TestSeverity` enum member. Defaults to `MEDIUM`.
    *   `description` (Optional): String description. Defaults to docstring.
    *   `name` (Optional): User-friendly name for UI. Defaults to function name.
    *   `timeout` (Optional): Integer in seconds.

### Step 4: Implement Test Logic
Write the code that performs the test.

### Step 5: Return Test Results
Supported formats (framework wraps into `TestResult`):
1.  Tuple `(TestStatus, "notes string")`
2.  Boolean (`True` for PASS, `False` for FAIL)
3.  Dictionary `{"status": TestStatus.PASS, "notes": "...", ...}`

### Step 6: Logging (Optional)
Use `log_callback = kwargs.get('log_callback')`. Call `log_callback("message", "level")` with levels: "info", "warning", "error", "debug", "success".

### Step 7: GUI Interaction (If Applicable)
1.  Accept `parent_window = kwargs.get('parent_window')`.
2.  Manage GUI lifecycle within the test function.

### Step 8: Verification
1.  Run application.
2.  Open Profile Editor: New test should be in "All Tests" and its category tab.
3.  Add to a profile, save, and run the profile.
4.  Check logs and pass/fail status.

### Updating Enums (If Necessary)
If a new `TestCategory` or `TestSeverity` is needed, edit the enums in `c:\DEV\ArcoaEPS\agent\tests\test_framework.py`.
